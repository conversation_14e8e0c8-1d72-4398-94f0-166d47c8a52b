import React from 'react';
import { useQuery } from 'react-query';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Tooltip } from 'primereact/tooltip';
import { TfiTrash } from 'react-icons/tfi';
import { FaRegEye } from 'react-icons/fa';
import { FiEdit } from 'react-icons/fi';
import { Link } from 'react-router-dom';
import Container from '@components/Container';
import axiosInstance from "../../../config/Axios"; // Ensure the correct path
import { loadStripe } from '@stripe/stripe-js';

// Load Stripe key
const stripePromise = loadStripe('pk_test_51Kg7JrJPZyIMVMipnIL0gpi2E3jvHhQ4h6UDReB84sBKDnuC5dATko0CkagEPc639o7dbfiY9Ub7zmG1g3M9eq0p009uekzZe3');

// Fetch packages function
const fetchPackages = async () => {
  try {
    // Get the token from localStorage
    const token = localStorage.getItem('token');
    
    if (!token) {
      console.error("Token not found in localStorage");
      return; // Stop execution if token is not found
    }

    // Send request to the server
    const response = await axiosInstance.get('packages/show-sold-packages', {
      headers: {
        Authorization: `Bearer ${token}`,  // Add token to the header
      }
    });

    // Log response to ensure data is received
    console.log('Response data:', response.data);
    return response.data;

  } catch (error) {
    // Handle errors during the request
    console.error("Error fetching packages:", error);

    if (error.response) {
      // If there is a response from the server
      console.error('Response error:', error.response.data);
      console.error('Status code:', error.response.status);
    } else if (error.request) {
      // If no response received
      console.error('No response received:', error.request);
    } else {
      // Other errors
      console.error('Error message:', error.message);
    }
  }
};

const PackagesDataTable = () => {
  const { data: packages, isLoading, isError, error } = useQuery('packages', fetchPackages);

  if (isLoading) return <p className="text-center">Loading...</p>;
  if (isError) return <p className="text-center text-red-500">Error: {error.message}</p>;

  // Handle purchase of a package


  // Action icons for each package
  const actionsBodyTemplate = (rowData) => {
    return (
      <div className="flex justify-around space-x-3">
        {/* View details
        <Tooltip target=".view-icon" content="View Details" position="top" />
        <Link to={`/package-details/${rowData.id}`} className="view-icon">
          <FaRegEye className="text-blue-600 hover:text-blue-800 transition duration-200" size={20} />
        </Link> */}
  
        {/* Edit package */}
        <Tooltip target=".edit-icon" content="Edit" position="top" />
        <Link to={`/edit-package/${rowData.id}`} className="edit-icon">
          <FiEdit className="text-yellow-500 hover:text-yellow-700 transition duration-200" size={20} />
        </Link>
  
        {/* Buy or Upgrade package */}
        {/* <Tooltip target=".buy-icon" content={rowData.is_purchased ? "Upgrade Package" : "Buy Package"} position="top" />
        <button
          className={`main-btn text-md shadow-md px-5 py-2 rounded-lg transition duration-200 ${rowData.is_purchased ? "bg-orange-600 hover:bg-orange-700" : "bg-green-600 hover:bg-green-700"} text-white`}
          onClick={() => handleBuy(rowData.id)}
        >
          {rowData.is_purchased ? "Upgrade" : "Buy"}
        </button> */}
  
        {/* Delete package */}
        <Tooltip target=".delete-icon" content="Delete" position="top" />
        <button
          className="delete-icon text-red-500 hover:text-red-700 transition duration-200"
          onClick={() => handleDelete(rowData.id)}
        >
          <TfiTrash className="text-red-500" size={20} />
        </button>
      </div>
    );
  };

  return (
<Container className="w-full flex flex-col h-full">
  {/* Professional Header Section */}
  <div className="w-full mb-8">
    <div className="w-full">
      {/* Header Background with Light Gradient */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 dark:from-gray-800 dark:via-gray-700 dark:to-gray-600 shadow-xl border border-gray-200 dark:border-gray-600 sold-packages-header-container">
        {/* Animated Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundSize: '60px 60px'
          }}></div>
        </div>
        
        {/* Floating Elements */}
        <div className="absolute top-4 right-4 w-20 h-20 bg-blue-200/30 rounded-full blur-xl"></div>
        <div className="absolute bottom-4 left-4 w-16 h-16 bg-purple-200/30 rounded-full blur-xl"></div>
            
        {/* Header Content */}
        <div className="relative z-10 p-4">
          {/* Title Section */}
          <div className="flex flex-row items-center mb-4">
            <div className="mb-0">
              <div className="flex items-center gap-3">
                <div className="relative">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center shadow-lg">
                    <svg className="text-white text-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                    </svg>
                  </div>
                  <div className="absolute -top-1 -right-1 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center border-2 border-white shadow-md">
                    <span className="text-white text-xs font-bold">
                      {(packages?.length || 0).toLocaleString()}
                    </span>
                  </div>
                </div>
                <div>
                  <h1 className="text-xl lg:text-2xl font-bold text-gray-900 dark:text-gray-100 mb-0.5">
                    Sold Packages Management
                  </h1>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    View and manage all sold packages and their details
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Stats Section */}
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-3 items-end">
            <div className="lg:col-span-3">
              <div className="grid grid-cols-3 gap-3">
                {/* Total Packages */}
                <div className="bg-white/80 dark:bg-gray-700/80 backdrop-blur-sm rounded-xl p-2 border border-gray-200 dark:border-gray-600 shadow-sm hover:shadow-lg transition-all duration-300 group">
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-2 mb-1">
                      <div className="relative group">
                        <svg className="text-blue-600 group-hover:text-blue-700 transition-colors duration-300 group-hover:scale-110 group-hover:rotate-6" width={24} height={24} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                        </svg>
                        <div className="absolute inset-0 bg-blue-100 dark:bg-blue-900 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                      </div>
                      <div className="text-2xl font-bold text-gray-900 dark:text-gray-100 group-hover:text-blue-700 transition-colors duration-300">
                        {(packages?.length || 0).toLocaleString()}
                      </div>
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 group-hover:text-blue-600 transition-colors duration-300">
                      Total Packages
                    </div>
                  </div>
                </div>

                {/* Total Revenue */}
                <div className="bg-white/80 dark:bg-gray-700/80 backdrop-blur-sm rounded-xl p-2 border border-gray-200 dark:border-gray-600 shadow-sm hover:shadow-lg transition-all duration-300 group">
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-2 mb-1">
                      <div className="relative group">
                        <svg className="text-green-600 group-hover:text-green-700 transition-colors duration-300 group-hover:scale-110 group-hover:rotate-6" width={24} height={24} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                        <div className="absolute inset-0 bg-green-100 dark:bg-green-900 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                      </div>
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400 group-hover:text-green-700 transition-colors duration-300">
                        {(packages?.reduce((sum, pkg) => sum + (parseFloat(pkg.total_price) || 0), 0) || 0).toLocaleString()}
                      </div>
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 group-hover:text-green-600 transition-colors duration-300">Total Revenue</div>
                  </div>
                </div>

                {/* Average Price */}
                <div className="bg-white/80 dark:bg-gray-700/80 backdrop-blur-sm rounded-xl p-2 border border-gray-200 dark:border-gray-600 shadow-sm hover:shadow-lg transition-all duration-300 group">
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-2 mb-1">
                      <div className="relative group">
                        <svg className="text-purple-600 group-hover:text-purple-700 transition-colors duration-300 group-hover:scale-110 group-hover:rotate-6" width={24} height={24} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                        <div className="absolute inset-0 bg-purple-100 dark:bg-purple-900 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                      </div>
                      <div className="text-2xl font-bold text-purple-600 dark:text-purple-400 group-hover:text-purple-700 transition-colors duration-300">
                        {packages?.length > 0 ? Math.round(packages.reduce((sum, pkg) => sum + (parseFloat(pkg.total_price) || 0), 0) / packages.length).toLocaleString() : 0}
                      </div>
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 group-hover:text-purple-600 transition-colors duration-300">Avg Price</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mt-6 flex flex-wrap gap-3">
            <div className="px-4 py-2 bg-blue-100/80 dark:bg-blue-900/80 backdrop-blur-sm border border-blue-200 dark:border-blue-700 rounded-lg text-blue-700 dark:text-blue-300 text-sm font-medium shadow-sm flex items-center gap-2 group">
              <div className="relative">
                <svg className="text-blue-600 group-hover:text-blue-700 transition-colors duration-300 group-hover:scale-110 group-hover:-rotate-12" width={16} height={16} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <div className="absolute inset-0 bg-blue-200 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
              </div>
              Showing {(packages?.length || 0).toLocaleString()} sold packages
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  {/* Display table data */}
  <div className="flex-1">
    <DataTable 
      value={packages} 
      paginator 
      rows={10}
      className="mt-4 shadow-lg rounded-lg bg-white h-full"
      responsiveLayout="scroll"
      lazy
      filterDisplay="row"
      breakpoint="960px"
      dataKey="id"
      rowsPerPageOptions={[5, 25, 50, 100]}
      paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} products"
      scrollable
      scrollHeight="100%"
    >
    <Column field="name" header="Package Name" className="text-left" />
    <Column field="total_price" header="Total Price" className="text-left" />
    <Column field="card_limit" header="Card Limit" className="text-center" />
    <Column 
      field="card_types.name" 
      header="Card Type" 
      className="text-center"
      body={(rowData) => {
        return rowData.card_types.length > 1 
          ? rowData.card_types.map(card => card.name).join(' + ') 
          : rowData.card_types[0].name;
      }}
    />
        <Column 
      field="purchased_by_manager_name" 
      header="Purchased By" 
      className="text-center"
    />

  </DataTable>
  </div>
</Container>

  );
};

export default PackagesDataTable;
