import { useState, useRef, useEffect } from "react";
import { Dialog } from "primereact/dialog";
import { FaGift, FaPhone, FaEnvelope, FaBuilding, FaUser, FaMoneyBillWave, FaMoneyCheckAlt, FaPercent, FaRegClock, FaCalendarTimes, FaCalendarCheck, FaDollarSign, FaUserTie, FaLayerGroup, FaHashtag, FaBoxOpen, FaCheckCircle, FaTimesCircle, FaFileExcel, FaPrint, FaMapMarkerAlt, FaGlobe, FaMap } from "react-icons/fa";
import { HiOutlineCreditCard } from "react-icons/hi";
import { BsBank2 } from "react-icons/bs";
import { TfiTrash } from "react-icons/tfi";
import { FiEdit } from "react-icons/fi";
import { Skeleton } from "primereact/skeleton";
import { Toast } from "primereact/toast";
import axios from "axios";
import * as XLSX from 'xlsx';
import { useLayout } from "../../../../contexts/LayoutContext";
import PropTypes from 'prop-types';

// Add CSS animations for pulsing effect
const addPulseStyles = () => {
  if (typeof document !== 'undefined') {
    const styleId = 'pulse-animations';
    if (!document.getElementById(styleId)) {
      const style = document.createElement('style');
      style.id = styleId;
      style.textContent = `
        @keyframes pulse-core {
          0% {
            transform: translate(-50%, -50%) scale(1);
            opacity: 1;
            box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
          }
          50% {
            transform: translate(-50%, -50%) scale(1.15);
            opacity: 0.9;
            box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
          }
          100% {
            transform: translate(-50%, -50%) scale(1);
            opacity: 1;
            box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
          }
        }
        
        @keyframes pulse-ring-1 {
          0% {
            transform: translate(-50%, -50%) scale(0.8);
            opacity: 0.8;
          }
          25% {
            transform: translate(-50%, -50%) scale(1.2);
            opacity: 0.6;
          }
          50% {
            transform: translate(-50%, -50%) scale(1.6);
            opacity: 0.4;
          }
          75% {
            transform: translate(-50%, -50%) scale(2);
            opacity: 0.2;
          }
          100% {
            transform: translate(-50%, -50%) scale(2.4);
            opacity: 0;
          }
        }
        
        @keyframes pulse-ring-2 {
          0% {
            transform: translate(-50%, -50%) scale(0.6);
            opacity: 0.6;
          }
          25% {
            transform: translate(-50%, -50%) scale(1.4);
            opacity: 0.4;
          }
          50% {
            transform: translate(-50%, -50%) scale(2.2);
            opacity: 0.2;
          }
          75% {
            transform: translate(-50%, -50%) scale(3);
            opacity: 0.1;
          }
          100% {
            transform: translate(-50%, -50%) scale(3.8);
            opacity: 0;
          }
        }
        
        @keyframes pulse-ring-3 {
          0% {
            transform: translate(-50%, -50%) scale(0.4);
            opacity: 0.4;
          }
          50% {
            transform: translate(-50%, -50%) scale(2.8);
            opacity: 0.1;
          }
          100% {
            transform: translate(-50%, -50%) scale(4.2);
            opacity: 0;
          }
        }
        
        .pulse-core {
          animation: pulse-core 3s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;
        }
        
        .pulse-ring-1 {
          animation: pulse-ring-1 2.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;
        }
        
        .pulse-ring-2 {
          animation: pulse-ring-2 2.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite 0.8s;
        }
        
        .pulse-ring-3 {
          animation: pulse-ring-3 2.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite 1.6s;
        }
      `;
      document.head.appendChild(style);
    }
  }
};

// Google Maps Component with Pulsing Red Marker
const GoogleMapPreview = ({ latitude, longitude, address, companyName }) => {
  const mapRef = useRef(null);
  const [mapLoaded, setMapLoaded] = useState(false);

  useEffect(() => {
    // Add pulse styles
    addPulseStyles();
    
    // Load Google Maps script
    const loadGoogleMaps = () => {
      if (window.google && window.google.maps) {
        setMapLoaded(true);
        return;
      }

      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${import.meta.env.VITE_GOOGLE_MAPS_API_KEY || 'AIzaSyB41DRUbKWJHPxaFjMAwdrzWzbVKartNGg'}&libraries=places`;
      script.async = true;
      script.defer = true;
      script.onload = () => setMapLoaded(true);
      document.head.appendChild(script);
    };

    loadGoogleMaps();
  }, []);

  useEffect(() => {
    if (!mapLoaded || !mapRef.current || !latitude || !longitude) return;

    const position = { lat: Number(latitude), lng: Number(longitude) };
    
    // Enhanced map styles for better clarity and professionalism
    const mapStyles = [
      {
        featureType: "poi",
        elementType: "labels",
        stylers: [{ visibility: "off" }]
      },
      {
        featureType: "transit",
        elementType: "labels",
        stylers: [{ visibility: "off" }]
      },
      {
        featureType: "landscape",
        elementType: "geometry",
        stylers: [{ color: "#f5f5f5" }]
      },
      {
        featureType: "road",
        elementType: "geometry",
        stylers: [{ color: "#ffffff" }]
      },
      {
        featureType: "road",
        elementType: "geometry.stroke",
        stylers: [{ color: "#e0e0e0" }]
      },
      {
        featureType: "water",
        elementType: "geometry",
        stylers: [{ color: "#e3f2fd" }]
      },
      {
        featureType: "administrative",
        elementType: "geometry.stroke",
        stylers: [{ color: "#c9c9c9" }]
      }
    ];
    
    const map = new window.google.maps.Map(mapRef.current, {
      center: position,
      zoom: 16,
      mapTypeId: window.google.maps.MapTypeId.ROADMAP,
      mapTypeControl: false,
      streetViewControl: false,
      fullscreenControl: false,
      zoomControl: true,
      zoomControlOptions: {
        position: window.google.maps.ControlPosition.TOP_RIGHT,
        style: window.google.maps.ZoomControlStyle.SMALL
      },
      styles: mapStyles,
      backgroundColor: "#f8fafc"
    });

    // Create professional circular marker with gradient and shadow
    const pulsingRedMarker = {
      url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iMTYiIGZpbGw9InVybCgjZ3JhZGllbnQpIi8+CjxjaXJjbGUgY3g9IjE2IiBjeT0iMTYiIHI9IjEyIiBmaWxsPSJ3aGl0ZSIvPgo8Y2lyY2xlIGN4PSIxNiIgY3k9IjE2IiByPSI4IiBmaWxsPSJ1cmwoI2dyYWRpZW50SW5uZXIpIi8+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWRpZW50IiB4MT0iMCIgeTE9IjAiIHgyPSIxIiB5Mj0iMSI+CjxzdG9wIG9mZnNldD0iMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNmNDQzMzY7c3RvcC1vcGFjaXR5OjEiIC8+CjxzdG9wIG9mZnNldD0iMTAwJSIgc3R5bGU9InN0b3AtY29sb3I6I2RjMjYyNjtzdG9wLW9wYWNpdHk6MSIgLz4KPC9saW5lYXJHcmFkaWVudD4KPGxpbmVhckdyYWRpZW50IGlkPSJncmFkaWVudElubmVyIiB4MT0iMCIgeTE9IjAiIHgyPSIxIiB5Mj0iMSI+CjxzdG9wIG9mZnNldD0iMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNmNDQzMzY7c3RvcC1vcGFjaXR5OjEiIC8+CjxzdG9wIG9mZnNldD0iMTAwJSIgc3R5bGU9InN0b3AtY29sb3I6I2VlMjIyMjtzdG9wLW9wYWNpdHk6MSIgLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K',
      scaledSize: new window.google.maps.Size(32, 32),
      anchor: new window.google.maps.Point(16, 16)
    };

    // Add marker with pulsing animation
    const marker = new window.google.maps.Marker({
      position: position,
      map: map,
      title: companyName || 'Company Location',
      icon: pulsingRedMarker,
      animation: window.google.maps.Animation.DROP,
      zIndex: 9999, // Much higher z-index to ensure it appears above everything
      optimized: false
    });

    // Ensure marker is visible
    marker.setMap(map);

    // Create pulsing overlay that follows the marker
    class PulsingOverlay extends window.google.maps.OverlayView {
      constructor(position, map) {
        super();
        this.position = position;
        this.setMap(map);
      }

      onAdd() {
        this.div = document.createElement('div');
        this.div.style.position = 'absolute';
        this.div.style.pointerEvents = 'none';
        this.div.style.zIndex = '100'; // Much lower z-index so marker appears above
        
        // Create pulsing elements - pulse rings first (back), then core (front)
        this.div.innerHTML = `
          <div class="pulse-ring-3" style="
            position: absolute;
            width: 32px;
            height: 32px;
            background: radial-gradient(circle, rgba(239, 68, 68, 0.3) 0%, transparent 60%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 0 25px rgba(239, 68, 68, 0.2);
            z-index: 1;
          "></div>
          <div class="pulse-ring-2" style="
            position: absolute;
            width: 40px;
            height: 40px;
            background: radial-gradient(circle, rgba(239, 68, 68, 0.4) 0%, transparent 65%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
            z-index: 2;
          "></div>
          <div class="pulse-ring-1" style="
            position: absolute;
            width: 48px;
            height: 48px;
            background: radial-gradient(circle, rgba(239, 68, 68, 0.5) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 0 15px rgba(239, 68, 68, 0.4);
            z-index: 3;
          "></div>
          <div class="pulse-core" style="
            position: absolute;
            width: 32px;
            height: 32px;
            background: radial-gradient(circle, #ef4444 0%, #dc2626 100%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 4px 16px rgba(239, 68, 68, 0.5), 0 0 0 3px rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(255, 255, 255, 1);
            z-index: 10;
          "></div>
        `;
        
        const panes = this.getPanes();
        panes.overlayImage.appendChild(this.div);
      }

      draw() {
        if (!this.div) return;
        
        const projection = this.getProjection();
        if (!projection) return;
        
        const point = projection.fromLatLngToDivPixel(this.position);
        if (point) {
          this.div.style.left = point.x + 'px';
          this.div.style.top = point.y + 'px';
        }
      }

      remove() {
        if (this.div && this.div.parentNode) {
          this.div.parentNode.removeChild(this.div);
        }
        this.setMap(null);
      }
    }

    // Add pulsing overlay
    const pulsingOverlay = new PulsingOverlay(position, map);

    // Update overlay position when map moves
    const updateOverlayPosition = () => {
      pulsingOverlay.draw();
    };

    map.addListener('zoom_changed', updateOverlayPosition);
    map.addListener('center_changed', updateOverlayPosition);
    map.addListener('bounds_changed', updateOverlayPosition);

    // Enhanced info window with better styling
    const infoWindow = new window.google.maps.InfoWindow({
      content: `
        <div style="padding: 18px; max-width: 320px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: linear-gradient(165deg, #ffffff 0%, #f8fafc 30%, #f1f5f9 70%, #e2e8f0 100%); border-radius: 16px; box-shadow: 0 20px 40px rgba(0,0,0,0.15), 0 8px 16px rgba(0,0,0,0.1), 0 0 0 1px rgba(255,255,255,0.9); border: 2px solid rgba(255,255,255,0.8); position: relative; overflow: hidden;">
          <!-- Background Pattern -->
          <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 20% 80%, rgba(239, 68, 68, 0.03) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%); pointer-events: none;"></div>
          
          <!-- Header -->
          <div style="display: flex; align-items: center; margin-bottom: 14px; position: relative; z-index: 1;">
            <div style="width: 14px; height: 14px; background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); border-radius: 50%; margin-right: 10px; animation: pulse-core 3s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite; box-shadow: 0 0 20px rgba(239, 68, 68, 0.7), 0 0 0 2px rgba(255,255,255,0.9); border: 2px solid rgba(255,255,255,1);"></div>
            <div style="font-weight: 800; color: #1f2937; font-size: 14px; text-shadow: 0 1px 3px rgba(0,0,0,0.1); letter-spacing: -0.02em; line-height: 1.2;">${companyName || 'Company Location'}</div>
          </div>
          
          ${address ? `
            <!-- Address Section -->
            <div style="font-size: 12px; color: #374151; margin-bottom: 12px; line-height: 1.5; padding: 12px 16px; background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.15) 100%); border-radius: 12px; border-left: 4px solid #3b82f6; box-shadow: inset 0 2px 4px rgba(59, 130, 246, 0.1), 0 2px 6px rgba(59, 130, 246, 0.1); position: relative; z-index: 1;">
              <span style="font-size: 14px; margin-right: 8px; filter: drop-shadow(0 1px 3px rgba(0,0,0,0.15));">📍</span> 
              <span style="font-weight: 600; color: #1e40af;">${address}</span>
            </div>
          ` : ''}
          
          <!-- Coordinates Section -->
          <div style="font-size: 11px; color: #6b7280; font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace; background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); padding: 10px 14px; border-radius: 12px; display: inline-block; border: 2px solid #e2e8f0; box-shadow: inset 0 2px 4px rgba(0,0,0,0.05), 0 2px 6px rgba(0,0,0,0.1), 0 0 0 1px rgba(255,255,255,0.8); position: relative; z-index: 1;">
            <div style="font-weight: 700; color: #374151; margin-bottom: 4px; font-size: 10px; text-transform: uppercase; letter-spacing: 0.06em; text-shadow: 0 1px 2px rgba(0,0,0,0.1);">📍 Coordinates</div>
            <div style="font-weight: 600; color: #1f2937; font-size: 12px; background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%); padding: 6px 10px; border-radius: 6px; border: 1px solid #d1d5db; box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);">
              ${Number(latitude).toFixed(6)}°, ${Number(longitude).toFixed(6)}°
            </div>
          </div>
          
          <!-- Decorative Elements -->
          <div style="position: absolute; top: 8px; right: 8px; width: 30px; height: 30px; background: radial-gradient(circle, rgba(239, 68, 68, 0.1) 0%, transparent 70%); border-radius: 50%; pointer-events: none;"></div>
          <div style="position: absolute; bottom: 8px; left: 8px; width: 24px; height: 24px; background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%); border-radius: 50%; pointer-events: none;"></div>
        </div>
      `,
      maxWidth: 340,
      pixelOffset: new window.google.maps.Size(0, -30),
      backgroundColor: 'transparent',
      border: 'none'
    });

    // Show info window on marker click only
    marker.addListener('click', () => {
      if (infoWindow.getMap()) {
        infoWindow.close();
      } else {
        infoWindow.open(map, marker);
      }
    });

    // Close info window when clicking on map
    map.addListener('click', () => {
      infoWindow.close();
    });

    // Remove bouncing animation - keep only the visual pulse effects
    // const startPulsing = () => {
    //   marker.setAnimation(window.google.maps.Animation.BOUNCE);
    //   setTimeout(() => {
    //     marker.setAnimation(null);
    //   }, 1000);
    // };

    // Start pulsing animation periodically
    // const pulseInterval = setInterval(() => {
    //   startPulsing();
    // }, 3000);

    // Cleanup listeners on component unmount
    return () => {
      // Remove event listeners
      window.google.maps.event.clearListeners(map, 'zoom_changed');
      window.google.maps.event.clearListeners(map, 'center_changed');
      window.google.maps.event.clearListeners(map, 'bounds_changed');
      // Remove overlay properly
      if (pulsingOverlay) {
        pulsingOverlay.remove();
      }
    };

  }, [mapLoaded, latitude, longitude, address, companyName]);

  if (!mapLoaded) {
    return (
      <div className="relative bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg h-64 border-2 border-dashed border-blue-300 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-red-500 mx-auto mb-3"></div>
          <p className="text-sm text-blue-700 font-medium">Loading Google Maps...</p>
          <p className="text-xs text-blue-500 mt-1">Preparing interactive map</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      <div 
        ref={mapRef} 
        className="w-full h-64 rounded-lg border-2 border-blue-200 overflow-hidden shadow-xl"
        style={{ 
          minHeight: '256px',
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
        }}
      />
    </div>
  );
};

// PropTypes for GoogleMapPreview
GoogleMapPreview.propTypes = {
  latitude: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  longitude: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  address: PropTypes.string,
  companyName: PropTypes.string
};

// Import drag and drop components
import {
  DndContext,
  closestCenter,
  PointerSensor,
  useSensor,
  useSensors,
  useDroppable,
  DragOverlay
} from '@dnd-kit/core';
import {
  SortableContext,
  useSortable,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import {CSS} from '@dnd-kit/utilities';

function ManagerDetailModal({
  visible,
  onHide,
  selectedManager,
  managerActivePackages,
  loadingManagerPackages,
  managerPackagesError,
  managerPackageHistory,
  loadingManagerPackageHistory,
  managerPackageHistoryError,
  onGiftPackage,
  onCreateCard,
  onEditManager,
  onDeleteManager,
  leftCards,
  setLeftCards,
  rightCards,
  setRightCards,
  cardsLoading,
  assignLoading,
  handleAssignCards,
  cardsFetched
}) {
  const { isMobile } = useLayout();
  const toastRef = useRef(null);
  const [selectedCardForDetails, setSelectedCardForDetails] = useState(null);
  const [cardDetailsModalVisible, setCardDetailsModalVisible] = useState(false);

  if (!selectedManager) return null;

  return (
    <>
      <Toast ref={toastRef} />
      <Dialog
        visible={visible}
        style={{ width: isMobile ? "99vw" : "1020px" }}
        header="Manager Details"
        modal
        className="p-fluid dark:bg-gray-800 dark:text-gray-100"
        onHide={onHide}
      >
        <div className="space-y-6">
          {/* Manager Header */}
          <div className="flex items-center space-x-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-800 rounded-lg">
            <img
              src={selectedManager.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(selectedManager.name)}&background=00c3ac&color=fff&size=80`}
              alt={selectedManager.name}
              className="w-20 h-20 rounded-full object-cover border-4 border-white dark:border-gray-600 shadow-lg"
            />
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">{selectedManager.name}</h2>
              <p className="text-lg text-gray-600 dark:text-gray-400">{selectedManager.company_name || 'No Company'}</p>
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium text-white mt-2 ${
                selectedManager.packages && selectedManager.packages.length > 0 && selectedManager.packages[selectedManager.packages.length - 1]?.status === "active"
                  ? "bg-green-500"
                  : selectedManager.packages && selectedManager.packages.length > 0
                    ? "bg-red-500"
                    : "bg-gray-500"
              }`}>
                {selectedManager.packages && selectedManager.packages.length > 0
                  ? selectedManager.packages[selectedManager.packages.length - 1]?.status
                  : "No Package"}
              </span>
            </div>
          </div>

          {/* Contact Information */}
          <div className={`grid ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2'} gap-4`}>
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-600 pb-2">Contact Information</h3>

              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <FaEnvelope className="text-blue-500 w-5 h-5" />
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Email</p>
                    <p className="text-gray-900 dark:text-gray-100">{selectedManager.email || 'Not provided'}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <FaPhone className="text-green-500 w-5 h-5" />
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Phone</p>
                    <p className="text-gray-900 dark:text-gray-100">{selectedManager.phone || 'Not provided'}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <FaBuilding className="text-purple-500 w-5 h-5" />
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Company</p>
                    <p className="text-gray-900 dark:text-gray-100">{selectedManager.company_name || 'Not provided'}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <FaUser className="text-orange-500 w-5 h-5" />
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Department</p>
                    <p className="text-gray-900 dark:text-gray-100">{selectedManager.department || 'Not provided'}</p>
                  </div>
                </div>

                {selectedManager.latitude && selectedManager.longitude && (
                  <div className="flex items-center space-x-3">
                    <FaMapMarkerAlt className="text-red-500 w-5 h-5" />
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Location</p>
                      <p className="text-gray-900 dark:text-gray-100">{selectedManager.address ? 'Available' : 'Coordinates only'}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Additional Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-600 pb-2">Additional Information</h3>

              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Manager ID</p>
                  <p className="text-gray-900 dark:text-gray-100 font-mono">{selectedManager.id}</p>
                </div>

                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">User Type</p>
                  <p className="text-gray-900 dark:text-gray-100 capitalize">{selectedManager.user_type || 'Manager'}</p>
                </div>

                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Position</p>
                  <p className="text-gray-900 dark:text-gray-100">{selectedManager.position || 'Not specified'}</p>
                </div>

                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Created At</p>
                  <p className="text-gray-900 dark:text-gray-100">{new Date(selectedManager.created_at).toLocaleDateString()}</p>
                </div>

                {selectedManager.latitude && selectedManager.longitude && (
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Location Status</p>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <p className="text-gray-900 dark:text-gray-100 text-sm">Location Set</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Company Location Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-600 pb-2 flex items-center gap-2">
              <FaMapMarkerAlt className="text-red-500" /> Company Location
            </h3>

            {selectedManager.latitude && selectedManager.longitude ? (
              <div className="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-700 dark:to-gray-800 rounded-xl p-6 border border-blue-200 dark:border-gray-600 shadow-lg">
                {/* Location Header */}
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg">
                    <FaGlobe className="text-white text-2xl" />
                  </div>
                  <div className="flex-1">
                    <h4 className="text-xl font-bold text-blue-900 dark:text-blue-100 mb-1">Company Address</h4>
                    <p className="text-blue-700 dark:text-blue-300 text-sm">Geographic location and coordinates</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-sm font-medium text-green-700 dark:text-green-400">Location Available</span>
                  </div>
                </div>

                {/* Address Display */}
                {selectedManager.address && (
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-6 border border-blue-100 dark:border-gray-600 shadow-sm">
                    <div className="flex items-start gap-3">
                      <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <FaMapMarkerAlt className="text-red-500 text-lg" />
                      </div>
                      <div className="flex-1">
                        <label className="text-xs font-semibold text-gray-500 dark:text-gray-400 block mb-2 uppercase tracking-wide">Full Address</label>
                        <p className="text-gray-900 dark:text-gray-100 font-medium leading-relaxed">{selectedManager.address}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Coordinates Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-100 dark:border-gray-600 shadow-sm">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                        <FaMap className="text-blue-500 dark:text-blue-400 text-sm" />
                      </div>
                      <div>
                        <label className="text-xs font-semibold text-gray-500 dark:text-gray-400 block mb-1">Latitude</label>
                        <p className="text-gray-900 dark:text-gray-100 font-mono text-sm">{Number(selectedManager.latitude).toFixed(6)}°</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-100 dark:border-gray-600 shadow-sm">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                        <FaMap className="text-green-500 dark:text-green-400 text-sm" />
                      </div>
                      <div>
                        <label className="text-xs font-semibold text-gray-500 dark:text-gray-400 block mb-1">Longitude</label>
                        <p className="text-gray-900 dark:text-gray-100 font-mono text-sm">{Number(selectedManager.longitude).toFixed(6)}°</p>
                      </div>
                    </div>
                  </div>
                </div>

                                {/* Interactive Google Map Preview */}
                <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-100 dark:border-gray-600 shadow-sm">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                        <FaMap className="text-white text-sm" />
                      </div>
                      <div>
                        <label className="text-sm font-bold text-gray-800 dark:text-gray-200">Company Location</label>
                        <p className="text-xs text-gray-600 dark:text-gray-400 font-medium">{selectedManager.company_name ? `${selectedManager.company_name} Headquarters` : 'Corporate Address & Coordinates'}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 bg-blue-50 dark:bg-blue-900/20 px-3 py-1 rounded-full border border-blue-200 dark:border-blue-800">
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                      <span className="text-xs text-blue-700 dark:text-blue-300 font-semibold">Interactive Map</span>
                    </div>
                  </div>
                   
                  <div className="relative">
                    <GoogleMapPreview 
                      latitude={selectedManager.latitude}
                      longitude={selectedManager.longitude}
                      address={selectedManager.address}
                      companyName={selectedManager.company_name}
                    />
                    
                    {/* Floating action button to open in Google Maps */}
                    <div className="absolute bottom-4 right-4">
                      <button
                        onClick={() => {
                          const url = `https://www.google.com/maps?q=${selectedManager.latitude},${selectedManager.longitude}`;
                          window.open(url, '_blank');
                        }}
                        className="bg-white/90 backdrop-blur-sm rounded-full p-3 shadow-lg border border-white/50 hover:bg-white transition-all duration-200 hover:shadow-xl"
                        title="Open in Google Maps"
                      >
                        <FaGlobe className="text-blue-500 text-lg" />
                      </button>
                    </div>

                    {/* Coordinates Display */}
                    <div className="absolute top-4 left-4">
                      <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg px-3 py-2 shadow-lg border border-white/50 dark:border-gray-600/50">
                        <div className="text-xs text-gray-600 dark:text-gray-400">
                          <div className="font-mono">{Number(selectedManager.latitude).toFixed(4)}°</div>
                          <div className="font-mono">{Number(selectedManager.longitude).toFixed(4)}°</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Location Actions */}
                <div className="flex gap-3 mt-4">
                  <button
                    className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
                    onClick={() => {
                      const url = `https://www.openstreetmap.org/?mlat=${selectedManager.latitude}&mlon=${selectedManager.longitude}&zoom=15`;
                      window.open(url, '_blank');
                    }}
                  >
                    <FaGlobe className="text-white" />
                    <span>Open in Maps</span>
                  </button>
                  
                  <button
                    className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
                    onClick={() => {
                      const url = `https://www.google.com/maps?q=${selectedManager.latitude},${selectedManager.longitude}`;
                      window.open(url, '_blank');
                    }}
                  >
                    <FaMap className="text-white" />
                    <span>Google Maps</span>
                  </button>
                </div>
              </div>
            ) : (
              <div className="bg-gradient-to-br from-gray-50 to-slate-100 dark:from-gray-700 dark:to-gray-800 rounded-xl p-8 border-2 border-dashed border-gray-300 dark:border-gray-600 text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-gray-300 to-slate-400 dark:from-gray-600 dark:to-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
                  <FaMapMarkerAlt className="text-gray-500 dark:text-gray-400 text-3xl" />
                </div>
                <h4 className="text-xl font-bold text-gray-700 dark:text-gray-300 mb-2">No Location Data</h4>
                <p className="text-gray-500 dark:text-gray-400 mb-4">This manager hasn&apos;t set their company location yet.</p>
                <div className="flex items-center justify-center gap-2 text-sm text-gray-400 dark:text-gray-500">
                  <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full"></div>
                  <span>Location not available</span>
                  <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full"></div>
                </div>
              </div>
            )}
          </div>

          {/* Active Packages Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-600 pb-2 flex items-center gap-2">
              <HiOutlineCreditCard className="text-blue-500" /> Active Packages & Cards
            </h3>

            {loadingManagerPackages ? (
              <div className="flex flex-col gap-4">
                {[...Array(1)].map((_, i) => (
                  <div key={i} className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-100 dark:border-gray-700 shadow animate-pulse">
                    <Skeleton width="100%" height="30px" className="mb-2" />
                    <Skeleton width="80%" height="20px" className="mb-2" />
                    <Skeleton width="60%" height="20px" />
                  </div>
                ))}
              </div>
            ) : managerPackagesError || !managerActivePackages || !managerActivePackages.active_packages || managerActivePackages.active_packages.length === 0 ? (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-8 text-center">
                <div className="text-6xl text-red-300 dark:text-red-400 mb-4">📦</div>
                <h3 className="text-xl font-bold text-red-800 dark:text-red-300 mb-2">NO PURCHASED PACKAGE</h3>
                <p className="text-red-600 dark:text-red-400">No active packages found for this user.</p>
              </div>
            ) : (
              <div className="flex flex-col gap-6">
                {managerActivePackages.active_packages.map((pkg) => (
                  <div
                    key={pkg.id}
                    className="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-700 dark:to-gray-800 rounded-xl p-5 border border-blue-100 dark:border-gray-600 shadow-md flex flex-col gap-4 relative overflow-hidden"
                  >
                    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-blue-200 dark:border-gray-600 p-6 mb-2 relative">
                      {Number(pkg.total_price) === 0 ? (
                        <i
                          className="pi pi-gift"
                          style={{
                            position: 'absolute',
                            left: '50%',
                            top: '50%',
                            transform: 'translate(-50%, -50%)',
                            fontSize: '14rem',
                            color: 'rgba(139,92,246,0.13)',
                            zIndex: 1,
                            pointerEvents: 'none',
                            opacity: 1,
                          }}
                        />
                      ) : (
                        <BsBank2
                          style={{
                            position: 'absolute',
                            left: '50%',
                            top: '50%',
                            transform: 'translate(-50%, -50%)',
                            fontSize: '14rem',
                            color: 'rgba(37,99,235,0.13)',
                            zIndex: 1,
                            pointerEvents: 'none',
                            opacity: 1,
                          }}
                        />
                      )}
                      <div className="relative z-10">
                        <div className="flex items-center gap-4 mb-4">
                          <div className="flex items-center justify-center w-12 h-12 rounded-full bg-blue-100">
                            <HiOutlineCreditCard className="text-blue-500 text-2xl" />
                          </div>
                          <div className="flex-1">
                            <div className="text-2xl font-bold text-blue-900 dark:text-blue-100 mb-1">{pkg.name}</div>
                            <span className={`inline-block px-3 py-1 rounded-full text-xs font-semibold ${pkg.status === 'active' ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-700'}`}>{pkg.status}</span>
                          </div>
                        </div>
                        
                        {/* Package Details Grid */}
                        <div className={`grid ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'} gap-8`}>
                          <div className="flex items-center gap-2">
                            <FaHashtag className="text-gray-400 dark:text-gray-500" />
                            <span className="text-xs text-gray-500 dark:text-gray-400">Package ID</span>
                            <span className="font-mono text-base text-gray-800 dark:text-gray-200">{pkg.id}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <FaMoneyBillWave className="text-green-500 dark:text-green-400" />
                            <span className="text-xs text-gray-500 dark:text-gray-400">Monthly Price</span>
                            <span className="font-mono text-base text-blue-700 dark:text-blue-400">${pkg.monthly_price}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <FaMoneyCheckAlt className="text-blue-500 dark:text-blue-400" />
                            <span className="text-xs text-gray-500 dark:text-gray-400">Yearly Price</span>
                            <span className="font-mono text-base text-blue-700 dark:text-blue-400">${pkg.yearly_price}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <FaPercent className="text-purple-500 dark:text-purple-400" />
                            <span className="text-xs text-gray-500 dark:text-gray-400">Yearly Discount</span>
                            <span className="text-base text-gray-800 dark:text-gray-200">{pkg.yearly_discount ?? '—'}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <FaRegClock className="text-yellow-500 dark:text-yellow-400" />
                            <span className="text-xs text-gray-500 dark:text-gray-400">Subscription Duration</span>
                            <span className="text-base text-gray-800 dark:text-gray-200">{pkg.subscription_duration} month(s)</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <HiOutlineCreditCard className="text-blue-400 dark:text-blue-300" />
                            <span className="text-xs text-gray-500 dark:text-gray-400">Card Limit</span>
                            <span className="text-base text-gray-800 dark:text-gray-200">{pkg.card_limit}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <FaCalendarTimes className="text-red-400 dark:text-red-300" />
                            <span className="text-xs text-gray-500 dark:text-gray-400">Expiry Date</span>
                            <span className="font-mono text-base text-gray-800 dark:text-gray-200">{pkg.expiry_date}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <FaCalendarCheck className="text-green-400 dark:text-green-300" />
                            <span className="text-xs text-gray-500 dark:text-gray-400">Purchased At</span>
                            <span className="font-mono text-base text-gray-800 dark:text-gray-200">{pkg.purchased_at}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <FaDollarSign className="text-green-700 dark:text-green-400" />
                            <span className="text-xs text-gray-500 dark:text-gray-400">Total Price</span>
                            <span className="font-mono text-base text-blue-700 dark:text-blue-400">${pkg.total_price}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <FaUserTie className="text-gray-700 dark:text-gray-400" />
                            <span className="text-xs text-gray-500 dark:text-gray-400">Purchased By Manager ID</span>
                            <span className="font-mono text-base text-gray-800 dark:text-gray-200">{pkg.purchased_by_manager_id}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <FaLayerGroup className="text-blue-700 dark:text-blue-400" />
                            <span className="text-xs text-gray-500 dark:text-gray-400">Cards Count</span>
                            <span className="font-bold text-base text-blue-900 dark:text-blue-300">{pkg.cards_count}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Cards Table */}
                    <div className="overflow-x-auto mt-2">
                      {pkg.cards && pkg.cards.length > 0 && (
                        <div className="flex justify-end mb-2 gap-2">
                          {/* Excel Button */}
                          <div className="relative group">
                            <button
                              className="flex items-center gap-1 px-2 py-1 bg-gradient-to-r from-green-400 via-green-500 to-green-600 hover:from-green-500 hover:to-green-700 text-white text-xs font-semibold rounded-xl shadow-sm border border-green-200 transition-all duration-200 transform hover:scale-105 active:scale-95 focus:outline-none focus:ring-2 focus:ring-green-200"
                              onClick={() => {
                                const cards = pkg.cards.map(card => ({
                                  'Card ID': card.id,
                                  'Name': card.name,
                                  'Number': card.number,
                                  'Card Type': card.card_type?.name,
                                  'Created At': card.created_at,
                                  'Updated At': card.updated_at,
                                  'Package Name': pkg.name,
                                  'Package ID': pkg.id,
                                }));
                                if (cards.length === 0) return;
                                const ws = XLSX.utils.json_to_sheet(cards);
                                const wb = XLSX.utils.book_new();
                                XLSX.utils.book_append_sheet(wb, ws, 'Cards');
                                XLSX.writeFile(wb, `manager_${selectedManager?.id || ''}_package_${pkg.id}_cards.xlsx`);
                              }}
                              type="button"
                              style={{ minWidth: 0 }}
                            >
                              <FaFileExcel size={16} className="text-white drop-shadow" />
                              <span>Excel</span>
                            </button>
                            {/* Tooltip */}
                            <div className="absolute right-0 top-full mt-2 opacity-0 group-hover:opacity-100 pointer-events-none transition-opacity duration-200 z-50">
                              <div className="bg-gray-900 text-white text-xs rounded-lg px-3 py-1 shadow-lg whitespace-nowrap">
                                Export Cards To Excel
                              </div>
                            </div>
                          </div>
                          {/* Print PDF Button */}
                          <div className="relative group">
                            <button
                              className="flex items-center gap-1 px-2 py-1 bg-gradient-to-r from-gray-400 via-gray-500 to-gray-600 hover:from-gray-500 hover:to-gray-700 text-white text-xs font-semibold rounded-xl shadow-sm border border-gray-200 transition-all duration-200 transform hover:scale-105 active:scale-95 focus:outline-none focus:ring-2 focus:ring-gray-200"
                              onClick={() => {
                                const table = document.getElementById(`cards-table-${pkg.id}`);
                                if (!table) return;
                                const printWindow = window.open('', '', 'width=900,height=700');
                                printWindow.document.write('<html><head><title>Print Cards</title>');
                                printWindow.document.write('<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">');
                                printWindow.document.write('<style>@media print {@page {margin: 1cm;} html,body{margin:0!important;padding:0!important;} .print-hide-details{display:none!important;} }</style>');
                                printWindow.document.write('</head><body>');
                                printWindow.document.write('<h2 style="font-size:20px;font-weight:bold;margin-bottom:10px;">Cards Table</h2>');
                                printWindow.document.write(table.outerHTML);
                                printWindow.document.write('</body></html>');
                                printWindow.document.close();
                                printWindow.focus();
                                setTimeout(() => { printWindow.print(); printWindow.close(); }, 500);
                              }}
                              type="button"
                              style={{ minWidth: 0 }}
                            >
                              <FaPrint size={16} className="text-white drop-shadow" />
                              <span>Print PDF</span>
                            </button>
                            {/* Tooltip */}
                            <div className="absolute right-0 top-full mt-2 opacity-0 group-hover:opacity-100 pointer-events-none transition-opacity duration-200 z-50">
                              <div className="bg-gray-900 text-white text-xs rounded-lg px-3 py-1 shadow-lg whitespace-nowrap">
                                Print Table
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Mobile-friendly table */}
                      {isMobile ? (
                        <div className="space-y-3">
                          {pkg.cards && pkg.cards.length > 0 ? (
                            pkg.cards.map(card => (
                              <div key={card.id} className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 shadow-sm">
                                <div className="flex justify-between items-start mb-3">
                                  <div className="flex-1">
                                    <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-1">{card.name}</h4>
                                    <p className="text-sm text-gray-600 dark:text-gray-400 font-mono">{card.number}</p>
                                    <p className="text-xs text-gray-500 dark:text-gray-400">{card.card_type?.name}</p>
                                  </div>
                                  <div className="flex gap-2">
                                    <button
                                      className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition text-xs font-semibold"
                                      onClick={() => {
                                        setSelectedCardForDetails(card);
                                        setCardDetailsModalVisible(true);
                                      }}
                                    >
                                      View
                                    </button>
                                    <button
                                      className="p-2 bg-gradient-to-br from-red-500 via-red-600 to-red-700 text-white rounded-full shadow-md hover:from-red-600 hover:to-red-800 focus:outline-none transition-all duration-150"
                                      onClick={async () => {
                                        if (!selectedManager?.id) return;
                                        try {
                                          const token = localStorage.getItem('token');
                                          await axios.post(`${import.meta.env.VITE_BACKEND_URL}/packages/unassign-card`, {
                                            user_id: selectedManager.id,
                                            card_number: card.number
                                          }, {
                                            headers: { Authorization: `Bearer ${token}` }
                                          });
                                          toastRef.current?.show({
                                            severity: 'success',
                                            summary: 'Success',
                                            detail: 'Card unassigned successfully!',
                                            life: 3000,
                                          });
                                        } catch (error) {
                                          toastRef.current?.show({
                                            severity: 'error',
                                            summary: 'Error',
                                            detail: error.response?.data?.message || 'Failed to unassign card',
                                            life: 3000,
                                          });
                                        }
                                      }}
                                      title="Unassign this card from manager"
                                    >
                                      <TfiTrash className="text-white text-sm" />
                                    </button>
                                  </div>
                                </div>
                                <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
                                  <div>Created: {card.created_at ? new Date(card.created_at).toLocaleDateString() : ''}</div>
                                  <div>Updated: {card.updated_at ? new Date(card.updated_at).toLocaleDateString() : ''}</div>
                                </div>
                              </div>
                            ))
                          ) : (
                            <div className="text-center text-gray-500 dark:text-gray-400 py-8">No cards assigned to this package</div>
                          )}
                        </div>
                      ) : (
                        <table id={`cards-table-${pkg.id}`} className="min-w-full divide-y divide-gray-200 dark:divide-gray-700 rounded-lg overflow-hidden shadow bg-white dark:bg-gray-800">
                          <thead className="bg-blue-50 dark:bg-gray-700">
                            <tr>
                              <th className="px-3 py-2 text-left text-xs font-semibold text-blue-900 dark:text-blue-100 uppercase tracking-wider">ID</th>
                              <th className="px-3 py-2 text-left text-xs font-semibold text-blue-900 dark:text-blue-100 uppercase tracking-wider">Name</th>
                              <th className="px-3 py-2 text-left text-xs font-semibold text-blue-900 dark:text-blue-100 uppercase tracking-wider">Number</th>
                              <th className="px-3 py-2 text-left text-xs font-semibold text-blue-900 dark:text-blue-100 uppercase tracking-wider">Card Type</th>
                              <th className="px-3 py-2 text-left text-xs font-semibold text-blue-900 dark:text-blue-100 uppercase tracking-wider">Created At</th>
                              <th className="px-3 py-2 text-left text-xs font-semibold text-blue-900 dark:text-blue-100 uppercase tracking-wider">Updated At</th>
                              <th className="px-3 py-2 text-center text-xs font-semibold text-blue-900 dark:text-blue-100 uppercase tracking-wider print-hide-details">Details</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-100 dark:divide-gray-700">
                            {pkg.cards && pkg.cards.length > 0 ? (
                              pkg.cards.map(card => (
                                <tr key={card.id} className="hover:bg-blue-50 dark:hover:bg-gray-700 transition">
                                  <td className="px-3 py-2 font-mono text-gray-700 dark:text-gray-300 text-center">{card.id}</td>
                                  <td className="px-3 py-2 font-medium text-gray-900 dark:text-gray-100 truncate max-w-[120px]" title={card.name}>{card.name}</td>
                                  <td className="px-3 py-2 font-mono text-gray-700 dark:text-gray-300 truncate max-w-[120px]" title={card.number}>{card.number}</td>
                                  <td className="px-3 py-2 text-gray-700 dark:text-gray-300 text-center">{card.card_type?.name}</td>
                                  <td className="px-3 py-2 text-gray-700 dark:text-gray-300 text-center">{card.created_at ? new Date(card.created_at).toLocaleDateString() : ''}</td>
                                  <td className="px-3 py-2 text-gray-700 dark:text-gray-300 text-center">{card.updated_at ? new Date(card.updated_at).toLocaleDateString() : ''}</td>
                                  <td className="px-3 py-2 text-center print-hide-details">
                                    <div className="flex items-center justify-center gap-2">
                                      <button
                                        className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition text-xs font-semibold shadow-sm focus:outline-none"
                                        onClick={() => {
                                          setSelectedCardForDetails(card);
                                          setCardDetailsModalVisible(true);
                                        }}
                                        title="View Card Details"
                                        style={{ minWidth: 36, minHeight: 36, display: 'flex', alignItems: 'center', justifyContent: 'center' }}
                                      >
                                        View
                                      </button>
                                      {/* Unassign Button */}
                                      <div className="relative group">
                                        <button
                                          className="p-2 bg-gradient-to-br from-red-500 via-red-600 to-red-700 text-white rounded-full shadow-md hover:from-red-600 hover:to-red-800 focus:outline-none transition-all duration-150 flex items-center justify-center"
                                          style={{ minWidth: 36, minHeight: 36 }}
                                          onClick={async () => {
                                            if (!selectedManager?.id) return;
                                            try {
                                              const token = localStorage.getItem('token');
                                              await axios.post(`${import.meta.env.VITE_BACKEND_URL}/packages/unassign-card`, {
                                                user_id: selectedManager.id,
                                                card_number: card.number
                                              }, {
                                                headers: { Authorization: `Bearer ${token}` }
                                              });
                                              toastRef.current?.show({
                                                severity: 'success',
                                                summary: 'Success',
                                                detail: 'Card unassigned successfully!',
                                                life: 3000,
                                              });
                                            } catch (error) {
                                              toastRef.current?.show({
                                                severity: 'error',
                                                summary: 'Error',
                                                detail: error.response?.data?.message || 'Failed to unassign card',
                                                life: 3000,
                                              });
                                            }
                                          }}
                                          tabIndex={0}
                                          title="Unassign this card from manager"
                                        >
                                          <TfiTrash className="text-white text-base" />
                                        </button>
                                        {/* Tooltip */}
                                        <div className="absolute left-1/2 -translate-x-1/2 top-full mt-2 opacity-0 group-hover:opacity-100 pointer-events-none transition-opacity duration-200 z-50 whitespace-nowrap px-3 py-1 bg-gray-900 text-white text-xs rounded-lg shadow-lg">
                                          Unassign Card
                                        </div>
                                      </div>
                                    </div>
                                  </td>
                                </tr>
                              ))
                            ) : (
                              <tr>
                                <td colSpan="7" className="px-3 py-8 text-center text-gray-500 dark:text-gray-400">No cards assigned to this package</td>
                              </tr>
                            )}
                          </tbody>
                        </table>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Assign Cards Section */}
          {visible && cardsFetched && (
            <div className="mt-8">
              <h3 className="text-lg font-bold mb-4 text-center">Assign Cards to Manager</h3>
              <CardsDnDSection
                leftCards={leftCards}
                setLeftCards={setLeftCards}
                rightCards={rightCards}
                setRightCards={setRightCards}
                cardsLoading={cardsLoading}
                assignLoading={assignLoading}
                handleAssignCards={handleAssignCards}
                isMobile={isMobile}
              />
            </div>
          )}

          {/* Package History */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-600 pb-2 flex items-center gap-2">
              <FaBoxOpen className="text-orange-500" /> Package History
            </h3>
            {loadingManagerPackageHistory ? (
              <div className="flex flex-col gap-4">
                {[...Array(2)].map((_, i) => (
                  <div key={i} className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-100 dark:border-gray-700 shadow animate-pulse">
                    <Skeleton width="100%" height="30px" className="mb-2" />
                    <Skeleton width="80%" height="20px" className="mb-2" />
                    <Skeleton width="60%" height="20px" />
                  </div>
                ))}
              </div>
            ) : managerPackageHistoryError || !managerPackageHistory || managerPackageHistory.length === 0 ? (
              <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-8 text-center">
                <div className="text-6xl text-orange-300 dark:text-orange-400 mb-4">📋</div>
                <h3 className="text-xl font-bold text-orange-800 dark:text-orange-300 mb-2">NO PACKAGE HISTORY</h3>
                <p className="text-orange-600 dark:text-orange-400">No package history found for this manager.</p>
              </div>
            ) : (
              <div className="flex flex-col gap-6">
                {[...managerPackageHistory]
                  .sort((a, b) => new Date(b.purchased_at) - new Date(a.purchased_at))
                  .map((pkg, index) => (
                    <div
                      key={index}
                      className={`bg-gradient-to-br ${pkg.status === 'active' 
                        ? 'from-green-50 to-emerald-100' 
                        : 'from-red-50 to-rose-100'
                      } rounded-xl p-5 border ${pkg.status === 'active' 
                        ? 'border-green-100 dark:border-green-800' 
                        : 'border-red-100 dark:border-red-800'
                      } shadow-md flex flex-col gap-4 relative overflow-hidden`}
                    >
                      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-600 p-6 mb-2 relative">
                        {pkg.status === 'active' ? (
                          <FaCheckCircle
                            style={{
                              position: 'absolute',
                              left: '50%',
                              top: '50%',
                              transform: 'translate(-50%, -50%)',
                              fontSize: '14rem',
                              color: 'rgba(34,197,94,0.13)',
                              zIndex: 1,
                              pointerEvents: 'none',
                              opacity: 1,
                            }}
                          />
                        ) : (
                          <FaTimesCircle
                            style={{
                              position: 'absolute',
                              left: '50%',
                              top: '50%',
                              transform: 'translate(-50%, -50%)',
                              fontSize: '14rem',
                              color: 'rgba(239,68,68,0.13)',
                              zIndex: 1,
                              pointerEvents: 'none',
                              opacity: 1,
                            }}
                          />
                        )}
                        <div className="relative z-10">
                          <div className="flex items-center gap-4 mb-4">
                            <div className={`flex items-center justify-center w-12 h-12 rounded-full ${pkg.status === 'active' ? 'bg-green-100' : 'bg-red-100'}`}>
                              <FaBoxOpen className={`text-2xl ${pkg.status === 'active' ? 'text-green-500' : 'text-red-500'}`} />
                            </div>
                            <div className="flex-1">
                              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-1">{pkg.package_name || `Package #${index + 1}`}</div>
                              <span className={`inline-block px-3 py-1 rounded-full text-xs font-semibold ${pkg.status === 'active' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`}>
                                {pkg.status}
                              </span>
                            </div>
                          </div>
                          
                          {/* Package Details Grid */}
                          <div className={`grid ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'} gap-6`}>
                            <div className="flex items-center gap-2">
                              <FaDollarSign className="text-green-600 dark:text-green-400" />
                              <span className="text-xs text-gray-500 dark:text-gray-400">Total Price</span>
                              <span className="font-mono text-base text-gray-800 dark:text-gray-200">${pkg.total_price}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <FaLayerGroup className="text-blue-700 dark:text-blue-400" />
                              <span className="text-xs text-gray-500 dark:text-gray-400">Card Limit</span>
                              <span className="font-mono text-base text-gray-800 dark:text-gray-200">{pkg.card_limit}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <FaCalendarCheck className="text-green-500 dark:text-green-400" />
                              <span className="text-xs text-gray-500 dark:text-gray-400">Purchased At</span>
                              <span className="font-mono text-base text-gray-800 dark:text-gray-200">
                                {pkg.purchased_at ? new Date(pkg.purchased_at).toLocaleDateString() : 'N/A'}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <FaCalendarTimes className="text-red-400 dark:text-red-300" />
                              <span className="text-xs text-gray-500 dark:text-gray-400">Expiry Date</span>
                              <span className="font-mono text-base text-gray-800 dark:text-gray-200">
                                {pkg.expiry_date ? new Date(pkg.expiry_date).toLocaleDateString() : 'N/A'}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <FaRegClock className="text-yellow-500 dark:text-yellow-400" />
                              <span className="text-xs text-gray-500 dark:text-gray-400">Duration</span>
                              <span className="text-base text-gray-800 dark:text-gray-200">
                                {pkg.purchased_at && pkg.expiry_date 
                                  ? `${Math.ceil((new Date(pkg.expiry_date) - new Date(pkg.purchased_at)) / (1000 * 60 * 60 * 24))} days`
                                  : 'N/A'
                                }
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <FaHashtag className="text-gray-400 dark:text-gray-500" />
                              <span className="text-xs text-gray-500 dark:text-gray-400">History ID</span>
                              <span className="font-mono text-base text-gray-800 dark:text-gray-200">#{index + 1}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className={`flex ${isMobile ? 'flex-col' : 'flex-wrap'} gap-3 pt-4 border-t border-gray-200 dark:border-gray-600`}>
            <button
              style={{
                backgroundColor: '#9333ea',
                color: 'white',
                border: 'none',
                padding: '8px 16px',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                transition: 'background-color 0.2s',
                fontWeight: '500',
                width: isMobile ? '100%' : 'auto',
                justifyContent: isMobile ? 'center' : 'flex-start'
              }}
              onMouseEnter={(e) => e.target.style.backgroundColor = '#7c3aed'}
              onMouseLeave={(e) => e.target.style.backgroundColor = '#9333ea'}
              onClick={() => {
                onHide();
                onGiftPackage && onGiftPackage(selectedManager);
              }}
            >
              <FaGift />
              <span>Gift Package</span>
            </button>

            <button
              style={{
                backgroundColor: '#2563eb',
                color: 'white',
                border: 'none',
                padding: '8px 16px',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                transition: 'background-color 0.2s',
                fontWeight: '500',
                width: isMobile ? '100%' : 'auto',
                justifyContent: isMobile ? 'center' : 'flex-start'
              }}
              onMouseEnter={(e) => e.target.style.backgroundColor = '#1d4ed8'}
              onMouseLeave={(e) => e.target.style.backgroundColor = '#2563eb'}
              onClick={() => {
                onHide();
                onCreateCard && onCreateCard(selectedManager);
              }}
            >
              <HiOutlineCreditCard />
              <span>Create Card</span>
            </button>

            <button
              style={{
                backgroundColor: '#d97706',
                color: 'white',
                border: 'none',
                padding: '8px 16px',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                transition: 'background-color 0.2s',
                fontWeight: '500',
                width: isMobile ? '100%' : 'auto',
                justifyContent: isMobile ? 'center' : 'flex-start'
              }}
              onMouseEnter={(e) => e.target.style.backgroundColor = '#b45309'}
              onMouseLeave={(e) => e.target.style.backgroundColor = '#d97706'}
              onClick={() => {
                onHide();
                onEditManager && onEditManager(selectedManager);
              }}
            >
              <FiEdit />
              <span>Edit Manager</span>
            </button>

            {selectedManager.latitude && selectedManager.longitude && (
              <button
                style={{
                  backgroundColor: '#059669',
                  color: 'white',
                  border: 'none',
                  padding: '8px 16px',
                  borderRadius: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  transition: 'background-color 0.2s',
                  fontWeight: '500',
                  width: isMobile ? '100%' : 'auto',
                  justifyContent: isMobile ? 'center' : 'flex-start'
                }}
                onMouseEnter={(e) => e.target.style.backgroundColor = '#047857'}
                onMouseLeave={(e) => e.target.style.backgroundColor = '#059669'}
                onClick={() => {
                  const url = `https://www.google.com/maps?q=${selectedManager.latitude},${selectedManager.longitude}`;
                  window.open(url, '_blank');
                }}
              >
                <FaMapMarkerAlt />
                <span>View Location</span>
              </button>
            )}

            {selectedManager.id.toString() !== localStorage.getItem("user_id") && (
              <button
                style={{
                  backgroundColor: '#dc2626',
                  color: 'white',
                  border: 'none',
                  padding: '8px 16px',
                  borderRadius: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  transition: 'background-color 0.2s',
                  fontWeight: '500',
                  width: isMobile ? '100%' : 'auto',
                  justifyContent: isMobile ? 'center' : 'flex-start'
                }}
                onMouseEnter={(e) => e.target.style.backgroundColor = '#b91c1c'}
                onMouseLeave={(e) => e.target.style.backgroundColor = '#dc2626'}
                onClick={() => {
                  onHide();
                  onDeleteManager && onDeleteManager(selectedManager);
                }}
              >
                <TfiTrash />
                <span>Delete Manager</span>
              </button>
            )}
          </div>
        </div>
      </Dialog>

      {/* Card Details Modal */}
      <Dialog
        visible={cardDetailsModalVisible}
        style={{ width: '450px' }}
        header="Card Details"
        modal
        className="p-fluid dark:bg-gray-800 dark:text-gray-100"
        onHide={() => setCardDetailsModalVisible(false)}
      >
        {selectedCardForDetails && (
          <div className="space-y-3">
            <div className="flex flex-col gap-2">
              <div><span className="font-semibold text-gray-900 dark:text-gray-100">ID:</span> <span className="text-gray-700 dark:text-gray-300">{selectedCardForDetails.id}</span></div>
              <div><span className="font-semibold text-gray-900 dark:text-gray-100">Name:</span> <span className="text-gray-700 dark:text-gray-300">{selectedCardForDetails.name}</span></div>
              <div><span className="font-semibold text-gray-900 dark:text-gray-100">Number:</span> <span className="text-gray-700 dark:text-gray-300">{selectedCardForDetails.number}</span></div>
              <div><span className="font-semibold text-gray-900 dark:text-gray-100">Created At:</span> <span className="text-gray-700 dark:text-gray-300">{selectedCardForDetails.created_at}</span></div>
              <div><span className="font-semibold text-gray-900 dark:text-gray-100">Updated At:</span> <span className="text-gray-700 dark:text-gray-300">{selectedCardForDetails.updated_at}</span></div>
              {selectedCardForDetails.card_type && (
                <div className="bg-blue-50 dark:bg-blue-900/20 rounded p-2 mt-2">
                  <div className="font-semibold text-blue-800 dark:text-blue-300 mb-1">Card Type Details:</div>
                  <div><span className="font-semibold text-gray-900 dark:text-gray-100">Name:</span> <span className="text-gray-700 dark:text-gray-300">{selectedCardForDetails.card_type.name}</span></div>
                  <div><span className="font-semibold text-gray-900 dark:text-gray-100">Type Connection:</span> <span className="text-gray-700 dark:text-gray-300">{selectedCardForDetails.card_type.type_of_connection}</span></div>
                  <div><span className="font-semibold text-gray-900 dark:text-gray-100">Colors:</span> <span className="text-gray-700 dark:text-gray-300">{selectedCardForDetails.card_type.number_of_colors}</span></div>
                  {selectedCardForDetails.card_type.setting && (
                    <div className="mt-1">
                      <span className="font-semibold text-gray-900 dark:text-gray-100">Settings:</span>
                      <ul className="ml-4 list-disc">
                        {Object.entries(selectedCardForDetails.card_type.setting).map(([key, value]) => (
                          <li key={key} className="text-xs text-gray-600 dark:text-gray-400">{key}: {value}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  <div><span className="font-semibold text-gray-900 dark:text-gray-100">Created At:</span> <span className="text-gray-700 dark:text-gray-300">{selectedCardForDetails.card_type.created_at}</span></div>
                  <div><span className="font-semibold text-gray-900 dark:text-gray-100">Updated At:</span> <span className="text-gray-700 dark:text-gray-300">{selectedCardForDetails.card_type.updated_at}</span></div>
                </div>
              )}
              {/* Show any other fields dynamically */}
              {Object.entries(selectedCardForDetails).map(([key, value]) => (
                !['id','name','number','card_type','created_at','updated_at'].includes(key) && value ? (
                  <div key={key}><span className="font-semibold text-gray-900 dark:text-gray-100">{key}:</span> <span className="text-gray-700 dark:text-gray-300">{typeof value === 'object' ? JSON.stringify(value) : value}</span></div>
                ) : null
              ))}
            </div>
          </div>
        )}
      </Dialog>
    </>
  );
}

// CardsDnDSection Component
function CardsDnDSection({leftCards, setLeftCards, rightCards, setRightCards, cardsLoading, assignLoading, handleAssignCards, isMobile}) {
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 10, 
        delay: 250, 
      },
    })
  );
  const [isOverRight, setIsOverRight] = useState(false);
  const [justDropped, setJustDropped] = useState(false);
  const [justDroppedCardId, setJustDroppedCardId] = useState(null);
  const [activeCard, setActiveCard] = useState(null);
  const popAudioRef = useRef();
  const [audioActivated, setAudioActivated] = useState(false);
  const [selectedCardIds, setSelectedCardIds] = useState([]);
  const [activeCardIds, setActiveCardIds] = useState([]);

  const handleDoubleClick = (cardId, from) => {
    if (from === 'left') {
      const card = leftCards.find(c => c.id === cardId);
      if (card) {
        setLeftCards(prev => prev.filter(c => c.id !== cardId));
        setRightCards(prev => [...prev, card]);
        setJustDropped(true);
        setJustDroppedCardId(cardId);
        if (popAudioRef.current) {
          popAudioRef.current.volume = 1;
          popAudioRef.current.currentTime = 0;
          popAudioRef.current.play().catch(()=>{});
        }
        setTimeout(() => {
          setJustDropped(false);
          setJustDroppedCardId(null);
        }, 900);
      }
    } else if (from === 'right') {
      const card = rightCards.find(c => c.id === cardId);
      if (card) {
        setRightCards(prev => prev.filter(c => c.id !== cardId));
        setLeftCards(prev => [...prev, card]);
      }
    }
  };

  const handleDragStart = (event) => {
    if (!audioActivated && popAudioRef.current) {
      popAudioRef.current.volume = 0.01;
      popAudioRef.current.play().catch(()=>{});
      setAudioActivated(true);
    }
    const { active } = event;
    if (selectedCardIds.includes(active.id)) {
      setActiveCardIds(selectedCardIds);
    } else {
      setActiveCardIds([active.id]);
    }
    const card = leftCards.find(c => c.id === active.id) || rightCards.find(c => c.id === active.id);
    setActiveCard(card);
  };

  const handleDragEnd = (event) => {
    setIsOverRight(false);
    setActiveCard(null);
    const {active, over} = event;
    if (!over) {
      setActiveCardIds([]);
      return;
    }
    if (active.id === over.id) {
      setActiveCardIds([]);
      return;
    }
    let targetList = null;
    if (over.id === 'left-list') {
      targetList = 'left';
    } else if (over.id === 'right-list') {
      targetList = 'right';
    } else if (rightCards.some(c => c.id === over.id)) {
      targetList = 'right';
    } else if (leftCards.some(c => c.id === over.id)) {
      targetList = 'left';
    }
    if (!targetList) {
      setActiveCardIds([]);
      return;
    }

    // Multi-card move
    if (activeCardIds.length > 1) {
      if (targetList === 'left') {
        const moved = rightCards.filter(c => activeCardIds.includes(c.id));
        setRightCards(prev => prev.filter(c => !activeCardIds.includes(c.id)));
        setLeftCards(prev => [...prev, ...moved]);
      } else if (targetList === 'right') {
        const moved = leftCards.filter(c => activeCardIds.includes(c.id));
        setLeftCards(prev => prev.filter(c => !activeCardIds.includes(c.id)));
        setRightCards(prev => [...prev, ...moved]);
        setJustDropped(true);
        setJustDroppedCardId(activeCardIds[0]);
        if (popAudioRef.current) {
          popAudioRef.current.volume = 1;
          popAudioRef.current.currentTime = 0;
          popAudioRef.current.play().catch(()=>{});
        }
        setTimeout(() => {
          setJustDropped(false);
          setJustDroppedCardId(null);
        }, 900);
      }
      setSelectedCardIds([]);
      setActiveCardIds([]);
      return;
    }

    // Single card move
    if (targetList === 'left' && rightCards.find(c => c.id === active.id)) {
      const card = rightCards.find(c => c.id === active.id);
      setRightCards(prev => prev.filter(c => c.id !== active.id));
      setLeftCards(prev => [...prev, card]);
    }
    else if (targetList === 'right' && leftCards.find(c => c.id === active.id)) {
      const card = leftCards.find(c => c.id === active.id);
      setLeftCards(prev => prev.filter(c => c.id !== active.id));
      setRightCards(prev => [...prev, card]);
      setJustDropped(true);
      setJustDroppedCardId(card.id);
      if (popAudioRef.current) {
        popAudioRef.current.volume = 1;
        popAudioRef.current.currentTime = 0;
        popAudioRef.current.play().catch(()=>{});
      }
      setTimeout(() => {
        setJustDropped(false);
        setJustDroppedCardId(null);
      }, 900);
    }
    setActiveCardIds([]);
    setSelectedCardIds([]);
  };

  const handleDragOver = (event) => {
    if (event.over && event.over.id === 'right-list') {
      setIsOverRight(true);
    } else {
      setIsOverRight(false);
    }
  };

  const groupedLeftCards = groupCardsByType(leftCards);

  useEffect(() => {
    if (justDropped && popAudioRef.current) {
      popAudioRef.current.currentTime = 0;
      popAudioRef.current.play().catch(()=>{});
    }
  }, [justDropped]);

  const allLeftCardIds = leftCards.map(card => card.id);
  const allSelected = allLeftCardIds.length > 0 && allLeftCardIds.every(id => selectedCardIds.includes(id));
  const someSelected = selectedCardIds.length > 0 && !allSelected;
  const selectAllRef = useRef();

  useEffect(() => {
    if (selectAllRef.current) {
      selectAllRef.current.indeterminate = someSelected;
    }
  }, [someSelected]);

  return (
    <>
      <audio ref={popAudioRef} src="https://cdn.pixabay.com/audio/2022/03/15/audio_115b9b7bfa.mp3" preload="auto" />

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onDragOver={handleDragOver}
      >
        <div style={{display: 'flex', gap: isMobile ? 16 : 32, flexDirection: isMobile ? 'column' : 'row'}}>
          <SortableContext id="left-list" items={leftCards.map(c => c.id)} strategy={verticalListSortingStrategy}>
            <DroppableContainer id="left-list" style={{
              minHeight: 120,
              flex: 1,
              background: 'linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%)',
              borderRadius: 18,
              border: '2px solid #60a5fa',
              padding: 18,
              overflowY: 'auto',
              maxHeight: isMobile ? 300 : 420,
              boxShadow: '0 4px 24px 0 rgba(96,165,250,0.08)',
              transition: 'box-shadow 0.3s, border-color 0.3s',
            }} justDropped={justDropped}>
              <div className={`flex items-center gap-3 mb-4 ${isMobile ? 'flex-col text-center' : ''}`}>
                <svg width="28" height="28" viewBox="0 0 32 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="1" y="2" width="30" height="18" rx="4" fill="url(#card-gradient2)" stroke="#2563eb" strokeWidth="1.5"/>
                  <rect x="1" y="7" width="30" height="3" fill="#e0e7ff"/>
                  <circle cx="7" cy="16" r="2" fill="#fbbf24"/>
                  <rect x="12" y="15" width="10" height="2" rx="1" fill="#fff" opacity="0.7"/>
                  <defs>
                    <linearGradient id="card-gradient2" x1="1" y1="2" x2="31" y2="20" gradientUnits="userSpaceOnUse">
                      <stop stopColor="#60a5fa"/>
                      <stop offset="1" stopColor="#a5b4fc"/>
                    </linearGradient>
                  </defs>
                </svg>
                <h4 className="font-bold text-blue-700 dark:text-blue-300 text-lg">Available Cards</h4>
                {!isMobile && (
                  <div className="flex items-center gap-2 ml-2">
                    <input
                      type="checkbox"
                      ref={selectAllRef}
                      checked={allSelected}
                      onChange={e => {
                        if (e.target.checked) {
                          setSelectedCardIds(allLeftCardIds);
                        } else {
                          setSelectedCardIds([]);
                        }
                      }}
                      className="accent-blue-700 w-4 h-4 ml-12"
                    />
                    <span className="text-sm font-medium text-blue-700 dark:text-blue-300">Select All</span>
                  </div>
                )}
              </div>
              {cardsLoading && <div className="text-center text-gray-400 dark:text-gray-500">Loading...</div>}
              {!cardsLoading && Object.keys(groupedLeftCards).length === 0 && (
                <div className="text-center text-gray-400 dark:text-gray-500">No cards available</div>
              )}
              {Object.entries(groupedLeftCards).map(([type, cards]) => (
                <div key={type} className="mb-4 rounded-xl border p-3" style={{ background: '#ede9fe', borderColor: '#a78bfa' }}>
                  <div className="flex items-center gap-2 mb-2">
                    <span className="inline-flex items-center justify-center w-8 h-8 mr-1">
                      <svg width="32" height="22" viewBox="0 0 32 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect x="1" y="2" width="30" height="18" rx="4" fill="url(#card-gradient)" stroke="#a78bfa" strokeWidth="1.5"/>
                        <rect x="1" y="7" width="30" height="3" fill="#e0e7ff"/>
                        <circle cx="7" cy="16" r="2" fill="#fbbf24"/>
                        <rect x="12" y="15" width="10" height="2" rx="1" fill="#fff" opacity="0.7"/>
                        <defs>
                          <linearGradient id="card-gradient" x1="1" y1="2" x2="31" y2="20" gradientUnits="userSpaceOnUse">
                            <stop stopColor="#a78bfa"/>
                            <stop offset="1" stopColor="#ede9fe"/>
                          </linearGradient>
                        </defs>
                      </svg>
                    </span>
                    <span className="font-bold text-purple-700 dark:text-purple-300">{type}</span>
                    <span className="ml-2 text-xs text-purple-700 dark:text-purple-300 bg-purple-100 dark:bg-purple-900/20 rounded-full px-2 py-0.5">{cards.length} cards</span>
                  </div>
                  {cards.map(card => (
                    <div key={card.id} className="flex items-center gap-2 mb-1">
                      <input
                        type="checkbox"
                        checked={selectedCardIds.includes(card.id)}
                        onChange={() => setSelectedCardIds(ids => ids.includes(card.id) ? ids.filter(i => i !== card.id) : [...ids, card.id])}
                        className="accent-purple-500"
                      />
                      <DraggableCard
                        card={card}
                        id={card.id}
                        from="left"
                        isOverlay={false}
                        justDropped={false}
                        selected={selectedCardIds.includes(card.id)}
                        isMobile={isMobile}
                        onDoubleClick={handleDoubleClick}
                      />
                    </div>
                  ))}
                </div>
              ))}
            </DroppableContainer>
          </SortableContext>

          <SortableContext id="right-list" items={rightCards.map(c => c.id)} strategy={verticalListSortingStrategy}>
            <DroppableContainer id="right-list" style={{
              minHeight: 180,
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              background: isOverRight
                ? 'linear-gradient(135deg, #bbf7d0 0%, #f0fdf4 100%)'
                : 'linear-gradient(135deg, #f0fdf4 0%, #bbf7d0 100%)',
              borderRadius: 18,
              border: isOverRight ? '3px dashed #a78bfa' : '3px dashed #a78bfa',
              padding: isMobile ? 16 : 32,
              paddingBottom: isMobile ? 60 : 90,
              boxShadow: isOverRight
                ? '0 8px 32px 0 rgba(168,139,250,0.18), 0 2px 8px 0 rgba(168,139,250,0.10)'
                : justDropped
                  ? '0 0 0 4px #a78bfa, 0 4px 24px 0 rgba(168,139,250,0.08)'
                  : '0 4px 24px 0 rgba(16,185,129,0.08)',
              transition: 'box-shadow 0.3s, border-color 0.3s, background 0.3s',
              position: 'relative',
              overflow: 'visible',
              animation: justDropped ? 'flash-purple 0.7s' : undefined,
            }} justDropped={justDropped}>
              <div className="flex flex-col items-center justify-center w-full mb-4">
                <svg width="38" height="38" viewBox="0 0 38 38" fill="none" xmlns="http://www.w3.org/2000/svg" className={isOverRight ? 'animate-pulse' : ''}>
                  <rect x="3" y="7" width="32" height="22" rx="6" fill="url(#drop-gradient)" stroke="#22c55e" strokeWidth="2"/>
                  <rect x="3" y="13" width="32" height="4" fill="#bbf7d0"/>
                  <circle cx="10" cy="25" r="3" fill="#fbbf24"/>
                  <rect x="16" y="23" width="12" height="3" rx="1.5" fill="#fff" opacity="0.7"/>
                  <defs>
                    <linearGradient id="drop-gradient" x1="3" y1="7" x2="35" y2="29" gradientUnits="userSpaceOnUse">
                      <stop stopColor="#6ee7b7"/>
                      <stop offset="1" stopColor="#bbf7d0"/>
                    </linearGradient>
                  </defs>
                </svg>
                <h4 className="font-bold text-green-700 dark:text-green-300 text-lg mt-2 text-center">Cards to Assign</h4>
                <span className="text-xs text-green-600 dark:text-green-400 mt-1 text-center">Drop cards here to assign</span>
              </div>
              <div className="flex flex-col w-full items-center justify-center">
                {rightCards.map((card) => (
                  <div key={card.id} className="flex items-center w-full mb-2 gap-2">
                    <DraggableCard
                      card={card}
                      id={card.id}
                      from="right"
                      setLeftCards={setLeftCards}
                      setRightCards={setRightCards}
                      isOverlay={false}
                      justDropped={justDroppedCardId === card.id}
                      selected={selectedCardIds.includes(card.id)}
                      onSelect={() => setSelectedCardIds(ids => ids.includes(card.id) ? ids.filter(i => i !== card.id) : [...ids, card.id])}
                      isMobile={isMobile}
                      onDoubleClick={handleDoubleClick}
                    />
                    <button
                      className="text-xs text-red-500 hover:text-red-700 flex items-center justify-center p-2 rounded-full bg-red-50 hover:bg-red-100 transition"
                      title="Remove"
                      type="button"
                      tabIndex={0}
                      onClick={e => {
                        e.stopPropagation();
                        e.preventDefault();
                        setRightCards(prev => prev.filter(c => c.id !== card.id));
                        setLeftCards(prev => [...prev, card]);
                      }}
                      onPointerDown={e => e.stopPropagation()}
                      onMouseDown={e => e.stopPropagation()}
                      onTouchStart={e => e.stopPropagation()}
                      style={{ minWidth: 36, minHeight: 36 }}
                    >
                      <TfiTrash size={16} />
                    </button>
                  </div>
                ))}
                {rightCards.length === 0 && <div className="text-center text-gray-400 dark:text-gray-500 w-full">Drag cards here to assign</div>}
                {rightCards.length > 0 && (
                  <button
                    className={`assign-cards-btn absolute ${isMobile ? 'left-4 right-4 bottom-4' : 'left-6 right-6 bottom-6'} mx-auto ${isMobile ? 'w-auto max-w-[calc(100%-2rem)]' : 'w-full md:w-auto'} ${isMobile ? 'py-2 px-4' : 'py-3 px-8'} rounded-2xl bg-gradient-to-r from-purple-500 to-green-400 text-white font-extrabold ${isMobile ? 'text-sm' : 'text-lg'} shadow-xl hover:from-purple-600 hover:to-green-600 transition-all duration-200 flex items-center justify-center gap-2 z-50`}
                    style={{ maxWidth: isMobile ? 'calc(100% - 2rem)' : 340 }}
                    onClick={handleAssignCards}
                    disabled={assignLoading}
                  >
                    <span className={`pi pi-check-circle ${isMobile ? 'text-lg' : 'text-2xl'} mr-2`} />
                    {assignLoading ? 'Assigning...' : 'Assign Cards'}
                  </button>
                )}
              </div>
              <style>{`
                @keyframes flash-green {
                  0% { box-shadow: 0 0 0 0 #22c55e; }
                  40% { box-shadow: 0 0 0 8px #bbf7d0; }
                  100% { box-shadow: 0 0 0 0 #22c55e; }
                }
                @keyframes flash-purple {
                  0% { box-shadow: 0 0 0 0 #a78bfa; }
                  40% { box-shadow: 0 0 0 8px #d3bff5; }
                  100% { box-shadow: 0 0 0 0 #a78bfa; }
                }
              `}</style>
            </DroppableContainer>
          </SortableContext>
        </div>
        <DragOverlay>
          {activeCardIds.length > 1 ? (
            <div className="p-4 bg-purple-100 dark:bg-purple-900/20 rounded-xl shadow-lg font-bold text-purple-700 dark:text-purple-300 flex items-center gap-2">
              <span className="pi pi-clone text-2xl" />
              {activeCardIds.length} Cards
            </div>
          ) : activeCard ? (
            <DraggableCard
              card={activeCard}
              id={activeCard.id}
              from={rightCards.find(c => c.id === activeCard.id) ? 'right' : 'left'}
              setLeftCards={setLeftCards}
              setRightCards={setRightCards}
              isOverlay={true}
              justDropped={justDroppedCardId === activeCard.id}
              selected={selectedCardIds.includes(activeCard.id)}
              onSelect={() => setSelectedCardIds(ids => ids.includes(activeCard.id) ? ids.filter(i => i !== activeCard.id) : [...ids, activeCard.id])}
              isMobile={isMobile}
              onDoubleClick={handleDoubleClick}
            />
          ) : null}
        </DragOverlay>
      </DndContext>
    </>
  );
}

// Helper functions
function groupCardsByType(cards) {
  return cards.reduce((groups, card) => {
    const type = card.card_type?.name || 'Other';
    if (!groups[type]) groups[type] = [];
    groups[type].push(card);
    return groups;
  }, {});
}

function DraggableCard({card, id, from, isOverlay, justDropped, selected, isMobile, onDoubleClick}) {
  const {attributes, listeners, setNodeRef, transform, isDragging} = useSortable({id});
  const style = {
    minHeight: isMobile ? 40 : 48,
    width: '100%',
    maxWidth: isMobile ? '100%' : 420,
    boxSizing: 'border-box',
    transform: isOverlay
      ? `${CSS.Transform.toString(transform)} rotateY(8deg) rotateX(2deg) scale(1.09)`
      : CSS.Transform.toString(transform),
    transition: 'box-shadow 0.12s, transform 0.12s',
    opacity: isOverlay ? 1 : isDragging ? 0.85 : 1,
    background: from === 'left' ? '#f9fafb' : '#f0fdf4',
    marginBottom: isMobile ? 6 : 8,
    padding: isMobile ? 8 : 12,
    borderRadius: 10,
    border: selected ? '2px solid #a78bfa' : (from === 'left' ? '1px solid #ddd' : '1px solid #22c55e'),
    cursor: 'move',
    display: 'flex',
    alignItems: 'center',
    gap: isMobile ? 6 : 8,
    zIndex: isOverlay ? 99999 : isDragging ? 9999 : 'auto',
    boxShadow: selected ? '0 0 0 2px #a78bfa' : (isOverlay
      ? '0 0 0 4px #a78bfa, 0 0 16px 4px #a78bfa55, 0 4px 16px 0 rgba(168,139,250,0.18), 0 1px 4px 0 rgba(168,139,250,0.10), inset 0 1px 4px #a78bfa22'
      : isDragging
        ? '0 0 0 2px #a78bfa, 0 4px 16px 0 rgba(168,139,250,0.10)'
        : 'none'),
    willChange: 'transform, box-shadow, opacity',
    scale: isOverlay || isDragging ? 1.09 : 1,
    position: isOverlay || isDragging ? 'relative' : 'static',
    animation: isOverlay
      ? 'bounce-drag 0.22s cubic-bezier(.68,-0.55,.27,1.55) 1, gradient-glow 1.2s ease-in-out infinite, vibrate-purple 0.22s linear infinite'
      : justDropped
        ? 'flash-card 0.7s linear 1'
        : isDragging
          ? 'vibrate-purple 0.25s linear infinite'
          : 'none',
    pointerEvents: isOverlay ? 'none' : 'auto',
  };

  const handleDoubleClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (onDoubleClick && !isOverlay) {
      onDoubleClick(id, from);
    }
  };

  return (
    <div 
      ref={setNodeRef} 
      style={style} 
      {...attributes} 
      {...listeners}
      onDoubleClick={handleDoubleClick}
    >
      <span className={`font-mono ${isMobile ? 'text-xs' : 'text-sm'} text-blue-900 dark:text-blue-300`}>{card.number}</span>
      <span className={`${isMobile ? 'text-xs' : 'text-xs'} text-gray-500 dark:text-gray-400 truncate`}>{card.name}</span>
      <span className={`${isMobile ? 'text-xs' : 'text-xs'} text-gray-400 dark:text-gray-500`}>({card.card_type?.name || 'Type'})</span>
      <style>{`
        @keyframes vibrate-purple {
          0% { transform: translate(0, 0) scale(1.09) rotateY(8deg) rotateX(2deg); }
          20% { transform: translate(-1.5px, 1.5px) scale(1.09) rotateY(8deg) rotateX(2deg); }
          40% { transform: translate(-1.5px, -1.5px) scale(1.09) rotateY(8deg) rotateX(2deg); }
          60% { transform: translate(1.5px, 1.5px) scale(1.09) rotateY(8deg) rotateX(2deg); }
          80% { transform: translate(1.5px, -1.5px) scale(1.09) rotateY(8deg) rotateX(2deg); }
          100% { transform: translate(0, 0) scale(1.09) rotateY(8deg) rotateX(2deg); }
        }
        @keyframes gradient-glow {
          0% { box-shadow: 0 0 0 4px #a78bfa, 0 0 16px 4px #a78bfa55; }
          50% { box-shadow: 0 0 0 8px #c4b5fd, 0 0 24px 8px #a78bfa99; }
          100% { box-shadow: 0 0 0 4px #a78bfa, 0 0 16px 4px #a78bfa55; }
        }
        @keyframes bounce-drag {
          0% { transform: scale(1) rotateY(8deg) rotateX(2deg); }
          30% { transform: scale(1.13) rotateY(8deg) rotateX(2deg); }
          60% { transform: scale(1.09) rotateY(8deg) rotateX(2deg); }
          100% { transform: scale(1.09) rotateY(8deg) rotateX(2deg); }
        }
        @keyframes flash-card {
          0% { box-shadow: 0 0 0 0 #a78bfa, 0 0 16px 4px #a78bfa55; }
          30% { box-shadow: 0 0 0 6px #fbbf24, 0 0 24px 8px #a78bfa99; }
          60% { box-shadow: 0 0 0 4px #a78bfa, 0 0 16px 4px #a78bfa55; }
          100% { box-shadow: 0 0 0 0 #a78bfa, 0 0 16px 4px #a78bfa55; }
        }
      `}</style>
    </div>
  );
}

function DroppableContainer({id, children, style, justDropped}) {
  const {setNodeRef} = useDroppable({id});
  return (
    <div
      ref={setNodeRef}
      id={id}
      style={{
        ...style,
        transition: 'box-shadow 0.12s, border-color 0.12s, background 0.12s',
        willChange: 'box-shadow, border-color, background',
        zIndex: id === 'right-list' ? 100 : 'auto',
        pointerEvents: 'auto',
      }}
    >
      {children}
      {id === 'right-list' && justDropped && (
        <span style={{
          position: 'absolute',
          left: '50%',
          top: '50%',
          width: 70,
          height: 70,
          background: 'radial-gradient(circle, #a78bfa55 0%, #a78bfa00 80%)',
          borderRadius: '50%',
          transform: 'translate(-50%, -50%)',
          pointerEvents: 'none',
          animation: 'ripple-effect 0.5s cubic-bezier(.4,0,.2,1) 1',
          zIndex: 1000
        }} />
      )}
      <style>{`
        @keyframes ripple-effect {
          0% { opacity: 0.7; transform: translate(-50%, -50%) scale(0.7); }
          60% { opacity: 0.4; transform: translate(-50%, -50%) scale(1.1); }
          100% { opacity: 0; transform: translate(-50%, -50%) scale(1.5); }
        }
      `}</style>
    </div>
  );
}

// PropTypes for components
CardsDnDSection.propTypes = {
  leftCards: PropTypes.array.isRequired,
  setLeftCards: PropTypes.func.isRequired,
  rightCards: PropTypes.array.isRequired,
  setRightCards: PropTypes.func.isRequired,
  cardsLoading: PropTypes.bool.isRequired,
  assignLoading: PropTypes.bool.isRequired,
  handleAssignCards: PropTypes.func.isRequired,
  isMobile: PropTypes.bool.isRequired
};

DraggableCard.propTypes = {
  card: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    number: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    name: PropTypes.string,
    card_type: PropTypes.shape({
      name: PropTypes.string
    })
  }).isRequired,
  id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  from: PropTypes.string.isRequired,
  isOverlay: PropTypes.bool,
  justDropped: PropTypes.bool,
  selected: PropTypes.bool,
  isMobile: PropTypes.bool,
  onDoubleClick: PropTypes.func
};

DroppableContainer.propTypes = {
  id: PropTypes.string.isRequired,
  children: PropTypes.node,
  style: PropTypes.object,
  justDropped: PropTypes.bool
};

ManagerDetailModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onHide: PropTypes.func.isRequired,
  selectedManager: PropTypes.object,
  managerActivePackages: PropTypes.object,
  loadingManagerPackages: PropTypes.bool,
  managerPackagesError: PropTypes.string,
  managerPackageHistory: PropTypes.array,
  loadingManagerPackageHistory: PropTypes.bool,
  managerPackageHistoryError: PropTypes.string,
  onGiftPackage: PropTypes.func,
  onCreateCard: PropTypes.func,
  onEditManager: PropTypes.func,
  onDeleteManager: PropTypes.func,
  leftCards: PropTypes.array,
  setLeftCards: PropTypes.func,
  rightCards: PropTypes.array,
  setRightCards: PropTypes.func,
  cardsLoading: PropTypes.bool,
  assignLoading: PropTypes.bool,
  handleAssignCards: PropTypes.func,
  cardsFetched: PropTypes.bool
};

export default ManagerDetailModal;
