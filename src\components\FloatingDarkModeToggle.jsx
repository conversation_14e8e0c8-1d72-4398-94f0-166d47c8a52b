import React, { useState, useEffect } from 'react';
import { useDarkMode } from '@contexts/DarkModeContext';

const FloatingDarkModeToggle = () => {
  const { isDarkMode, toggleDarkMode } = useDarkMode();
  const [isAnimating, setIsAnimating] = useState(false);
  const [isVisible, setIsVisible] = useState(true);

  const handleToggle = () => {
    setIsAnimating(true);
    toggleDarkMode();
    
    // إزالة تأثير التحريك بعد انتهاءه
    setTimeout(() => {
      setIsAnimating(false);
    }, 300);
  };

    return (
        <div className="fixed bottom-6 right-8 z-50">
            <div className="relative group">
                {/* Shadow Base */}
                <div className={`absolute inset-0 rounded-full blur-2xl scale-125 -z-10 translate-y-2 ${
                    isDarkMode ? 'bg-blue-500/40' : 'bg-yellow-500/40'
                }`}></div>
                <div className="absolute inset-0 rounded-full bg-black/30 dark:bg-black/50 blur-xl scale-120 -z-10 translate-y-1"></div>
                <div className="absolute inset-0 rounded-full bg-black/20 dark:bg-black/40 blur-lg scale-110 -z-10 translate-y-0.5"></div>
        {/* Tooltip */}
        <div className="absolute bottom-full right-0 mb-3 px-3 py-2 bg-gray-800 dark:bg-gray-700 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap shadow-lg">
          {isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
          <div className="absolute top-full right-4 border-4 border-transparent border-t-gray-800 dark:border-t-gray-700"></div>
        </div>

        {/* Professional Reflection */}
        <div className="absolute -bottom-12 left-1/2 transform -translate-x-1/2 w-20 h-16 opacity-70">
          {/* Main Reflection */}
          <div className={`
            absolute inset-0 rounded-full
            ${isDarkMode 
              ? 'bg-gradient-to-t from-blue-600/90 via-blue-500/70 to-transparent' 
              : 'bg-gradient-to-t from-yellow-400/90 via-orange-400/70 to-transparent'
            }
            blur-lg
            scale-y-35
            transform rotate-180
            filter drop-shadow-lg
          `}></div>
          
          {/* Secondary Reflection Layer */}
          <div className={`
            absolute inset-0 rounded-full
            ${isDarkMode 
              ? 'bg-gradient-to-t from-blue-500/60 via-blue-400/40 to-transparent' 
              : 'bg-gradient-to-t from-yellow-300/60 via-orange-300/40 to-transparent'
            }
            blur-xl
            scale-y-30
            scale-x-110
            transform rotate-180
            translate-y-1
          `}></div>
          
          {/* Reflection Glow */}
          <div className={`
            absolute inset-0 rounded-full
            ${isDarkMode 
              ? 'bg-gradient-to-t from-blue-400/40 via-blue-300/20 to-transparent' 
              : 'bg-gradient-to-t from-yellow-200/40 via-orange-200/20 to-transparent'
            }
            blur-2xl
            scale-y-25
            scale-x-120
            transform rotate-180
            translate-y-2
          `}></div>
          
          {/* Reflection Ripple Effect */}
          <div className={`
            absolute inset-0 rounded-full
            ${isDarkMode 
              ? 'bg-gradient-radial from-blue-500/30 via-transparent to-transparent' 
              : 'bg-gradient-radial from-yellow-400/30 via-transparent to-transparent'
            }
            blur-3xl
            scale-150
            transform rotate-180
            translate-y-3
            animate-pulse
          `}></div>
        </div>

        {/* Floating Toggle Button */}
        <button
          onClick={handleToggle}
          className={`
            w-14 h-14
            relative
            rounded-full
            transition-all
            duration-500
            ease-in-out
            focus:outline-none
            focus:ring-4
            focus:ring-opacity-50
            ${isDarkMode 
              ? 'bg-gradient-to-br from-blue-600 via-blue-500 to-indigo-600 focus:ring-blue-300' 
              : 'bg-gradient-to-br from-yellow-400 via-orange-400 to-yellow-500 focus:ring-yellow-300'
            }
            ${isAnimating ? 'animate-pulse scale-110' : 'hover:scale-110'}
            shadow-[0_15px_35px_rgba(0,0,0,0.2)] 
            hover:shadow-[0_20px_45px_rgba(0,0,0,0.3)]
            dark:shadow-[0_15px_35px_rgba(0,0,0,0.5)]
            dark:hover:shadow-[0_20px_45px_rgba(0,0,0,0.7)]
            backdrop-blur-sm
            border-2
            ${isDarkMode ? 'border-blue-400/30' : 'border-yellow-300/30'}
            hover:border-opacity-60
            group
            before:absolute
            before:inset-0
            before:rounded-full
            before:bg-gradient-to-br
            before:from-transparent
            before:via-white/10
            before:to-transparent
            before:opacity-0
            before:transition-opacity
            before:duration-300
            hover:before:opacity-100
          `}
          aria-label={isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
        >
          {/* خلفية متدرجة مع تأثيرات */}
          <div className={`
            absolute inset-0 rounded-full
            ${isDarkMode 
              ? 'bg-gradient-to-br from-blue-600 via-blue-500 to-indigo-600' 
              : 'bg-gradient-to-br from-yellow-400 via-orange-400 to-yellow-500'
            }
            opacity-90
            transition-opacity duration-300
          `} />
          
          {/* النجوم في الوضع المظلم */}
          {isDarkMode && (
            <div className="absolute inset-0 rounded-full overflow-hidden">
              <div className="absolute top-2 left-3 w-1 h-1 bg-white rounded-full opacity-70 animate-pulse" />
              <div className="absolute top-4 right-4 w-0.5 h-0.5 bg-white rounded-full opacity-50 animate-pulse" style={{ animationDelay: '0.5s' }} />
              <div className="absolute bottom-3 left-5 w-0.5 h-0.5 bg-white rounded-full opacity-60 animate-pulse" style={{ animationDelay: '1s' }} />
              <div className="absolute bottom-2 right-2 w-0.5 h-0.5 bg-white rounded-full opacity-40 animate-pulse" style={{ animationDelay: '1.5s' }} />
            </div>
          )}
          
          {/* أيقونة الشمس */}
          <div className={`
            absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2
            transition-all duration-500 ease-in-out
            ${isDarkMode ? 'opacity-0 scale-0 rotate-180' : 'opacity-100 scale-100 rotate-0'}
          `}>
            <svg 
              className="w-6 h-6 text-yellow-200 drop-shadow-lg" 
              fill="currentColor" 
              viewBox="0 0 20 20"
            >
              <path 
                fillRule="evenodd" 
                d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" 
                clipRule="evenodd" 
              />
            </svg>
          </div>
          
          {/* أيقونة القمر */}
          <div className={`
            absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2
            transition-all duration-500 ease-in-out
            ${isDarkMode ? 'opacity-100 scale-100 rotate-0' : 'opacity-0 scale-0 -rotate-180'}
          `}>
            <svg 
              className="w-6 h-6 text-blue-100 drop-shadow-lg" 
              fill="currentColor" 
              viewBox="0 0 20 20"
            >
              <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
            </svg>
          </div>
          
          {/* تأثير التوهج */}
          <div className={`
            absolute inset-0 rounded-full
            ${isDarkMode 
              ? 'bg-blue-400 opacity-0 group-hover:opacity-30' 
              : 'bg-yellow-300 opacity-0 group-hover:opacity-30'
            }
            transition-opacity duration-300
            blur-sm
          `} />
        </button>
        
        {/* تأثيرات إضافية */}
        {isAnimating && (
          <div className={`
            absolute inset-0 rounded-full
            ${isDarkMode 
              ? 'bg-blue-500 opacity-40' 
              : 'bg-yellow-400 opacity-40'
            }
            animate-ping
            -z-10
          `} />
        )}
      </div>
    </div>
  );
};

export default FloatingDarkModeToggle;
