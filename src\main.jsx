import React from 'react'
import ReactDOM from 'react-dom/client'
import { I18nextProvider } from 'react-i18next';
import { Browser<PERSON>outer } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from 'react-query';

import i18n from './config/i18n';
import 'primereact/resources/themes/saga-blue/theme.css';
import 'primereact/resources/primereact.min.css';
import 'primeicons/primeicons.css';
import 'leaflet/dist/leaflet.css';

import './index.css'
import './styles/mobile-modals.css'

import RoutesContainer from './routes/RoutesContainer';
import { DataTableProvider } from './contexts/DataTableContext';
import { GlobalProvider } from './contexts/GlobalContext';
import { LayoutProvider } from './contexts/LayoutContext';
import { AuthProvider } from './contexts/AuthContext';
import { DarkModeProvider } from './contexts/DarkModeContext';

const queryClient = new QueryClient();

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <QueryClientProvider client={ queryClient }>
      <I18nextProvider i18n={ i18n }>
        <DarkModeProvider>
          <GlobalProvider>
            <LayoutProvider>
              <DataTableProvider>
                <AuthProvider>
                  <BrowserRouter>
                    <RoutesContainer />
                  </BrowserRouter>
                </AuthProvider>
              </DataTableProvider>
            </LayoutProvider>
          </GlobalProvider>
        </DarkModeProvider>
      </I18nextProvider>
    </QueryClientProvider>
  </React.StrictMode>,
)
