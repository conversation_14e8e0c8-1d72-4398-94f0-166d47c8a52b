.truncate-cell {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Compact card design styles */
.group-member-card {
  transition: all 0.3s ease;
}

.group-member-card:hover {
  transform: translateY(-2px);
}

/* Ensure card design previews are properly scaled and compact */
.card-design-preview {
  height: 60px !important;
  max-height: 60px;
  overflow: hidden;
}

.card-design-preview * {
  pointer-events: none;
}

/* Remove any extra spacing from card design containers */
.card-design-preview .w-full,
.card-design-preview div {
  margin: 0 !important;
  padding: 0 !important;
}

/* Compact spacing for mobile */
@media (max-width: 768px) {
  .group-member-card {
    margin-bottom: 0.75rem;
  }
}