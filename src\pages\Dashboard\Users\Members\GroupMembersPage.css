.truncate-cell {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Compact card design styles */
.group-member-card {
  transition: all 0.3s ease;
}

.group-member-card:hover {
  transform: translateY(-2px);
}

/* Ensure card design previews are properly scaled */
.card-design-preview {
  max-height: 80px;
  overflow: hidden;
}

.card-design-preview * {
  pointer-events: none;
}

/* Compact spacing for mobile */
@media (max-width: 768px) {
  .group-member-card {
    margin-bottom: 0.75rem;
  }
}