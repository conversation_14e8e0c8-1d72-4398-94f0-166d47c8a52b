import { useDesignSpace } from "@contexts/DesignSpaceContext";
import { useState } from "react";
import { useDarkMode } from '@contexts/DarkModeContext';

const icons = [
  // الأيقونات القديمة
  "https://file-tech-test.livaatverse.com/uploads/file---1733395522.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395672.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395711.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395786.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396147.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396169.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395845.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395869.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395897.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395925.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395957.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396014.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396030.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396048.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396065.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396086.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396110.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396128.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396344.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396361.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396381.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396398.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395975.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395993.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396212.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396261.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396229.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396415.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395813.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396432.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396279.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396294.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396320.svg",
  "https://unpkg.com/feather-icons/dist/icons/activity.svg",
  "https://unpkg.com/feather-icons/dist/icons/airplay.svg",
  "https://unpkg.com/feather-icons/dist/icons/alert-circle.svg",
  "https://unpkg.com/feather-icons/dist/icons/alert-octagon.svg",
  "https://unpkg.com/feather-icons/dist/icons/alert-triangle.svg",
  "https://unpkg.com/feather-icons/dist/icons/anchor.svg",
  "https://unpkg.com/feather-icons/dist/icons/aperture.svg",
  "https://unpkg.com/feather-icons/dist/icons/archive.svg",
  "https://unpkg.com/feather-icons/dist/icons/award.svg",
  "https://unpkg.com/feather-icons/dist/icons/at-sign.svg",
  "https://unpkg.com/feather-icons/dist/icons/bar-chart-2.svg",
  "https://unpkg.com/feather-icons/dist/icons/bar-chart.svg",
  "https://unpkg.com/feather-icons/dist/icons/battery.svg",
  "https://unpkg.com/feather-icons/dist/icons/bell.svg",
  "https://unpkg.com/feather-icons/dist/icons/bluetooth.svg",
  "https://unpkg.com/feather-icons/dist/icons/book-open.svg",
  "https://unpkg.com/feather-icons/dist/icons/book.svg",
  "https://unpkg.com/feather-icons/dist/icons/bookmark.svg",
  "https://unpkg.com/feather-icons/dist/icons/box.svg",
  "https://unpkg.com/feather-icons/dist/icons/briefcase.svg",
  "https://unpkg.com/feather-icons/dist/icons/calendar.svg",
  "https://unpkg.com/feather-icons/dist/icons/camera.svg",
  "https://unpkg.com/feather-icons/dist/icons/cast.svg",
  "https://unpkg.com/feather-icons/dist/icons/check-circle.svg",
  "https://unpkg.com/feather-icons/dist/icons/check-square.svg",
  "https://unpkg.com/feather-icons/dist/icons/chevron-down.svg",
  "https://unpkg.com/feather-icons/dist/icons/chevron-left.svg",
  "https://unpkg.com/feather-icons/dist/icons/chevron-right.svg",
  "https://unpkg.com/feather-icons/dist/icons/chevron-up.svg",
  "https://unpkg.com/feather-icons/dist/icons/clipboard.svg",
  "https://unpkg.com/feather-icons/dist/icons/clock.svg",
  "https://unpkg.com/feather-icons/dist/icons/cloud-drizzle.svg",
  "https://unpkg.com/feather-icons/dist/icons/cloud-lightning.svg",
  "https://unpkg.com/feather-icons/dist/icons/cloud-off.svg",
  "https://unpkg.com/feather-icons/dist/icons/cloud-rain.svg",
  "https://unpkg.com/feather-icons/dist/icons/cloud-snow.svg",
  "https://unpkg.com/feather-icons/dist/icons/cloud.svg",
  "https://unpkg.com/feather-icons/dist/icons/code.svg",
  "https://unpkg.com/feather-icons/dist/icons/codepen.svg",
  "https://unpkg.com/feather-icons/dist/icons/command.svg",
  "https://unpkg.com/feather-icons/dist/icons/compass.svg",
  "https://unpkg.com/feather-icons/dist/icons/cpu.svg",
  "https://unpkg.com/feather-icons/dist/icons/crosshair.svg",
  "https://unpkg.com/feather-icons/dist/icons/database.svg",
  "https://unpkg.com/feather-icons/dist/icons/disc.svg",
  "https://unpkg.com/feather-icons/dist/icons/dollar-sign.svg",
  "https://unpkg.com/feather-icons/dist/icons/download-cloud.svg",
  "https://unpkg.com/feather-icons/dist/icons/download.svg",
  "https://unpkg.com/feather-icons/dist/icons/droplet.svg",
  "https://unpkg.com/feather-icons/dist/icons/edit-2.svg",
  "https://unpkg.com/feather-icons/dist/icons/edit-3.svg",
  "https://unpkg.com/feather-icons/dist/icons/edit.svg",
  "https://unpkg.com/feather-icons/dist/icons/external-link.svg",
  "https://unpkg.com/feather-icons/dist/icons/eye-off.svg",
  "https://unpkg.com/feather-icons/dist/icons/eye.svg",
  "https://unpkg.com/feather-icons/dist/icons/feather.svg",
  "https://unpkg.com/feather-icons/dist/icons/file-minus.svg",
  "https://unpkg.com/feather-icons/dist/icons/file-plus.svg",
  "https://unpkg.com/feather-icons/dist/icons/file-text.svg",
  "https://unpkg.com/feather-icons/dist/icons/file.svg",
  "https://unpkg.com/feather-icons/dist/icons/film.svg",
  "https://unpkg.com/feather-icons/dist/icons/filter.svg",
  "https://unpkg.com/feather-icons/dist/icons/flag.svg",
  "https://unpkg.com/feather-icons/dist/icons/folder-minus.svg",
  "https://unpkg.com/feather-icons/dist/icons/folder-plus.svg",
  "https://unpkg.com/feather-icons/dist/icons/folder.svg",
  "https://unpkg.com/feather-icons/dist/icons/frown.svg",
  "https://unpkg.com/feather-icons/dist/icons/gift.svg",
  "https://unpkg.com/feather-icons/dist/icons/git-branch.svg",
  "https://unpkg.com/feather-icons/dist/icons/git-commit.svg",
  "https://unpkg.com/feather-icons/dist/icons/git-merge.svg",
  "https://unpkg.com/feather-icons/dist/icons/git-pull-request.svg",
  "https://unpkg.com/feather-icons/dist/icons/github.svg",
  "https://unpkg.com/feather-icons/dist/icons/gitlab.svg",
  "https://unpkg.com/feather-icons/dist/icons/globe.svg",
  "https://unpkg.com/feather-icons/dist/icons/grid.svg",
  "https://unpkg.com/feather-icons/dist/icons/hard-drive.svg",
  "https://unpkg.com/feather-icons/dist/icons/hash.svg",
  "https://unpkg.com/feather-icons/dist/icons/headphones.svg",
  "https://unpkg.com/feather-icons/dist/icons/heart.svg",
  "https://unpkg.com/feather-icons/dist/icons/help-circle.svg",
  "https://unpkg.com/feather-icons/dist/icons/home.svg",
  "https://unpkg.com/feather-icons/dist/icons/image.svg",
  "https://unpkg.com/feather-icons/dist/icons/inbox.svg",
  "https://unpkg.com/feather-icons/dist/icons/info.svg",
  "https://unpkg.com/feather-icons/dist/icons/key.svg",
  "https://unpkg.com/feather-icons/dist/icons/layers.svg",
  "https://unpkg.com/feather-icons/dist/icons/layout.svg",
  "https://unpkg.com/feather-icons/dist/icons/life-buoy.svg",
  "https://unpkg.com/feather-icons/dist/icons/link-2.svg",
  "https://unpkg.com/feather-icons/dist/icons/link.svg",
  "https://unpkg.com/feather-icons/dist/icons/list.svg",
  "https://unpkg.com/feather-icons/dist/icons/loader.svg",
  "https://unpkg.com/feather-icons/dist/icons/lock.svg",
  "https://unpkg.com/feather-icons/dist/icons/log-in.svg",
  "https://unpkg.com/feather-icons/dist/icons/log-out.svg",
  "https://unpkg.com/feather-icons/dist/icons/mail.svg",
  "https://unpkg.com/feather-icons/dist/icons/map-pin.svg",
  "https://unpkg.com/feather-icons/dist/icons/map.svg",
  "https://unpkg.com/feather-icons/dist/icons/maximize-2.svg",
  "https://unpkg.com/feather-icons/dist/icons/maximize.svg",
  "https://unpkg.com/feather-icons/dist/icons/meh.svg",
  "https://unpkg.com/feather-icons/dist/icons/menu.svg",
  "https://unpkg.com/feather-icons/dist/icons/message-circle.svg",
  "https://unpkg.com/feather-icons/dist/icons/message-square.svg",
  "https://unpkg.com/feather-icons/dist/icons/mic-off.svg",
  "https://unpkg.com/feather-icons/dist/icons/mic.svg",
  "https://unpkg.com/feather-icons/dist/icons/minimize-2.svg",
  "https://unpkg.com/feather-icons/dist/icons/minimize.svg",
  "https://unpkg.com/feather-icons/dist/icons/moon.svg",
  "https://unpkg.com/feather-icons/dist/icons/more-horizontal.svg",
  "https://unpkg.com/feather-icons/dist/icons/more-vertical.svg",
  "https://unpkg.com/feather-icons/dist/icons/mouse-pointer.svg",
  "https://unpkg.com/feather-icons/dist/icons/move.svg",
  "https://unpkg.com/feather-icons/dist/icons/music.svg",
  "https://unpkg.com/feather-icons/dist/icons/navigation-2.svg",
  "https://unpkg.com/feather-icons/dist/icons/navigation.svg",
  "https://unpkg.com/feather-icons/dist/icons/octagon.svg",
  "https://unpkg.com/feather-icons/dist/icons/package.svg",
  "https://unpkg.com/feather-icons/dist/icons/paperclip.svg",
  "https://unpkg.com/feather-icons/dist/icons/pause-circle.svg",
  "https://unpkg.com/feather-icons/dist/icons/pause.svg",
  "https://unpkg.com/feather-icons/dist/icons/pen-tool.svg",
  "https://unpkg.com/feather-icons/dist/icons/percent.svg",
  "https://unpkg.com/feather-icons/dist/icons/phone-call.svg",
  "https://unpkg.com/feather-icons/dist/icons/phone-forwarded.svg",
  "https://unpkg.com/feather-icons/dist/icons/phone-incoming.svg",
  "https://unpkg.com/feather-icons/dist/icons/phone-missed.svg",
  "https://unpkg.com/feather-icons/dist/icons/phone-off.svg",
  "https://unpkg.com/feather-icons/dist/icons/phone-outgoing.svg",
  "https://unpkg.com/feather-icons/dist/icons/phone.svg",
  "https://unpkg.com/feather-icons/dist/icons/pie-chart.svg",
  "https://unpkg.com/feather-icons/dist/icons/play-circle.svg",
  "https://unpkg.com/feather-icons/dist/icons/play.svg",
  "https://unpkg.com/feather-icons/dist/icons/plus-circle.svg",
  "https://unpkg.com/feather-icons/dist/icons/plus-square.svg",
  "https://unpkg.com/feather-icons/dist/icons/plus.svg",
  "https://unpkg.com/feather-icons/dist/icons/pocket.svg",
  "https://unpkg.com/feather-icons/dist/icons/power.svg",
  "https://unpkg.com/feather-icons/dist/icons/printer.svg",
  "https://unpkg.com/feather-icons/dist/icons/radio.svg",
  "https://unpkg.com/feather-icons/dist/icons/refresh-ccw.svg",
  "https://unpkg.com/feather-icons/dist/icons/refresh-cw.svg",
  "https://unpkg.com/feather-icons/dist/icons/repeat.svg",
  "https://unpkg.com/feather-icons/dist/icons/rewind.svg",
  "https://unpkg.com/feather-icons/dist/icons/rotate-ccw.svg",
  "https://unpkg.com/feather-icons/dist/icons/rotate-cw.svg",
  "https://unpkg.com/feather-icons/dist/icons/rss.svg",
  "https://unpkg.com/feather-icons/dist/icons/save.svg",
  "https://unpkg.com/feather-icons/dist/icons/scissors.svg",
  "https://unpkg.com/feather-icons/dist/icons/search.svg",
  "https://unpkg.com/feather-icons/dist/icons/send.svg",
  "https://unpkg.com/feather-icons/dist/icons/server.svg",
  "https://unpkg.com/feather-icons/dist/icons/settings.svg",
  "https://unpkg.com/feather-icons/dist/icons/share-2.svg",
  "https://unpkg.com/feather-icons/dist/icons/share.svg",
  "https://unpkg.com/feather-icons/dist/icons/shield-off.svg",
  "https://unpkg.com/feather-icons/dist/icons/shield.svg",
  "https://unpkg.com/feather-icons/dist/icons/shopping-bag.svg",
  "https://unpkg.com/feather-icons/dist/icons/shopping-cart.svg",
  "https://unpkg.com/feather-icons/dist/icons/shuffle.svg",
  "https://unpkg.com/feather-icons/dist/icons/sidebar.svg",
  "https://unpkg.com/feather-icons/dist/icons/skip-back.svg",
  "https://unpkg.com/feather-icons/dist/icons/skip-forward.svg",
  "https://unpkg.com/feather-icons/dist/icons/slack.svg",
  "https://unpkg.com/feather-icons/dist/icons/slash.svg",
  "https://unpkg.com/feather-icons/dist/icons/sliders.svg",
  "https://unpkg.com/feather-icons/dist/icons/smartphone.svg",
  "https://unpkg.com/feather-icons/dist/icons/smile.svg",
  "https://unpkg.com/feather-icons/dist/icons/speaker.svg",
  "https://unpkg.com/feather-icons/dist/icons/square.svg",
  "https://unpkg.com/feather-icons/dist/icons/star.svg",
  "https://unpkg.com/feather-icons/dist/icons/stop-circle.svg",
  "https://unpkg.com/feather-icons/dist/icons/sun.svg",
  "https://unpkg.com/feather-icons/dist/icons/sunrise.svg",
  "https://unpkg.com/feather-icons/dist/icons/sunset.svg",
  "https://unpkg.com/feather-icons/dist/icons/tablet.svg",
  "https://unpkg.com/feather-icons/dist/icons/tag.svg",
  "https://unpkg.com/feather-icons/dist/icons/target.svg",
  "https://unpkg.com/feather-icons/dist/icons/terminal.svg",
  "https://unpkg.com/feather-icons/dist/icons/thermometer.svg",
  "https://unpkg.com/feather-icons/dist/icons/thumbs-down.svg",
  "https://unpkg.com/feather-icons/dist/icons/thumbs-up.svg",
  "https://unpkg.com/feather-icons/dist/icons/toggle-left.svg",
  "https://unpkg.com/feather-icons/dist/icons/toggle-right.svg",
  "https://unpkg.com/feather-icons/dist/icons/tool.svg",
  "https://unpkg.com/feather-icons/dist/icons/trash-2.svg",
  "https://unpkg.com/feather-icons/dist/icons/trash.svg",
  "https://unpkg.com/feather-icons/dist/icons/trello.svg",
  "https://unpkg.com/feather-icons/dist/icons/trending-down.svg",
  "https://unpkg.com/feather-icons/dist/icons/trending-up.svg",
  "https://unpkg.com/feather-icons/dist/icons/triangle.svg",
  "https://unpkg.com/feather-icons/dist/icons/truck.svg",
  "https://unpkg.com/feather-icons/dist/icons/tv.svg",
  "https://unpkg.com/feather-icons/dist/icons/twitter.svg",
  "https://unpkg.com/feather-icons/dist/icons/type.svg",
  "https://unpkg.com/feather-icons/dist/icons/umbrella.svg",
  "https://unpkg.com/feather-icons/dist/icons/underline.svg",
  "https://unpkg.com/feather-icons/dist/icons/unlock.svg",
  "https://unpkg.com/feather-icons/dist/icons/upload-cloud.svg",
  "https://unpkg.com/feather-icons/dist/icons/upload.svg",
  "https://unpkg.com/feather-icons/dist/icons/user-check.svg",
  "https://unpkg.com/feather-icons/dist/icons/user-minus.svg",
  "https://unpkg.com/feather-icons/dist/icons/user-plus.svg",
  "https://unpkg.com/feather-icons/dist/icons/user-x.svg",
  "https://unpkg.com/feather-icons/dist/icons/user.svg",
  "https://unpkg.com/feather-icons/dist/icons/users.svg",
  "https://unpkg.com/feather-icons/dist/icons/video-off.svg",
  "https://unpkg.com/feather-icons/dist/icons/video.svg",
  "https://unpkg.com/feather-icons/dist/icons/voicemail.svg",
  "https://unpkg.com/feather-icons/dist/icons/volume-1.svg",
  "https://unpkg.com/feather-icons/dist/icons/volume-2.svg",
  "https://unpkg.com/feather-icons/dist/icons/volume-x.svg",
  "https://unpkg.com/feather-icons/dist/icons/volume.svg",
  "https://unpkg.com/feather-icons/dist/icons/watch.svg",
  "https://unpkg.com/feather-icons/dist/icons/wifi-off.svg",
  "https://unpkg.com/feather-icons/dist/icons/wifi.svg",
  "https://unpkg.com/feather-icons/dist/icons/wind.svg",
  "https://unpkg.com/feather-icons/dist/icons/x-circle.svg",
  "https://unpkg.com/feather-icons/dist/icons/x-octagon.svg",
  "https://unpkg.com/feather-icons/dist/icons/x-square.svg",
  "https://unpkg.com/feather-icons/dist/icons/x.svg",
  "https://unpkg.com/feather-icons/dist/icons/youtube.svg",
  // يمكنك إضافة المزيد من أيقونات SVG من مصادر مثل https://iconoir.com/ أو https://iconmonstr.com/ أو https://icons.getbootstrap.com/
];

const iconColors = [
  '#000000', '#ffffff', '#FF0000', '#FF8000', '#FFFF00', '#80FF00', '#00FF00', '#00FF80', '#00FFFF', '#0080FF', '#0000FF', '#8000FF', '#FF00FF', '#FF0080',
  '#FF6B6B', '#FF8E53', '#FFA726', '#FFB74D', '#FFCC02', '#FFD54F', '#FFE082', '#FFECB3', '#FFF3E0', '#FFAB91', '#FF8A65', '#FF7043',
  '#4FC3F7', '#29B6F6', '#03A9F4', '#039BE5', '#0288D1', '#0277BD', '#01579B', '#E1F5FE', '#B3E5FC', '#81D4FA', '#8BC34A', '#CDDC39', '#FFEB3B', '#FF9800', '#FF5722', '#795548', '#9E9E9E', '#607D8B', '#3F51B5', '#2196F3', '#00BCD4', '#009688', '#FF1744', '#F50057', '#D500F9', '#651FFF', '#3D5AFE', '#2979FF', '#00B0FF', '#00E5FF', '#1DE9B6', '#00E676', '#76FF03', '#C6FF00'
];

function replaceSvgColor(svgText, color) {
  // فقط استبدل fill أو stroke في العناصر الداخلية إذا كانت موجودة وليست none/white/transparent
  svgText = svgText.replace(
    /(fill|stroke)="(?!none|white|#fff(?:fff)?|transparent)([^"]*)"/gi,
    `$1="${color}`
  );
  return svgText;
}

function IconsSettings() {
  const { addElement } = useDesignSpace();
  const { isDarkMode } = useDarkMode();
  const [iconColor, setIconColor] = useState('#000000');

  const handleAddIcon = async (iconUrl) => {
    try {
      const res = await fetch(iconUrl);
      let svgText = await res.text();
      svgText = replaceSvgColor(svgText, iconColor);
      svgText = svgText.replace(
        /<svg([^>]*)>/i,
        (match, attrs) => {
          let newAttrs = attrs;
          if (!/viewBox=/i.test(attrs)) {
            newAttrs += ' viewBox="0 0 24 24"';
          }
          newAttrs = newAttrs.replace(/width="[^"]*"/i, 'width="100"');
          newAttrs = newAttrs.replace(/height="[^"]*"/i, 'height="100"');
          if (!/width=/i.test(newAttrs)) newAttrs += ' width="100"';
          if (!/height=/i.test(newAttrs)) newAttrs += ' height="100"';
          return `<svg${newAttrs}>`;
        }
      );
      addElement('svg', svgText, {
        width: 100,
        height: 100,
        style: {},
        attributes: {}
      });
    } catch (e) {
      alert('error  fitch svg icon ');
    }
  };

  return (
    <div className={`flex flex-col items-center w-full ${isDarkMode ? 'text-gray-100' : ''}`}>
      {/* Color Picker */}
      <div className="flex flex-wrap gap-2 mb-4 items-center justify-center">
        {iconColors.map((color) => (
          <button
            key={color}
            type="button"
            className={`w-7 h-7 rounded-full border-2 transition-all ${iconColor === color ? 'border-blue-600 scale-110' : (isDarkMode ? 'border-gray-600' : 'border-gray-300')}`}
            style={{ backgroundColor: color }}
            onClick={() => setIconColor(color)}
            aria-label={`Select color ${color}`}
          />
        ))}
        <input
          type="color"
          value={iconColor}
          onChange={e => setIconColor(e.target.value)}
          className={`w-8 h-8 rounded border ml-2 cursor-pointer ${isDarkMode ? 'bg-gray-800 border-gray-600' : ''}`}
          aria-label="Custom color"
        />
        <input
          type="text"
          value={iconColor}
          onChange={e => setIconColor(e.target.value)}
          className={`w-20 ml-2 px-2 py-1 border rounded text-xs font-mono ${isDarkMode ? 'bg-gray-800 border-gray-600 text-gray-100 placeholder-gray-400' : ''}`}
          placeholder="#RRGGBB"
        />
      </div>
      {/* Icons Grid */}
      <div className="flex flex-wrap justify-start mx-auto">
        {icons.map((icon, index) => {
          return (
            <img
              loading="lazy"
              className="m-3 cursor-pointer"
              width={"30px"}
              key={index}
              src={icon}
              onClick={() => handleAddIcon(icon)}
              title="click here to select the icon color"
              style={{ 
                filter: isDarkMode 
                  ? `brightness(0) invert(1) drop-shadow(0 0 0 ${iconColor})` 
                  : `drop-shadow(0 0 0 ${iconColor})` 
              }}
            />
          );
        })}
      </div>
    </div>
  );
}

export default IconsSettings;