import React, { useEffect, useState } from 'react';
import { useDesignSpace } from '@contexts/DesignSpaceContext';
import { useDarkMode } from '@contexts/DarkModeContext';
import white_logo from "@images/white_logo.svg";
import {
    IoShapesOutline,
    IoColorPaletteOutline,
    IoImageOutline,
    IoTextOutline,
    IoLayersOutline,
    IoGridOutline
} from 'react-icons/io5';
import {
    FaPencilRuler,
    FaPalette,
    FaFont,
    FaImage
} from 'react-icons/fa';
import {
    RxText,
    RxImage,
    RxPencil2
} from 'react-icons/rx';

const DesignSpaceBackground = () => {
    const { cardType } = useDesignSpace();
    const { isDarkMode } = useDarkMode();
    const [particles, setParticles] = useState([]);
    const [beams, setBeams] = useState([]);
    const [designElements, setDesignElements] = useState([]);
    const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

    // Generate static particles for the background
    useEffect(() => {
        const generateParticles = () => {
            const newParticles = [];
            const colors = ['gray', 'slate', 'zinc', 'neutral', 'stone'];

            for (let i = 0; i < 10; i++) {
                newParticles.push({
                    id: i,
                    x: Math.random() * 100,
                    y: Math.random() * 100,
                    size: 20 + Math.random() * 40,
                    color: colors[Math.floor(Math.random() * colors.length)]
                });
            }

            setParticles(newParticles);
        };

        const generateBeams = () => {
            const newBeams = [];

            for (let i = 0; i < 5; i++) {
                newBeams.push({
                    id: i,
                    x: 10 + Math.random() * 80,
                    rotation: -45 + Math.random() * 90,
                    opacity: 0.05 + Math.random() * 0.1
                });
            }

            setBeams(newBeams);
        };

        // Generate static design elements
        const generateDesignElements = () => {
            const newElements = [];
            const icons = [
                <IoShapesOutline size={20} />,
                <IoColorPaletteOutline size={20} />,
                <IoImageOutline size={20} />,
                <IoTextOutline size={20} />,
                <IoLayersOutline size={20} />,
                <IoGridOutline size={20} />,
                <RxText size={20} />,
                <RxImage size={20} />,
                <RxPencil2 size={20} />,
                <FaPencilRuler size={20} />,
                <FaPalette size={20} />,
                <FaFont size={20} />,
                <FaImage size={20} />
            ];

            // Generate 30 static design elements
            for (let i = 0; i < 30; i++) {
                const x = Math.random() * 100; // % position
                const y = Math.random() * 100; // % position
                const size = Math.random() * 0.5 + 0.5; // scale factor 0.5-1.0
                const opacity = Math.random() * 0.15 + 0.05; // 0.05-0.2
                const rotation = Math.random() * 360; // 0-360 degrees
                const iconIndex = Math.floor(Math.random() * icons.length);

                newElements.push({
                    id: `design-element-${i}`,
                    x,
                    y,
                    size,
                    opacity,
                    rotation,
                    icon: icons[iconIndex]
                });
            }

            setDesignElements(newElements);
        };

        generateParticles();
        generateBeams();
        generateDesignElements();
    }, []);

    // Mobile detection useEffect
    useEffect(() => {
        const handleResize = () => {
            setIsMobile(window.innerWidth < 768);
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    return (
        <div className="absolute inset-0 overflow-hidden stone-wall-background">
            {/* Static Particles */}
            <div className="absolute inset-0 opacity-10">
                {particles.map(particle => (
                    <div
                        key={particle.id}
                        className="absolute rounded-full filter blur-3xl"
                        style={{
                            top: `${particle.y}%`,
                            left: `${particle.x}%`,
                            width: `${particle.size}px`,
                            height: `${particle.size}px`,
                            backgroundColor: particle.color === 'gray' ? '#9ca3af' :
                                            particle.color === 'slate' ? '#64748b' :
                                            particle.color === 'zinc' ? '#71717a' :
                                            particle.color === 'neutral' ? '#737373' :
                                            '#78716c', // stone
                            opacity: isDarkMode ? 0.4 : 0.7 // More subtle in dark mode
                        }}
                    ></div>
                ))}
            </div>

            {/* Static Light Beams */}
            <div className="absolute inset-0 opacity-10">
                {beams.map(beam => (
                    <div
                        key={beam.id}
                        className="absolute top-0 w-1 h-full"
                        style={{
                            left: `${beam.x}%`,
                            transform: `rotate(${beam.rotation}deg)`,
                            filter: 'blur(8px) brightness(1.1)',
                            opacity: beam.opacity,
                            backgroundColor: isDarkMode ? '#9ca3af' : '#ffffff'
                        }}
                    ></div>
                ))}
            </div>

            {/* Static Gradient Overlay */}
            <div
                className="absolute inset-0 opacity-10"
                style={{
                    background: isDarkMode 
                        ? 'linear-gradient(45deg, rgba(55, 65, 81, 0.18), rgba(75, 85, 99, 0.18), rgba(107, 114, 128, 0.18))'
                        : 'linear-gradient(45deg, rgba(107, 114, 128, 0.12), rgba(156, 163, 175, 0.12), rgba(209, 213, 219, 0.12))',
                    backgroundSize: '400% 400%'
                }}
            ></div>

            {/* Static Design Elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none design-elements-container">
                {designElements.map((element) => (
                    <div
                        key={element.id}
                        className="absolute design-element"
                        style={{
                            left: `${element.x}%`,
                            top: `${element.y}%`,
                            opacity: Math.min(element.opacity + 0.35, 1),
                            color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(76, 29, 149, 0.65)',
                            transform: `rotate(${element.rotation}deg) scale(${element.size})`,
                            zIndex: 1,
                            filter: isDarkMode 
                                ? `drop-shadow(0 0 10px rgba(255, 255, 255, 0.3))`
                                : `drop-shadow(0 0 10px rgba(76, 29, 149, 0.28))`
                        }}
                    >
                        {element.icon}
                    </div>
                ))}
            </div>

            {/* Static Logo Watermark */}
            <div className="pointer-events-none w-auto h-auto" style={{zIndex: 10, position: 'absolute', right: 36, bottom: 8, overflow: 'visible', boxSizing: 'content-box', marginRight: 0, paddingRight: 0}}>
                <div className="relative" style={{width: isMobile ? 75 : 110, height: 'auto', overflow: 'visible', boxSizing: 'content-box', marginRight: 0, paddingRight: 0}}>
                    {/* Base logo layer */}
                    <img
                        src={white_logo}
                        alt="Logo Watermark"
                        className="w-full"
                        style={{
                            maxWidth: isMobile ? 75 : 110,
                            minWidth: 40,
                            filter: isDarkMode 
                                ? 'brightness(0) saturate(100%) invert(100%) drop-shadow(0 0 10px rgba(255, 255, 255, 0.3))'
                                : 'brightness(0) saturate(100%) invert(13%) sepia(80%) saturate(6000%) hue-rotate(240deg) brightness(0.7) drop-shadow(0 0 10px #4c1d95cc)',
                            opacity: 0.92,
                            display: 'block',
                            boxSizing: 'content-box',
                            marginRight: 0,
                            paddingRight: 0
                        }}
                    />
                </div>
            </div>

            {/* Static stone wall background */}
            <style jsx>{`
                .stone-wall-background {
                    background-color: ${isDarkMode ? '#1f2937' : '#fff'} !important;
                    background-image:
                        linear-gradient(335deg, ${isDarkMode ? 'rgba(55, 65, 81, 0.15)' : 'rgba(107, 114, 128, 0.08)'} 1px, transparent 1px),
                        linear-gradient(155deg, ${isDarkMode ? 'rgba(55, 65, 81, 0.15)' : 'rgba(107, 114, 128, 0.08)'} 1px, transparent 1px),
                        linear-gradient(335deg, ${isDarkMode ? 'rgba(55, 65, 81, 0.15)' : 'rgba(107, 114, 128, 0.08)'} 1px, transparent 1px),
                        linear-gradient(155deg, ${isDarkMode ? 'rgba(55, 65, 81, 0.15)' : 'rgba(107, 114, 128, 0.08)'} 1px, transparent 1px),
                        linear-gradient(335deg, ${isDarkMode ? 'rgba(55, 65, 81, 0.15)' : 'rgba(107, 114, 128, 0.08)'} 1px, transparent 1px),
                        linear-gradient(155deg, ${isDarkMode ? 'rgba(55, 65, 81, 0.15)' : 'rgba(107, 114, 128, 0.08)'} 1px, transparent 1px),
                        linear-gradient(90deg, ${isDarkMode ? 'rgba(55, 65, 81, 0.18)' : 'rgba(107, 114, 128, 0.1)'} 1px, transparent 1px),
                        linear-gradient(180deg, ${isDarkMode ? 'rgba(55, 65, 81, 0.18)' : 'rgba(107, 114, 128, 0.1)'} 1px, transparent 1px),
                        radial-gradient(circle at 50% 50%, ${isDarkMode ? 'rgba(55, 65, 81, 0.18)' : 'rgba(107, 114, 128, 0.1)'} 0%, rgba(0, 0, 0, 0) 60%);
                    background-size:
                        20px 20px,
                        20px 20px,
                        100px 100px,
                        100px 100px,
                        200px 200px,
                        200px 200px,
                        40px 40px,
                        40px 40px,
                        100% 100%;
                    background-position:
                        0 0,
                        0 0,
                        0 0,
                        0 0,
                        0 0,
                        0 0,
                        0 0,
                        0 0,
                        center center;
                    box-shadow: inset 0 0 100px ${isDarkMode ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.04)'};
                }
            `}</style>
        </div>
    );
};

export default DesignSpaceBackground;
