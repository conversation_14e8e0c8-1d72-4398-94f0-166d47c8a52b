import { useState, useEffect, useCallback } from 'react';
import { Dialog } from 'primereact/dialog';
import { ProgressBar } from 'primereact/progressbar';
import { But<PERSON> } from 'primereact/button';
import axiosInstance from '../../../../config/Axios';
import { useGlobalContext } from '@contexts/GlobalContext';
import { useDarkMode } from '@contexts/DarkModeContext';
import { FaCheckCircle, FaSpinner, FaExclamationTriangle, FaFolder, FaFileImage, FaImage } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';
import { RiGalleryLine } from 'react-icons/ri';
import { MdCameraEnhance } from 'react-icons/md';
import { IoPhonePortraitOutline } from 'react-icons/io5';
import { useNavigate } from 'react-router-dom';
import PropTypes from 'prop-types';

const ImageGenerationModal = ({ visible, designId, batchId, useBatchApi = false, useGroupsDesignApi = false, onHide, groupIds = [], navigationOptions = { stayOnPage: true, goToTemplates: false } }) => {
    console.log("Modal opened with designId:", designId, "batchId:", batchId, "useBatchApi:", useBatchApi, "useGroupsDesignApi:", useGroupsDesignApi, "groupIds:", groupIds);
    // Start with 'pending' state to show the pending UI first
    const [status, setStatus] = useState('pending');
    const [progress, setProgress] = useState(0);
    const [totalUsers, setTotalUsers] = useState(0);
    const [completedImages, setCompletedImages] = useState(0);
    const [error, setError] = useState(null);
    const [groupNames, setGroupNames] = useState([]); // Store group names
    const { showToast } = useGlobalContext();
    const { isDarkMode } = useDarkMode();
    const navigate = useNavigate();

    // Function to handle navigation after completion
    const handleNavigationAfterCompletion = useCallback(() => {
        console.log("🧭 [ImageGenerationModal] Handling navigation after completion:", navigationOptions);
        if (navigationOptions.goToTemplates) {
            console.log("🧭 [ImageGenerationModal] Navigating to template design page");
            navigate('/manager/template-design');
        } else {
            console.log("🧭 [ImageGenerationModal] Staying on current page");
        }
        // If stayOnPage is true, do nothing (stay on current page)
    }, [navigationOptions, navigate]);

    // Function to reset modal state
    const resetModalState = useCallback(() => {
        console.log("🔄 [ImageGenerationModal] Resetting modal state");
        setStatus('pending');
        setProgress(0);
        setTotalUsers(0);
        setCompletedImages(0);
        setError(null);
        setGroupNames([]);
    }, []);

    // Reset state when modal is closed
    useEffect(() => {
        if (!visible) {
            resetModalState();
        }
    }, [visible, resetModalState]);

    // Function to fetch group names
    const fetchGroupNames = useCallback(async () => {
        if (!groupIds || groupIds.length === 0) {
            setGroupNames([]);
            return;
        }

        try {
            // Get all groups first, then filter by the groupIds we have
            const response = await axiosInstance.get('/groups');

            if (response.data && response.data.data) {
                const allGroups = response.data.data;
                const filteredGroups = allGroups.filter(group => groupIds.includes(group.id));
                const names = filteredGroups.map(group => group.title || group.name || `Group ${group.id}`);
                setGroupNames(names);
                console.log("Fetched group names:", names);
            } else if (Array.isArray(response.data)) {
                // If response.data is directly an array
                const filteredGroups = response.data.filter(group => groupIds.includes(group.id));
                const names = filteredGroups.map(group => group.title || group.name || `Group ${group.id}`);
                setGroupNames(names);
                console.log("Fetched group names:", names);
            }
        } catch (error) {
            console.error('Error fetching group names:', error);
            // Fallback to group IDs if API fails
            setGroupNames(groupIds.map(id => `Group ${id}`));
        }
    }, [groupIds]);

    // Fetch status from the API - wrapped in useCallback to prevent recreation on each render
    const fetchStatus = useCallback(async () => {
        try {
            console.log("🔄 [ImageGenerationModal] Starting fetchStatus...");
            
            // Validate required parameters
            if (!designId) {
                console.error("❌ [ImageGenerationModal] No designId provided");
                setError('No design ID provided');
                return null;
            }

            let response;

            // Prepare query parameters for groupIds
            const queryParams = new URLSearchParams();
            if (groupIds && groupIds.length > 0 && groupIds.some(id => id !== null && id !== undefined)) {
                const validGroupIds = groupIds.filter(id => id !== null && id !== undefined);
                if (validGroupIds.length > 0) {
                    queryParams.append('group_ids', JSON.stringify(validGroupIds));
                    console.log("📤 [ImageGenerationModal] Sending groupIds to API:", validGroupIds);
                }
            }

            if (useGroupsDesignApi && batchId) {
                console.log("🚀 [ImageGenerationModal] Fetching groups design status for design ID:", designId, "batch ID:", batchId, "groupIds:", groupIds);
                const url = `/designs/${designId}/groups-design-status/${batchId}${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
                console.log("🔗 [ImageGenerationModal] API URL:", url);
                console.log("📋 [ImageGenerationModal] Query params:", queryParams.toString());
                console.log("📋 [ImageGenerationModal] groupIds:", groupIds);
                
                try {
                    response = await axiosInstance.get(url);
                } catch (error) {
                    console.warn("⚠️ [ImageGenerationModal] groups-design-status failed, trying getStatus as fallback:", error);
                    // Fallback to getStatus if groups-design-status fails
                    const fallbackUrl = `/status-designs/${designId}/status${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
                    console.log("🔄 [ImageGenerationModal] Using fallback URL:", fallbackUrl);
                    response = await axiosInstance.get(fallbackUrl);
                }
            } else if (useBatchApi && batchId) {
                console.log("📊 [ImageGenerationModal] Fetching new user assignment status for design ID:", designId, "batch ID:", batchId, "groupIds:", groupIds);
                const url = `/designs/${designId}/new-user-assignment-status/${batchId}${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
                response = await axiosInstance.get(url);
            } else if (useBatchApi && designId) {
                console.log("📊 [ImageGenerationModal] Fetching new user assignment status for design ID:", designId, "without batch ID", "groupIds:", groupIds);
                const url = `/designs/${designId}/new-user-assignment-status${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
                response = await axiosInstance.get(url);
            } else {
                console.log("📊 [ImageGenerationModal] Using getStatus API for design ID:", designId, "groupIds:", groupIds);
                const url = `/status-designs/${designId}/status${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
                console.log("🔗 [ImageGenerationModal] getStatus API URL:", url);
                console.log("📋 [ImageGenerationModal] getStatus Query params:", queryParams.toString());
                console.log("📋 [ImageGenerationModal] groupIds:", groupIds);
                
                // If no valid groupIds, don't send query params
                if (!groupIds || groupIds.length === 0 || !groupIds.some(id => id !== null && id !== undefined)) {
                    console.log("⚠️ [ImageGenerationModal] No valid groupIds, using design status without group filter");
                    const fallbackUrl = `/status-designs/${designId}/status`;
                    console.log("🔄 [ImageGenerationModal] Using fallback URL:", fallbackUrl);
                    response = await axiosInstance.get(fallbackUrl);
                } else {
                    response = await axiosInstance.get(url);
                }
            }

            const data = response.data;
            console.log("📊 [ImageGenerationModal] API Response:", data);
            console.log("📊 [ImageGenerationModal] Status from API:", data.status);

            // Force status to be a string and trim any whitespace
            let cleanStatus = String(data.status).trim().toLowerCase();
            console.log("🧹 [ImageGenerationModal] Cleaned status:", cleanStatus);

            // Store the actual API status (removed for now)

            // Update other state values
            setTotalUsers(data.total_users || 0);
            setCompletedImages(data.completed_images || 0);
            setProgress(data.progress || 0);

            console.log("✅ [ImageGenerationModal] Updated state values:", {
                status: cleanStatus,
                totalUsers: data.total_users,
                completedImages: data.completed_images,
                progress: data.progress
            });

            return cleanStatus;
        } catch (error) {
            console.error('❌ [ImageGenerationModal] Error fetching status:', error);
            console.error('❌ [ImageGenerationModal] Error details:', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status
            });
            setError('Failed to fetch status. Please try again later.');
            return null;
        }
    }, [designId, batchId, useBatchApi, useGroupsDesignApi, groupIds]); // Remove unnecessary dependencies

    // Log when the designId or batchId prop changes and reset status
    useEffect(() => {
        console.log("designId prop changed to:", designId, "batchId:", batchId, "useBatchApi:", useBatchApi, "useGroupsDesignApi:", useGroupsDesignApi, "groupIds:", groupIds);

        // Reset status to pending when designId or batchId changes
        if (designId || (useBatchApi && batchId) || (useGroupsDesignApi && batchId)) {
            console.log("Starting with 'pending' status for 5 seconds");
            console.log("Processing for groups:", groupIds);
            setStatus('pending');
            setError(null); // Reset error state

            // After 5 seconds, change to processing and start checking API
            const pendingTimer = setTimeout(() => {
                console.log("5 seconds passed, changing to 'processing' status");
                setStatus('processing');

                // Start checking API status immediately after changing to processing
                fetchStatus().catch(error => {
                    console.error("Error in initial fetchStatus:", error);
                    setError('Failed to fetch initial status');
                });
            }, 5000); // 5 seconds

            // Cleanup function to clear timer if component unmounts or designId changes
            return () => {
                clearTimeout(pendingTimer);
            };
        }
    }, [designId, batchId, useBatchApi, useGroupsDesignApi, fetchStatus, groupIds]);

    // Fetch group names when groupIds changes
    useEffect(() => {
        if (visible && groupIds && groupIds.length > 0) {
            fetchGroupNames();
        }
    }, [visible, groupIds, fetchGroupNames]);

    // Poll for status updates - only start polling when status is 'processing'
    useEffect(() => {
        const hasValidId = designId || (useBatchApi && batchId) || (useGroupsDesignApi && batchId);
        if (!visible || !hasValidId || status !== 'processing') {
            console.log("Not starting polling - visible:", visible, "hasValidId:", hasValidId, "status:", status);
            return;
        }

        const identifier = useGroupsDesignApi && batchId ? `groups design batch ID: ${batchId}` : 
                          useBatchApi && batchId ? `batch ID: ${batchId}` : 
                          `design ID: ${designId}`;
        console.log("Starting polling for", identifier, "with status:", status);

        // Set up polling interval - poll every 2 seconds for faster updates
        console.log("Setting up polling interval: 2 seconds");
        const interval = setInterval(async () => {
            try {
                console.log("🔄 Polling for status update...");
                const apiStatus = await fetchStatus();

                // Only change UI status to completed when API returns completed
                if (apiStatus === 'completed' || apiStatus === 'complete') {
                    console.log("✅ API returned completed status! Changing UI to completed");
                    setStatus('completed');
                    setProgress(100);

                    // Show success toast
                    showToast('success', 'Success', 'Image generation completed successfully!');

                    // Log that polling will stop
                    console.log("🛑 POLLING WILL STOP - Status is completed");

                    // Handle navigation after completion
                    handleNavigationAfterCompletion();
                } else if (apiStatus === 'processing') {
                    console.log("🔄 API returned processing status, continuing to poll...");
                } else {
                    console.log("❓ API returned unknown status:", apiStatus);
                }
            } catch (error) {
                console.error('❌ Error polling design status:', error);
                // Don't stop polling on error, just log it
            }
        }, 2000); // Poll every 2 seconds for faster updates

        // Clean up function
        return () => {
            console.log("Cleaning up polling interval");
            clearInterval(interval);
        };
    }, [visible, designId, status, showToast, fetchStatus, handleNavigationAfterCompletion]);

    // Log the current status for debugging
    useEffect(() => {
        console.log("Current status state changed to:", status);
    }, [status]);

    // Render different content based on status
    const renderContent = () => {
        console.log("Current status in renderContent:", status);
        console.log("Status type:", typeof status);

        // IMPORTANT: We're using a hardcoded approach to ensure the correct UI is shown
        // This is a direct mapping without any complex logic
        const statusString = String(status).trim();
        console.log("Status string for rendering:", statusString);

        // Force lowercase for comparison
        const statusLower = statusString.toLowerCase();

        // Simple direct mapping to UI states
        let uiState;

        if (statusLower === 'pending') {
            uiState = 'pending';
            console.log("SHOWING PENDING UI");
        } else if (statusLower === 'processing') {
            uiState = 'processing';
            console.log("SHOWING PROCESSING UI");
        } else if (statusLower === 'completed') {
            uiState = 'completed';
            console.log("SHOWING COMPLETED UI");
        } else {
            // Default to pending for any other status
            uiState = 'pending';
            console.log("SHOWING DEFAULT (pending) UI for unknown status:", statusString);
        }

        // Use a simple switch with exact string matching
        switch (uiState) {
            case 'pending':
                return (
                    <div className="text-center p-5">
                        <motion.div
                            initial={{ scale: 0.8, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={{ duration: 0.7, type: "spring" }}
                            className="mb-8 relative"
                        >
                            {/* Glowing background effect */}
                            <div className="absolute inset-0 -m-10 bg-gradient-to-r from-blue-400 via-indigo-400 to-purple-500 rounded-full opacity-10 blur-xl"></div>

                            {/* 3D Smartphone with AI initialization animation */}
                            <div className="relative w-64 h-[450px] mx-auto">
                                {/* Phone frame with premium finish */}
                                <div className="relative w-full h-full rounded-[32px] overflow-hidden"
                                    style={{
                                        background: "linear-gradient(145deg, #2a2a2a, #3a3a3a)",
                                        boxShadow: "0 15px 35px rgba(0,0,0,0.3), inset 0 2px 2px rgba(255,255,255,0.1), inset 0 -2px 2px rgba(0,0,0,0.8)",
                                        border: "6px solid #222",
                                        transform: "perspective(1000px) rotateY(-5deg) rotateX(5deg)",
                                        transformStyle: "preserve-3d"
                                    }}>

                                    {/* Phone screen with AI initialization animation */}
                                    <div className="absolute inset-0 bg-gradient-to-br from-blue-900 to-black overflow-hidden"
                                        style={{
                                            boxShadow: "inset 0 0 15px rgba(255,255,255,0.1)",
                                        }}>

                                        {/* Screen content */}
                                        <div className="relative w-full h-full flex flex-col items-center justify-center p-4 overflow-hidden">
                                            {/* App header */}
                                            
                                        

                                            {/* AI Initialization Animation */}
                                            <div className="relative w-full h-[280px] mt-12 flex items-center justify-center">
                                                {/* Central AI brain visualization */}
                                                <div className="relative">
                                                    {/* Glowing orb */}
                                                    <motion.div
                                                        className="absolute inset-0 rounded-full"
                                                        style={{
                                                            background: "radial-gradient(circle, rgba(59,130,246,0.6) 0%, rgba(59,130,246,0) 70%)",
                                                            filter: "blur(10px)"
                                                        }}
                                                        animate={{
                                                            scale: [1, 1.2, 1],
                                                            opacity: [0.5, 0.8, 0.5]
                                                        }}
                                                        transition={{
                                                            repeat: Infinity,
                                                            duration: 3,
                                                            ease: "easeInOut"
                                                        }}
                                                    />

                                                    {/* Central orb */}
                                                    <motion.div
                                                        className="relative w-24 h-24 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center"
                                                        animate={{
                                                            boxShadow: [
                                                                "0 0 20px rgba(59,130,246,0.5)",
                                                                "0 0 30px rgba(59,130,246,0.7)",
                                                                "0 0 20px rgba(59,130,246,0.5)"
                                                            ]
                                                        }}
                                                        transition={{
                                                            repeat: Infinity,
                                                            duration: 3,
                                                            ease: "easeInOut"
                                                        }}
                                                    >
                                                        <motion.div
                                                            className="absolute inset-2 rounded-full bg-gradient-to-br from-blue-400 to-indigo-500"
                                                            animate={{
                                                                opacity: [0.8, 1, 0.8],
                                                                scale: [0.95, 1, 0.95]
                                                            }}
                                                            transition={{
                                                                repeat: Infinity,
                                                                duration: 2,
                                                                ease: "easeInOut"
                                                            }}
                                                        />

                                                        {/* AI Icon */}
                                                        <MdCameraEnhance className="text-white text-4xl relative z-10" />
                                                    </motion.div>

                                                    {/* Orbiting particles */}
                                                    {[...Array(8)].map((_, i) => {
                                                        const angle = (i * Math.PI * 2) / 8;
                                                        const delay = i * 0.15;

                                                        return (
                                                            <motion.div
                                                                key={`particle-${i}`}
                                                                className="absolute w-3 h-3 rounded-full bg-blue-400"
                                                                style={{
                                                                    top: "50%",
                                                                    left: "50%",
                                                                    marginTop: "-1.5px",
                                                                    marginLeft: "-1.5px"
                                                                }}
                                                                animate={{
                                                                    x: [
                                                                        Math.cos(angle) * 50,
                                                                        Math.cos(angle + Math.PI) * 50,
                                                                        Math.cos(angle + Math.PI * 2) * 50
                                                                    ],
                                                                    y: [
                                                                        Math.sin(angle) * 50,
                                                                        Math.sin(angle + Math.PI) * 50,
                                                                        Math.sin(angle + Math.PI * 2) * 50
                                                                    ],
                                                                    opacity: [0.8, 1, 0.8],
                                                                    scale: [1, 1.5, 1]
                                                                }}
                                                                transition={{
                                                                    repeat: Infinity,
                                                                    duration: 4,
                                                                    delay: delay,
                                                                    ease: "easeInOut"
                                                                }}
                                                            />
                                                        );
                                                    })}

                                                    {/* Connection lines */}
                                                    {[...Array(12)].map((_, i) => {
                                                        const startAngle = (i * Math.PI * 2) / 12;

                                                        return (
                                                            <motion.div
                                                                key={`line-${i}`}
                                                                className="absolute top-1/2 left-1/2 w-[100px] h-[1px] bg-blue-400 origin-left"
                                                                style={{
                                                                    width: "100px",
                                                                    opacity: 0.3,
                                                                    transform: `translate(-0px, -0.5px) rotate(${startAngle * 180 / Math.PI}deg)`,
                                                                }}
                                                                animate={{
                                                                    opacity: [0.1, 0.3, 0.1],
                                                                    width: [0, 100, 0]
                                                                }}
                                                                transition={{
                                                                    repeat: Infinity,
                                                                    duration: 3,
                                                                    delay: i * 0.2,
                                                                    ease: "easeInOut"
                                                                }}
                                                            />
                                                        );
                                                    })}
                                                </div>

                                                {/* Floating text elements */}
                                                {["Initializing", "Processing", "Loading", "Preparing"].map((text, i) => {
                                                    const angle = (i * Math.PI * 2) / 4;
                                                    const x = Math.cos(angle) * 100;
                                                    const y = Math.sin(angle) * 80;

                                                    return (
                                                        <motion.div
                                                            key={`text-${i}`}
                                                            className="absolute text-xs font-bold text-blue-300 bg-blue-900 bg-opacity-30 px-2 py-1 rounded-lg"
                                                            style={{
                                                                top: "50%",
                                                                left: "50%",
                                                                transform: `translate(${x}px, ${y}px)`,
                                                                textShadow: "0 0 10px rgba(59,130,246,0.5)"
                                                            }}
                                                            animate={{
                                                                opacity: [0.5, 1, 0.5],
                                                                y: [y, y - 5, y]
                                                            }}
                                                            transition={{
                                                                repeat: Infinity,
                                                                duration: 2,
                                                                delay: i * 0.5,
                                                                ease: "easeInOut"
                                                            }}
                                                        >
                                                            {text}
                                                        </motion.div>
                                                    );
                                                })}
                                            </div>


                                        </div>
                                    </div>

                                    {/* Dynamic Island */}
                                    <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-32 h-8 bg-black rounded-full z-10 flex items-center justify-center overflow-hidden">
                                        <motion.div
                                            className="w-full h-full relative flex items-center justify-center"
                                            animate={{
                                                width: ["60%", "95%", "60%"],
                                                borderRadius: ["9999px", "16px", "9999px"]
                                            }}
                                            transition={{
                                                repeat: Infinity,
                                                repeatType: "reverse",
                                                duration: 3,
                                                ease: "easeInOut",
                                                repeatDelay: 2
                                            }}
                                        >
                                            {/* Status indicator */}
                                            <div className="absolute left-2 top-1/2 transform -translate-y-1/2 w-2 h-2 rounded-full bg-green-500"></div>

                                            {/* Status text that appears when expanded */}
                                            <motion.div
                                                className="text-white text-[10px] font-medium flex items-center"
                                                animate={{
                                                    opacity: [0, 1, 0],
                                                    x: [10, 0, 10]
                                                }}
                                                transition={{
                                                    repeat: Infinity,
                                                    repeatType: "reverse",
                                                    duration: 3,
                                                    ease: "easeInOut",
                                                    repeatDelay: 2
                                                }}
                                            >
                                                <span className="ml-4">Processing Images</span>
                                            </motion.div>

                                            {/* Right side icons */}
                                            
                                        </motion.div>
                                    </div>

                                </div>



                                {/* Phone reflection */}
                                <div className="absolute bottom-[-10px] left-1/2 transform -translate-x-1/2 w-56 h-8 bg-black opacity-20 blur-md rounded-full"></div>
                            </div>

                            {/* Floating particles */}
                            {[...Array(15)].map((_, i) => (
                                <motion.div
                                    key={`float-particle-${i}`}
                                    className="absolute w-2 h-2 rounded-full"
                                    style={{
                                        background: i % 3 === 0
                                            ? "radial-gradient(circle, rgba(59,130,246,0.8) 0%, rgba(59,130,246,0) 70%)"
                                            : i % 3 === 1
                                                ? "radial-gradient(circle, rgba(99,102,241,0.8) 0%, rgba(99,102,241,0) 70%)"
                                                : "radial-gradient(circle, rgba(139,92,246,0.8) 0%, rgba(139,92,246,0) 70%)",
                                        top: `${Math.random() * 100}%`,
                                        left: `${Math.random() * 100}%`,
                                    }}
                                    animate={{
                                        y: [0, -(10 + Math.random() * 20)],
                                        x: [0, (Math.random() * 10) - 5],
                                        opacity: [0, 0.7, 0],
                                        scale: [0, 0.5 + Math.random() * 0.5, 0]
                                    }}
                                    transition={{
                                        repeat: Infinity,
                                        duration: 2 + Math.random() * 2,
                                        delay: Math.random() * 2
                                    }}
                                />
                            ))}
                        </motion.div>

                        <motion.div
                            initial={{ y: 20, opacity: 0 }}
                            animate={{ y: 0, opacity: 1 }}
                            transition={{ delay: 0.3, duration: 0.5 }}
                        >
                            <h3 className={`text-2xl font-bold mb-3 bg-gradient-to-r from-blue-500 to-indigo-500 bg-clip-text text-transparent ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>Preparing Your Professional Design</h3>
                            <p className={`mb-5 max-w-md mx-auto ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                                We&apos;re initializing our advanced processing engine to create beautiful professional images for all users in your group.
                                {groupIds && groupIds.length > 0 && (
                                    <div className={`mt-4 p-3 border rounded-lg ${isDarkMode ? 'bg-gradient-to-r from-blue-900/30 to-indigo-900/30 border-blue-700' : 'bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200'}`}>
                                        <div className="flex items-center justify-center mb-2">
                                            <div className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
                                            <span className={`text-sm font-semibold ${isDarkMode ? 'text-blue-300' : 'text-blue-700'}`}>
                                                Processing for {groupIds.length} group{groupIds.length > 1 ? 's' : ''}
                                            </span>
                                        </div>
                                        <div className="flex flex-wrap justify-center gap-2">
                                            {(groupNames.length > 0 ? groupNames : groupIds.map(id => `Group ${id}`)).map((name, index) => (
                                                <motion.div
                                                    key={index}
                                                    initial={{ scale: 0.8, opacity: 0 }}
                                                    animate={{ scale: 1, opacity: 1 }}
                                                    transition={{ delay: index * 0.1, duration: 0.3 }}
                                                    className={`px-3 py-1 border rounded-full text-xs font-medium shadow-sm ${isDarkMode ? 'bg-gray-700 border-blue-500 text-blue-300' : 'bg-white border-blue-300 text-blue-700'}`}
                                                >
                                                    {name}
                                                </motion.div>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </p>

                            {/* Status indicators */}
                            <div className="flex justify-center space-x-6 mb-4">
                                <motion.div
                                    className="text-center"
                                    initial={{ scale: 0.8, opacity: 0 }}
                                    animate={{ scale: 1, opacity: 1 }}
                                    transition={{ delay: 0.4, duration: 0.5 }}
                                >
                                    <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center mx-auto mb-2 shadow-md border border-blue-200">
                                        <motion.div
                                            className="w-8 h-8 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-400 flex items-center justify-center"
                                            animate={{ rotate: 360 }}
                                            transition={{ repeat: Infinity, duration: 3, ease: "linear" }}
                                        >
                                            <FaSpinner className="text-white text-lg" />
                                        </motion.div>
                                    </div>
                                    <p className={`text-xs font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Initializing</p>
                                </motion.div>

                                <motion.div
                                    className="text-center"
                                    initial={{ scale: 0.8, opacity: 0 }}
                                    animate={{ scale: 1, opacity: 1 }}
                                    transition={{ delay: 0.5, duration: 0.5 }}
                                >
                                    <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-indigo-100 to-indigo-200 flex items-center justify-center mx-auto mb-2 shadow-md border border-indigo-200">
                                        <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-indigo-500 to-purple-400 flex items-center justify-center">
                                            <MdCameraEnhance className="text-white text-lg" />
                                        </div>
                                    </div>
                                    <p className={`text-xs font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Processing</p>
                                </motion.div>

                                <motion.div
                                    className="text-center"
                                    initial={{ scale: 0.8, opacity: 0 }}
                                    animate={{ scale: 1, opacity: 1 }}
                                    transition={{ delay: 0.6, duration: 0.5 }}
                                >
                                    <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-purple-100 to-purple-200 flex items-center justify-center mx-auto mb-2 shadow-md border border-purple-200">
                                        <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-purple-500 to-pink-400 flex items-center justify-center">
                                            <IoPhonePortraitOutline className="text-white text-lg" />
                                        </div>
                                    </div>
                                    <p className={`text-xs font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Preparing</p>
                                </motion.div>
                            </div>

                            {/* Animated dots */}
                            <div className="flex justify-center space-x-2 text-blue-500">
                                <motion.span
                                    animate={{ opacity: [0.3, 1, 0.3], scale: [0.8, 1, 0.8] }}
                                    transition={{ repeat: Infinity, duration: 1.5, delay: 0 }}
                                    className="text-xl"
                                >●</motion.span>
                                <motion.span
                                    animate={{ opacity: [0.3, 1, 0.3], scale: [0.8, 1, 0.8] }}
                                    transition={{ repeat: Infinity, duration: 1.5, delay: 0.2 }}
                                    className="text-xl"
                                >●</motion.span>
                                <motion.span
                                    animate={{ opacity: [0.3, 1, 0.3], scale: [0.8, 1, 0.8] }}
                                    transition={{ repeat: Infinity, duration: 1.5, delay: 0.4 }}
                                    className="text-xl"
                                >●</motion.span>
                            </div>
                        </motion.div>
                    </div>
                );

            case 'processing':
                return (
                    <div className="text-center p-5">
                        <div className="relative mb-8 max-w-md mx-auto">
                            {/* Smartphone container with 3D effect */}
                            <motion.div
                                initial={{ scale: 0.9, opacity: 0, rotateY: -20 }}
                                animate={{ scale: 1, opacity: 1, rotateY: 0 }}
                                transition={{ duration: 0.7, type: "spring" }}
                                className="relative z-10 mx-auto"
                                style={{ perspective: "1000px", transformStyle: "preserve-3d" }}
                            >
                                {/* Phone frame with metallic effect */}
                                <div className="relative w-64 h-[470px] mx-auto rounded-[36px] overflow-hidden"
                                    style={{
                                        background: isDarkMode 
                                            ? "linear-gradient(145deg, #1a1a1a, #2a2a2a)" 
                                            : "linear-gradient(145deg, #2a2a2a, #3a3a3a)",
                                        boxShadow: isDarkMode
                                            ? "0 10px 30px rgba(0,0,0,0.5), inset 0 1px 1px rgba(255,255,255,0.1), inset 0 -1px 1px rgba(0,0,0,0.8)"
                                            : "0 10px 30px rgba(0,0,0,0.3), inset 0 1px 1px rgba(255,255,255,0.2), inset 0 -1px 1px rgba(0,0,0,0.5)",
                                        border: isDarkMode ? "6px solid #111" : "6px solid #222",
                                        transform: "rotateY(0deg) rotateX(5deg)",
                                        transformStyle: "preserve-3d"
                                    }}>

                                    {/* Phone screen with glossy effect */}
                                    <div className={`absolute inset-0 overflow-hidden ${isDarkMode ? 'bg-gradient-to-br from-gray-900 via-black to-gray-800' : 'bg-gradient-to-br from-blue-900 via-purple-800 to-indigo-900'}`}
                                        style={{
                                            boxShadow: isDarkMode 
                                                ? "inset 0 0 10px rgba(255,255,255,0.1)" 
                                                : "inset 0 0 10px rgba(255,255,255,0.3)",
                                        }}>

                                        {/* Screen content */}
                                        <div className="relative w-full h-full flex flex-col items-center justify-center p-4 overflow-hidden">
                                            {/* App header */}
                                           

                                            {/* Enhanced Processing Display */}
                                            <div className="relative w-full h-[330px] mt-8 overflow-hidden">
                                                {/* Professional Processing Interface */}
                                                <div className="absolute inset-0 flex flex-col items-center justify-center p-4">
                                                    {/* Central Processing Hub */}
                                                    <div className="relative mb-6">
                                                        {/* Main processing circle */}
                                                        <motion.div
                                                            className="w-24 h-24 rounded-full bg-gradient-to-br from-blue-500 via-purple-500 to-indigo-600 flex items-center justify-center relative"
                                                            animate={{
                                                                boxShadow: [
                                                                    "0 0 20px rgba(59,130,246,0.5)",
                                                                    "0 0 40px rgba(147,51,234,0.7)",
                                                                    "0 0 20px rgba(59,130,246,0.5)"
                                                                ]
                                                            }}
                                                            transition={{
                                                                repeat: Infinity,
                                                                duration: 2,
                                                                ease: "easeInOut"
                                                            }}
                                                        >
                                                            <motion.div
                                                                animate={{ rotate: 360 }}
                                                                transition={{ repeat: Infinity, duration: 3, ease: "linear" }}
                                                            >
                                                                <MdCameraEnhance className="text-white text-4xl" />
                                                            </motion.div>
                                                        </motion.div>

                                                        {/* Orbiting progress indicators */}
                                                        {[...Array(6)].map((_, i) => {
                                                            const angle = (i * Math.PI * 2) / 6;
                                                            const radius = 50;

                                                            return (
                                                                <motion.div
                                                                    key={`orbit-${i}`}
                                                                    className="absolute w-4 h-4 rounded-full bg-gradient-to-r from-blue-400 to-purple-400"
                                                                    style={{
                                                                        top: "50%",
                                                                        left: "50%",
                                                                        marginTop: "-8px",
                                                                        marginLeft: "-8px"
                                                                    }}
                                                                    animate={{
                                                                        x: Math.cos(angle) * radius,
                                                                        y: Math.sin(angle) * radius,
                                                                        scale: [1, 1.5, 1],
                                                                        opacity: [0.6, 1, 0.6]
                                                                    }}
                                                                    transition={{
                                                                        repeat: Infinity,
                                                                        duration: 4,
                                                                        delay: i * 0.3,
                                                                        ease: "easeInOut"
                                                                    }}
                                                                />
                                                            );
                                                        })}
                                                    </div>

                                                    {/* Processing Stats */}
                                                    <div className="w-full max-w-xs space-y-3">
                                                        {/* Images processed counter */}
                                                        <div className={`rounded-lg p-3 backdrop-blur-sm ${isDarkMode ? 'bg-gray-800/80 border border-gray-700' : 'bg-black bg-opacity-30'}`}>
                                                            <div className="flex justify-between items-center mb-2">
                                                                <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-200' : 'text-white'}`}>Images Generated</span>
                                                                <span className={`text-sm font-bold ${isDarkMode ? 'text-blue-400' : 'text-blue-300'}`}>{completedImages}/{totalUsers}</span>
                                                            </div>

                                                            {/* Enhanced Progress Bar */}
                                                            <div className={`w-full h-2 rounded-full overflow-hidden relative ${isDarkMode ? 'bg-gray-700' : 'bg-gray-800'}`}>
                                                                <motion.div
                                                                    className="h-full bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-500 relative"
                                                                    style={{ width: `${progress}%` }}
                                                                    initial={{ width: "0%" }}
                                                                    animate={{ width: `${progress}%` }}
                                                                    transition={{ duration: 0.5, ease: "easeOut" }}
                                                                >
                                                                    {/* Animated shine effect */}
                                                                    <motion.div
                                                                        className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30"
                                                                        animate={{
                                                                            x: ["-100%", "100%"]
                                                                        }}
                                                                        transition={{
                                                                            repeat: Infinity,
                                                                            duration: 1.5,
                                                                            ease: "linear"
                                                                        }}
                                                                    />
                                                                </motion.div>
                                                            </div>
                                                        </div>

                                                        {/* Status indicator */}
                                                        <div className={`rounded-lg p-3 backdrop-blur-sm ${isDarkMode ? 'bg-gray-800/80 border border-gray-700' : 'bg-black bg-opacity-30'}`}>
                                                            <div className="flex items-center justify-center space-x-2">
                                                                <motion.div
                                                                    className="w-3 h-3 rounded-full bg-green-400"
                                                                    animate={{ opacity: [0.5, 1, 0.5] }}
                                                                    transition={{ repeat: Infinity, duration: 1.5 }}
                                                                />
                                                                <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-200' : 'text-white'}`}>Processing Images...</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>


                                        </div>
                                    </div>

                                    {/* Dynamic Island */}
                                    <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-32 h-8 bg-black rounded-full z-10 flex items-center justify-center overflow-hidden">
                                        <motion.div
                                            className="w-full h-full relative flex items-center justify-center"
                                            animate={{
                                                width: ["60%", "95%", "60%"],
                                                borderRadius: ["9999px", "16px", "9999px"]
                                            }}
                                            transition={{
                                                repeat: Infinity,
                                                repeatType: "reverse",
                                                duration: 3,
                                                ease: "easeInOut",
                                                repeatDelay: 2
                                            }}
                                        >
                                            {/* Status indicator */}
                                            <div className="absolute left-2 top-1/2 transform -translate-y-1/2 w-2 h-2 rounded-full bg-green-500"></div>

                                            {/* Status text that appears when expanded */}
                                            <motion.div
                                                className="text-white text-[10px] font-medium flex items-center"
                                                animate={{
                                                    opacity: [0, 1, 0],
                                                    x: [10, 0, 10]
                                                }}
                                                transition={{
                                                    repeat: Infinity,
                                                    repeatType: "reverse",
                                                    duration: 3,
                                                    ease: "easeInOut",
                                                    repeatDelay: 2
                                                }}
                                            >
                                                <span className="ml-4">Processing Images</span>
                                            </motion.div>

                                            {/* Right side icons */}
                                            
                                        </motion.div>
                                    </div>

                                </div>



                                {/* Phone reflection */}
                                <div className="absolute bottom-[-10px] left-1/2 transform -translate-x-1/2 w-56 h-10 bg-black opacity-20 blur-md rounded-full"></div>

                                {/* Animated particles */}
                                {[...Array(8)].map((_, i) => (
                                    <motion.div
                                        key={`particle-${i}`}
                                        className="absolute w-2 h-2 rounded-full"
                                        style={{
                                            background: `radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%)`,
                                            top: `${Math.random() * 100}%`,
                                            left: `${Math.random() * 100}%`,
                                        }}
                                        animate={{
                                            y: [0, -20, 0],
                                            opacity: [0, 0.8, 0],
                                            scale: [0, 1, 0]
                                        }}
                                        transition={{
                                            repeat: Infinity,
                                            duration: 2 + Math.random() * 2,
                                            delay: Math.random() * 2
                                        }}
                                    />
                                ))}
                            </motion.div>
                        </div>

                        {/* Enhanced Professional Status Message */}
                        <div className="mb-6 max-w-md mx-auto">
                            <div className={`border rounded-xl p-4 shadow-sm ${isDarkMode ? 'bg-gradient-to-r from-gray-800/90 to-gray-700/90 border-gray-600' : 'bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200'}`}>
                                <div className="flex items-center justify-between mb-3">
                                    <div className="flex items-center space-x-2">
                                        <motion.div
                                            className="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center"
                                            animate={{
                                                boxShadow: [
                                                    "0 0 10px rgba(59,130,246,0.3)",
                                                    "0 0 20px rgba(59,130,246,0.5)",
                                                    "0 0 10px rgba(59,130,246,0.3)"
                                                ]
                                            }}
                                            transition={{
                                                repeat: Infinity,
                                                duration: 2,
                                                ease: "easeInOut"
                                            }}
                                        >
                                            <FaImage className="text-white text-sm" />
                                        </motion.div>
                                        <span className={`text-sm font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>Processing Status</span>
                                    </div>
                                    <div className={`px-2 py-1 rounded-full text-xs font-medium ${isDarkMode ? 'bg-blue-900/50 text-blue-300' : 'bg-blue-100 text-blue-700'}`}>
                                        Active
                                    </div>
                                </div>
                                
                                <div className="space-y-2">
                                    <div className="flex items-center justify-between text-sm">
                                        <span className={`flex items-center ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                                            <motion.div
                                                className="w-4 h-4 rounded-full bg-green-400 mr-2"
                                                animate={{ scale: [1, 1.2, 1], opacity: [0.7, 1, 0.7] }}
                                                transition={{ repeat: Infinity, duration: 1.5 }}
                                            />
                                            Generated Images
                                        </span>
                                        <span className={`font-bold ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`}>{completedImages}</span>
                                    </div>
                                    
                                    <div className="flex items-center justify-between text-sm">
                                        <span className={`flex items-center ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                                            <motion.div
                                                className="w-4 h-4 rounded-full bg-purple-400 mr-2"
                                                animate={{ scale: [1, 1.2, 1], opacity: [0.7, 1, 0.7] }}
                                                transition={{ repeat: Infinity, duration: 1.5, delay: 0.3 }}
                                            />
                                            Total Users
                                        </span>
                                        <span className={`font-bold ${isDarkMode ? 'text-purple-400' : 'text-purple-600'}`}>{totalUsers}</span>
                                    </div>
                                    
                                    <div className="flex items-center justify-between text-sm">
                                        <span className={`flex items-center ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                                            <motion.div
                                                className="w-4 h-4 rounded-full bg-indigo-400 mr-2"
                                                animate={{ scale: [1, 1.2, 1], opacity: [0.7, 1, 0.7] }}
                                                transition={{ repeat: Infinity, duration: 1.5, delay: 0.6 }}
                                            />
                                            Completion Rate
                                        </span>
                                        <span className={`font-bold ${isDarkMode ? 'text-indigo-400' : 'text-indigo-600'}`}>{progress}%</span>
                                    </div>

                                    {/* Groups Information */}
                                    {groupIds && groupIds.length > 0 && (
                                        <div className={`pt-2 border-t ${isDarkMode ? 'border-gray-600' : 'border-blue-200'}`}>
                                            <div className="flex items-center justify-between text-sm mb-2">
                                                <span className={`flex items-center ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                                                    <motion.div
                                                        className="w-4 h-4 rounded-full bg-blue-400 mr-2"
                                                        animate={{ scale: [1, 1.2, 1], opacity: [0.7, 1, 0.7] }}
                                                        transition={{ repeat: Infinity, duration: 1.5, delay: 0.9 }}
                                                    />
                                                    Active Groups
                                                </span>
                                                <span className={`font-bold ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`}>{groupIds.length}</span>
                                            </div>
                                            <div className="flex flex-wrap gap-1.5">
                                                {(groupNames.length > 0 ? groupNames : groupIds.map(id => `Group ${id}`)).map((name, index) => (
                                                    <motion.div
                                                        key={index}
                                                        initial={{ scale: 0.8, opacity: 0 }}
                                                        animate={{ scale: 1, opacity: 1 }}
                                                        transition={{ delay: index * 0.1, duration: 0.3 }}
                                                        className={`px-2 py-0.5 border rounded-full text-xs font-medium shadow-sm flex items-center ${isDarkMode ? 'bg-gray-700 border-blue-500 text-blue-300' : 'bg-white border-blue-300 text-blue-700'}`}
                                                    >
                                                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1 animate-pulse"></div>
                                                        {name}
                                                    </motion.div>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                </div>
                                
                                
                                <div className={`mt-3 pt-3 border-t ${isDarkMode ? 'border-gray-600' : 'border-blue-200'}`}>
                                    <div className="flex items-center justify-center space-x-1">
                                        <motion.div
                                            className="w-2 h-2 rounded-full bg-blue-500"
                                            animate={{ opacity: [0.3, 1, 0.3] }}
                                            transition={{ repeat: Infinity, duration: 1.5, delay: 0 }}
                                        />
                                        <motion.div
                                            className="w-2 h-2 rounded-full bg-indigo-500"
                                            animate={{ opacity: [0.3, 1, 0.3] }}
                                            transition={{ repeat: Infinity, duration: 1.5, delay: 0.2 }}
                                        />
                                        <motion.div
                                            className="w-2 h-2 rounded-full bg-purple-500"
                                            animate={{ opacity: [0.3, 1, 0.3] }}
                                            transition={{ repeat: Infinity, duration: 1.5, delay: 0.4 }}
                                        />
                                        <span className={`text-xs ml-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Processing in progress...</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className={`mb-2 text-sm flex justify-between max-w-md mx-auto ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                            <span>Overall Progress</span>
                            <span className="font-medium">{progress}%</span>
                        </div>

 

                        <div className="max-w-md mx-auto mb-4 relative">
                            <ProgressBar value={progress} className="mb-1" style={{
                                height: '10px',
                                borderRadius: '5px',
                                background: 'linear-gradient(to right, rgba(59, 130, 246, 0.2), rgba(147, 51, 234, 0.2))'
                            }} />

                            {/* Animated icon that moves with progress */}
                            <motion.div
                                className="absolute top-0 -mt-1"
                                style={{
                                    left: `calc(${progress}% - 10px)`,
                                    display: progress > 5 ? 'block' : 'none'
                                }}
                                animate={{ y: [0, -5, 0] }}
                                transition={{ repeat: Infinity, duration: 1.5 }}
                            >
                                <FaFileImage className="text-blue-500 text-lg" />
                            </motion.div>
                        </div>

                        <p className={`text-xs italic ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Please don&apos;t close this window while professional images are being generated</p>
                    </div>
                );

            case 'completed':
                return (
                    <div className="text-center p-5">
                        {/* 3D Smartphone with completed images */}
                        <motion.div
                            initial={{ scale: 0.8, opacity: 0, rotateY: -30 }}
                            animate={{ scale: 1, opacity: 1, rotateY: 0 }}
                            transition={{ duration: 0.8, type: 'spring', bounce: 0.4 }}
                            className="mb-8 relative"
                            style={{ perspective: "1200px" }}
                        >
                            {/* Success glow effect */}
                            <div className="absolute inset-0 -m-10 bg-gradient-to-r from-green-400 via-teal-300 to-blue-500 rounded-full opacity-10 blur-xl"></div>

                            {/* Phone container with 3D effect */}
                            <div className="relative w-64 h-[470px] mx-auto"
                                style={{
                                    transformStyle: "preserve-3d",
                                    transform: "rotateY(0deg) rotateX(5deg)"
                                }}
                            >
                                {/* Phone frame with premium finish */}
                                <div className="absolute inset-0 rounded-[40px] overflow-hidden"
                                    style={{
                                        background: isDarkMode 
                                            ? "linear-gradient(145deg, #0a0a0a, #1a1a1a)" 
                                            : "linear-gradient(145deg, #1a1a1a, #2a2a2a)",
                                        boxShadow: isDarkMode
                                            ? "0 15px 35px rgba(0,0,0,0.6), inset 0 2px 2px rgba(255,255,255,0.05), inset 0 -2px 2px rgba(0,0,0,0.9)"
                                            : "0 15px 35px rgba(0,0,0,0.4), inset 0 2px 2px rgba(255,255,255,0.1), inset 0 -2px 2px rgba(0,0,0,0.8)",
                                        border: isDarkMode ? "8px solid #111" : "8px solid #222",
                                        transformStyle: "preserve-3d"
                                    }}>

                                    {/* Phone screen with completed images gallery */}
                                    <div className={`absolute inset-0 overflow-hidden ${isDarkMode ? 'bg-gradient-to-br from-black via-gray-900 to-black' : 'bg-gradient-to-br from-gray-900 to-black'}`}
                                        style={{
                                            boxShadow: isDarkMode 
                                                ? "inset 0 0 15px rgba(255,255,255,0.05)" 
                                                : "inset 0 0 15px rgba(255,255,255,0.1)",
                                        }}>

                                        {/* Screen content */}
                                        <div className="relative w-full h-full flex flex-col items-center justify-center p-4 overflow-hidden">

                                            {/* Professional Success Display */}
                                            <div className="relative w-full h-[400px] mt-4 overflow-hidden">
                                                {/* Success Interface */}
                                                <div className="absolute inset-0 flex flex-col items-center justify-center p-4">
                                                    {/* Central Success Hub */}
                                                    <motion.div
                                                        className="relative mb-8"
                                                        initial={{ scale: 0, opacity: 0 }}
                                                        animate={{ scale: 1, opacity: 1 }}
                                                        transition={{ delay: 0.3, duration: 0.8, type: "spring" }}
                                                    >
                                                        {/* Main success circle with glow */}
                                                        <div className="relative">
                                                            <div className="absolute inset-0 bg-green-500 rounded-full opacity-30 blur-xl animate-pulse"></div>
                                                            <motion.div
                                                                className="relative w-32 h-32 rounded-full bg-gradient-to-br from-green-400 via-emerald-500 to-teal-500 flex items-center justify-center"
                                                                style={{
                                                                    boxShadow: "0 0 40px rgba(34,197,94,0.6), inset 0 2px 4px rgba(255,255,255,0.2)"
                                                                }}
                                                                animate={{
                                                                    boxShadow: [
                                                                        "0 0 40px rgba(34,197,94,0.6)",
                                                                        "0 0 60px rgba(34,197,94,0.8)",
                                                                        "0 0 40px rgba(34,197,94,0.6)"
                                                                    ]
                                                                }}
                                                                transition={{
                                                                    repeat: Infinity,
                                                                    duration: 3,
                                                                    ease: "easeInOut"
                                                                }}
                                                            >
                                                                <motion.div
                                                                    initial={{ scale: 0 }}
                                                                    animate={{ scale: 1 }}
                                                                    transition={{ delay: 0.6, duration: 0.5, type: "spring" }}
                                                                >
                                                                    <FaCheckCircle className="text-white text-6xl" />
                                                                </motion.div>
                                                            </motion.div>
                                                        </div>

                                                        {/* Orbiting success indicators */}
                                                        {[...Array(4)].map((_, i) => {
                                                            const angle = (i * Math.PI * 2) / 4;
                                                            const radius = 80;

                                                            return (
                                                                <motion.div
                                                                    key={`success-orbit-${i}`}
                                                                    className="absolute w-6 h-6 rounded-full bg-gradient-to-r from-green-300 to-emerald-400 flex items-center justify-center"
                                                                    style={{
                                                                        top: "50%",
                                                                        left: "50%",
                                                                        marginTop: "-12px",
                                                                        marginLeft: "-12px"
                                                                    }}
                                                                    animate={{
                                                                        x: Math.cos(angle) * radius,
                                                                        y: Math.sin(angle) * radius,
                                                                        rotate: 360
                                                                    }}
                                                                    transition={{
                                                                        repeat: Infinity,
                                                                        duration: 8,
                                                                        delay: i * 0.5,
                                                                        ease: "linear"
                                                                    }}
                                                                >
                                                                    <FaCheckCircle className="text-white text-sm" />
                                                                </motion.div>
                                                            );
                                                        })}
                                                    </motion.div>

                                                    {/* Success Stats */}
                                                    <motion.div
                                                        className="w-full max-w-sm space-y-4"
                                                        initial={{ y: 20, opacity: 0 }}
                                                        animate={{ y: 0, opacity: 1 }}
                                                        transition={{ delay: 0.8, duration: 0.6 }}
                                                    >
                                                        {/* Completion summary */}
                                                        <div className={`rounded-xl p-4 backdrop-blur-sm border ${isDarkMode ? 'bg-gradient-to-r from-green-900/30 to-emerald-900/30 border-green-700/50' : 'bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-green-400/30'}`}>
                                                            <div className="flex justify-between items-center mb-3">
                                                                <span className={`text-sm font-semibold ${isDarkMode ? 'text-gray-200' : 'text-white'}`}>Generation Complete</span>
                                                                <div className="flex items-center space-x-1">
                                                                    <FaCheckCircle className="text-green-400 text-sm" />
                                                                    <span className="text-green-300 text-sm font-bold">100%</span>
                                                                </div>
                                                            </div>

                                                            <div className="grid grid-cols-2 gap-3 text-center">
                                                                <div className={`rounded-lg p-2 ${isDarkMode ? 'bg-gray-800/50' : 'bg-black/30'}`}>
                                                                    <div className="text-green-300 text-lg font-bold">{completedImages}</div>
                                                                    <div className={`text-xs opacity-80 ${isDarkMode ? 'text-gray-300' : 'text-white'}`}>Images</div>
                                                                </div>
                                                                <div className={`rounded-lg p-2 ${isDarkMode ? 'bg-gray-800/50' : 'bg-black/30'}`}>
                                                                    <div className="text-green-300 text-lg font-bold">{totalUsers}</div>
                                                                    <div className={`text-xs opacity-80 ${isDarkMode ? 'text-gray-300' : 'text-white'}`}>Users</div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        {/* Success message */}
                                                        <div className={`rounded-xl p-3 backdrop-blur-sm border ${isDarkMode ? 'bg-gray-800/50 border-green-700/30' : 'bg-black/30 border-green-400/20'}`}>
                                                            <div className="flex items-center justify-center space-x-2">
                                                                <motion.div
                                                                    className="w-3 h-3 rounded-full bg-green-400"
                                                                    animate={{ scale: [1, 1.2, 1] }}
                                                                    transition={{ repeat: Infinity, duration: 2 }}
                                                                />
                                                                <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-200' : 'text-white'}`}>All Images Successfully Generated</span>
                                                            </div>
                                                        </div>
                                                    </motion.div>
                                                </div>
                                            </div>


                                        </div>
                                    </div>

                                    {/* Dynamic Island - Completed state */}
                                    <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-32 h-8 bg-black rounded-full z-10 flex items-center justify-center overflow-hidden">
                                        <motion.div
                                            className="w-full h-full relative flex items-center justify-center"
                                            animate={{
                                                width: ["60%", "95%", "60%"],
                                                borderRadius: ["9999px", "16px", "9999px"]
                                            }}
                                            transition={{
                                                repeat: Infinity,
                                                repeatType: "reverse",
                                                duration: 3,
                                                ease: "easeInOut",
                                                repeatDelay: 2
                                            }}
                                        >
                                            {/* Status indicator */}
                                            <div className="absolute left-2 top-1/2 transform -translate-y-1/2 w-2 h-2 rounded-full bg-green-500"></div>

                                            {/* Status text that appears when expanded */}
                                            <motion.div
                                                className="text-white text-[10px] font-medium flex items-center"
                                                animate={{
                                                    opacity: [0, 1, 0],
                                                    x: [10, 0, 10]
                                                }}
                                                transition={{
                                                    repeat: Infinity,
                                                    repeatType: "reverse",
                                                    duration: 3,
                                                    ease: "easeInOut",
                                                    repeatDelay: 2
                                                }}
                                            >
                                                <span className="ml-4">Images Ready</span>
                                                <FaCheckCircle className="ml-1 text-green-500" size={8} />
                                            </motion.div>

                                            {/* Right side icons */}
                                            
                                        </motion.div>
                                    </div>

                                </div>



                                {/* Phone reflection */}
                                <div className="absolute bottom-[-15px] left-1/2 transform -translate-x-1/2 w-64 h-10 bg-black opacity-20 blur-md rounded-full"></div>

                                {/* Success particles */}
                                {[...Array(20)].map((_, i) => (
                                    <motion.div
                                        key={`success-particle-${i}`}
                                        className="absolute w-2 h-2 rounded-full"
                                        style={{
                                            background: i % 3 === 0
                                                ? "radial-gradient(circle, rgba(74,222,128,0.8) 0%, rgba(74,222,128,0) 70%)"
                                                : i % 3 === 1
                                                    ? "radial-gradient(circle, rgba(45,212,191,0.8) 0%, rgba(45,212,191,0) 70%)"
                                                    : "radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%)",
                                            top: `${Math.random() * 100}%`,
                                            left: `${Math.random() * 100}%`,
                                        }}
                                        initial={{ scale: 0, opacity: 0 }}
                                        animate={{
                                            y: [0, -(20 + Math.random() * 30)],
                                            x: [0, (Math.random() * 20) - 10],
                                            opacity: [0, 0.8, 0],
                                            scale: [0, 1 + Math.random() * 0.5, 0]
                                        }}
                                        transition={{
                                            repeat: Infinity,
                                            duration: 2 + Math.random() * 2,
                                            delay: Math.random() * 2,
                                            repeatDelay: Math.random()
                                        }}
                                    />
                                ))}
                            </div>
                        </motion.div>

                        <motion.div
                            initial={{ y: 20, opacity: 0 }}
                            animate={{ y: 0, opacity: 1 }}
                            transition={{ delay: 0.5, duration: 0.5 }}
                        >
                            <h3 className={`text-2xl font-bold mb-3 bg-gradient-to-r from-green-500 to-teal-500 bg-clip-text text-transparent ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>All Images Generated Successfully!</h3>
                            <p className={`mb-6 max-w-md mx-auto ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                                Successfully generated <span className="font-bold text-green-600">{completedImages}</span> professional images for your design. They are now available for all users in your group.
                                {groupIds && groupIds.length > 0 && (
                                    <div className={`mt-4 p-3 border rounded-lg ${isDarkMode ? 'bg-gradient-to-r from-green-900/30 to-emerald-900/30 border-green-700' : 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200'}`}>
                                        <div className="flex items-center justify-center mb-2">
                                            <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                                            <span className={`text-sm font-semibold ${isDarkMode ? 'text-green-300' : 'text-green-700'}`}>
                                                Completed for {groupIds.length} group{groupIds.length > 1 ? 's' : ''}
                                            </span>
                                        </div>
                                        <div className="flex flex-wrap justify-center gap-2">
                                            {(groupNames.length > 0 ? groupNames : groupIds.map(id => `Group ${id}`)).map((name, index) => (
                                                <motion.div
                                                    key={index}
                                                    initial={{ scale: 0.8, opacity: 0 }}
                                                    animate={{ scale: 1, opacity: 1 }}
                                                    transition={{ delay: index * 0.1, duration: 0.3 }}
                                                    className={`px-3 py-1 border rounded-full text-xs font-medium shadow-sm flex items-center ${isDarkMode ? 'bg-gray-700 border-green-500 text-green-300' : 'bg-white border-green-300 text-green-700'}`}
                                                >
                                                    <FaCheckCircle className="w-3 h-3 text-green-500 mr-1.5" />
                                                    {name}
                                                </motion.div>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </p>

                            <div className="flex justify-center space-x-6 mb-6">
                                <motion.div
                                    className="text-center"
                                    initial={{ scale: 0.8, opacity: 0 }}
                                    animate={{ scale: 1, opacity: 1 }}
                                    transition={{ delay: 0.6, duration: 0.5 }}
                                    whileHover={{ scale: 1.05 }}
                                >
                                    <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-green-100 to-green-200 flex items-center justify-center mx-auto mb-2 shadow-lg border border-green-200">
                                        <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-green-500 to-teal-400 flex items-center justify-center">
                                            <RiGalleryLine className="text-white text-xl" />
                                        </div>
                                    </div>
                                    <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Images Saved</p>
                                </motion.div>

                                <motion.div
                                    className="text-center"
                                    initial={{ scale: 0.8, opacity: 0 }}
                                    animate={{ scale: 1, opacity: 1 }}
                                    transition={{ delay: 0.7, duration: 0.5 }}
                                    whileHover={{ scale: 1.05 }}
                                >
                                    <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center mx-auto mb-2 shadow-lg border border-blue-200">
                                        <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-400 flex items-center justify-center">
                                            <FaFolder className="text-white text-xl" />
                                        </div>
                                    </div>
                                    <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>In Cloud Storage</p>
                                </motion.div>

                                <motion.div
                                    className="text-center"
                                    initial={{ scale: 0.8, opacity: 0 }}
                                    animate={{ scale: 1, opacity: 1 }}
                                    transition={{ delay: 0.8, duration: 0.5 }}
                                    whileHover={{ scale: 1.05 }}
                                >
                                    <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-purple-100 to-purple-200 flex items-center justify-center mx-auto mb-2 shadow-lg border border-purple-200">
                                        <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-purple-500 to-pink-400 flex items-center justify-center">
                                            <FaCheckCircle className="text-white text-xl" />
                                        </div>
                                    </div>
                                    <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Ready to Use</p>
                                </motion.div>
                            </div>

                            <motion.div
                                initial={{ scale: 0.9, opacity: 0 }}
                                animate={{ scale: 1, opacity: 1 }}
                                transition={{ delay: 0.9, duration: 0.5 }}
                            >
                                <Button
                                    label="Close"
                                    icon="pi pi-check"
                                    onClick={() => {
                                        onHide();
                                    }}
                                    className="p-button-success px-6 py-2 shadow-lg"
                                    style={{
                                        background: "linear-gradient(to right, #10b981, #14b8a6)",
                                        border: "none",
                                        borderRadius: "8px"
                                    }}
                                />
                            </motion.div>
                        </motion.div>
                    </div>
                );

            default:
                return (
                    <div className="text-center p-5">
                        <motion.div
                            initial={{ scale: 0.9, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={{ duration: 0.7, type: "spring" }}
                            className="mb-8 relative"
                        >
                            {/* Glowing background effect */}
                            <div className="absolute inset-0 -m-10 bg-gradient-to-r from-yellow-400 via-orange-400 to-red-500 rounded-full opacity-10 blur-xl"></div>

                            {/* 3D Smartphone with error animation */}
                            <div className="relative w-64 h-[400px] mx-auto">
                                {/* Phone frame with premium finish */}
                                <div className="relative w-full h-full rounded-[32px] overflow-hidden"
                                    style={{
                                        background: "linear-gradient(145deg, #2a2a2a, #3a3a3a)",
                                        boxShadow: "0 15px 35px rgba(0,0,0,0.3), inset 0 2px 2px rgba(255,255,255,0.1), inset 0 -2px 2px rgba(0,0,0,0.8)",
                                        border: "6px solid #222",
                                        transform: "perspective(1000px) rotateY(-5deg) rotateX(5deg)",
                                        transformStyle: "preserve-3d"
                                    }}>

                                    {/* Phone screen with error animation */}
                                    <div className="absolute inset-0 bg-gradient-to-br from-gray-900 to-black overflow-hidden"
                                        style={{
                                            boxShadow: "inset 0 0 15px rgba(255,255,255,0.1)",
                                        }}>

                                        {/* Screen content */}
                                        <div className="relative w-full h-full flex flex-col items-center justify-center p-4 overflow-hidden">
                                            {/* App header */}
                                            <div className="absolute top-0 left-0 right-0 bg-black bg-opacity-70 backdrop-blur-md p-3 flex items-center justify-between">
                                                <div className="text-white text-xs font-medium flex items-center">
                                                    <IoPhonePortraitOutline className="mr-1 text-red-400" />
                                                    <span className="text-red-400">Image Processor</span>
                                                </div>
                                                <div className="flex items-center space-x-2">
                                                    <div className="px-2 py-0.5 bg-red-500 rounded-full text-[10px] text-white font-bold flex items-center">
                                                        <FaExclamationTriangle className="mr-1" size={8} />
                                                        ERROR
                                                    </div>
                                                </div>
                                            </div>

                                            {/* Error Animation */}
                                            <div className="relative w-full h-[280px] mt-12 flex items-center justify-center">
                                                {/* Error visualization */}
                                                <div className="relative">
                                                    {/* Glowing error effect */}
                                                    <motion.div
                                                        className="absolute inset-0 rounded-full"
                                                        style={{
                                                            background: "radial-gradient(circle, rgba(239,68,68,0.6) 0%, rgba(239,68,68,0) 70%)",
                                                            filter: "blur(15px)"
                                                        }}
                                                        animate={{
                                                            scale: [1, 1.3, 1],
                                                            opacity: [0.5, 0.8, 0.5]
                                                        }}
                                                        transition={{
                                                            repeat: Infinity,
                                                            duration: 2,
                                                            ease: "easeInOut"
                                                        }}
                                                    />

                                                    {/* Error icon container */}
                                                    <motion.div
                                                        className="relative w-28 h-28 rounded-full bg-gradient-to-br from-red-500 to-orange-600 flex items-center justify-center"
                                                        animate={{
                                                            boxShadow: [
                                                                "0 0 20px rgba(239,68,68,0.5)",
                                                                "0 0 30px rgba(239,68,68,0.7)",
                                                                "0 0 20px rgba(239,68,68,0.5)"
                                                            ]
                                                        }}
                                                        transition={{
                                                            repeat: Infinity,
                                                            duration: 2,
                                                            ease: "easeInOut"
                                                        }}
                                                    >
                                                        <motion.div
                                                            className="absolute inset-3 rounded-full bg-gradient-to-br from-red-400 to-orange-500"
                                                            animate={{
                                                                opacity: [0.8, 1, 0.8],
                                                                scale: [0.95, 1, 0.95]
                                                            }}
                                                            transition={{
                                                                repeat: Infinity,
                                                                duration: 2,
                                                                ease: "easeInOut"
                                                            }}
                                                        />

                                                        {/* Error Icon */}
                                                        <motion.div
                                                            className="relative z-10"
                                                            animate={{
                                                                rotate: [0, 5, 0, -5, 0],
                                                                scale: [1, 1.05, 1, 1.05, 1]
                                                            }}
                                                            transition={{
                                                                repeat: Infinity,
                                                                duration: 2,
                                                                ease: "easeInOut"
                                                            }}
                                                        >
                                                            <FaExclamationTriangle className="text-white text-5xl" />
                                                        </motion.div>
                                                    </motion.div>

                                                    {/* Animated error particles */}
                                                    {[...Array(12)].map((_, i) => {
                                                        const angle = (i * Math.PI * 2) / 12;
                                                        const distance = 70 + (i % 3) * 15;
                                                        const x = Math.cos(angle) * distance;
                                                        const y = Math.sin(angle) * distance;
                                                        const size = 2 + (i % 3) * 2;

                                                        return (
                                                            <motion.div
                                                                key={`error-particle-${i}`}
                                                                className="absolute rounded-full"
                                                                style={{
                                                                    width: `${size}px`,
                                                                    height: `${size}px`,
                                                                    background: i % 3 === 0
                                                                        ? "#ef4444" // red-500
                                                                        : i % 3 === 1
                                                                            ? "#f97316" // orange-500
                                                                            : "#f59e0b", // amber-500
                                                                    top: "50%",
                                                                    left: "50%",
                                                                    marginTop: `-${size/2}px`,
                                                                    marginLeft: `-${size/2}px`,
                                                                    boxShadow: `0 0 ${size}px ${i % 3 === 0 ? "#ef4444" : i % 3 === 1 ? "#f97316" : "#f59e0b"}`
                                                                }}
                                                                animate={{
                                                                    x: [x * 0.5, x, x * 0.8],
                                                                    y: [y * 0.5, y, y * 0.8],
                                                                    opacity: [0.5, 0.8, 0.5],
                                                                    scale: [0.8, 1.2, 0.8]
                                                                }}
                                                                transition={{
                                                                    repeat: Infinity,
                                                                    duration: 3 + (i % 3),
                                                                    delay: i * 0.1,
                                                                    ease: "easeInOut"
                                                                }}
                                                            />
                                                        );
                                                    })}

                                                    {/* Error message */}
                                                    <motion.div
                                                        className="absolute top-full left-1/2 transform -translate-x-1/2 mt-6 bg-red-900 bg-opacity-30 px-4 py-2 rounded-lg border border-red-500"
                                                        initial={{ opacity: 0, y: -10 }}
                                                        animate={{ opacity: 1, y: 0 }}
                                                        transition={{ delay: 0.5, duration: 0.5 }}
                                                    >
                                                        <div className="text-red-200 text-sm font-bold">Processing Error</div>
                                                        <div className="text-red-100 text-xs mt-1 max-w-[200px] truncate">
                                                            {error || 'Image processing failed'}
                                                        </div>
                                                    </motion.div>
                                                </div>
                                            </div>

                                            {/* Status bar at bottom */}
                                            <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 backdrop-blur-md p-3">
                                                <div className="text-white text-xs mb-2 flex justify-between items-center">
                                                    <span className="flex items-center">
                                                        <motion.span
                                                            className="w-2 h-2 bg-red-500 rounded-full mr-1"
                                                            animate={{ opacity: [0.5, 1, 0.5] }}
                                                            transition={{ repeat: Infinity, duration: 1.5 }}
                                                        />
                                                        System Error Detected
                                                    </span>
                                                </div>

                                                {/* Error progress bar */}
                                                <div className="w-full h-1 bg-gray-800 rounded-full overflow-hidden">
                                                    <motion.div
                                                        className="h-full bg-gradient-to-r from-red-500 via-orange-500 to-red-500"
                                                        style={{ width: "100%" }}
                                                        animate={{
                                                            opacity: [0.7, 1, 0.7],
                                                            x: ["-100%", "0%", "-100%"]
                                                        }}
                                                        transition={{
                                                            opacity: { repeat: Infinity, duration: 2, ease: "easeInOut" },
                                                            x: { repeat: Infinity, duration: 2, ease: "linear" }
                                                        }}
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Dynamic Island - Error state */}
                                    <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-32 h-8 bg-black rounded-full z-10 flex items-center justify-center overflow-hidden">
                                        <motion.div
                                            className="w-full h-full relative flex items-center justify-center"
                                            animate={{
                                                width: ["60%", "95%", "60%"],
                                                borderRadius: ["9999px", "16px", "9999px"]
                                            }}
                                            transition={{
                                                repeat: Infinity,
                                                repeatType: "reverse",
                                                duration: 3,
                                                ease: "easeInOut",
                                                repeatDelay: 2
                                            }}
                                        >
                                            {/* Status indicator */}
                                            <div className="absolute left-2 top-1/2 transform -translate-y-1/2 w-2 h-2 rounded-full bg-red-500"></div>

                                            {/* Status text that appears when expanded */}
                                            <motion.div
                                                className="text-white text-[10px] font-medium flex items-center"
                                                animate={{
                                                    opacity: [0, 1, 0],
                                                    x: [10, 0, 10]
                                                }}
                                                transition={{
                                                    repeat: Infinity,
                                                    repeatType: "reverse",
                                                    duration: 3,
                                                    ease: "easeInOut",
                                                    repeatDelay: 2
                                                }}
                                            >
                                                <span className="ml-4">Error Detected</span>
                                                <FaExclamationTriangle className="ml-1 text-red-500" size={8} />
                                            </motion.div>

                                            {/* Right side icons */}
                                            <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-1">
                                                <div className="w-1.5 h-1.5 rounded-full bg-white"></div>
                                                <div className="w-1.5 h-1.5 rounded-full bg-white"></div>
                                            </div>
                                        </motion.div>
                                    </div>

                                    {/* Phone buttons - more realistic and larger */}
                                    {/* Power button */}
                                    <div className="absolute -right-[10px] top-24 w-5 h-20 bg-gray-800 rounded-l-md shadow-inner flex items-center justify-center">
                                        <div className="w-3 h-16 bg-gray-700 rounded-l-sm"></div>
                                    </div>

                                    {/* Volume up button */}
                                    <div className="absolute -left-[10px] top-24 w-5 h-14 bg-gray-800 rounded-r-md shadow-inner flex items-center justify-center">
                                        <div className="w-3 h-12 bg-gray-700 rounded-r-sm"></div>
                                    </div>

                                    {/* Volume down button */}
                                    <div className="absolute -left-[10px] top-42 w-5 h-14 bg-gray-800 rounded-r-md shadow-inner flex items-center justify-center">
                                        <div className="w-3 h-12 bg-gray-700 rounded-r-sm"></div>
                                    </div>

                                    {/* Silent mode switch */}
                                    <div className="absolute -left-[12px] top-60 w-7 h-10 bg-gray-800 rounded-r-md shadow-inner flex items-center justify-center">
                                        <div className="w-5 h-8 bg-red-600 rounded-r-sm"></div>
                                    </div>

                                    {/* SIM card slot */}
                                    <div className="absolute -right-[10px] top-64 w-5 h-10 bg-gray-800 rounded-l-md shadow-inner flex items-center justify-center">
                                        <div className="w-3 h-8 bg-gray-700 rounded-l-sm"></div>
                                    </div>
                                </div>

                                {/* Phone reflection */}
                                <div className="absolute bottom-[-10px] left-1/2 transform -translate-x-1/2 w-56 h-8 bg-black opacity-20 blur-md rounded-full"></div>
                            </div>

                            {/* Floating error particles */}
                            {[...Array(10)].map((_, i) => (
                                <motion.div
                                    key={`float-error-particle-${i}`}
                                    className="absolute w-2 h-2 rounded-full"
                                    style={{
                                        background: i % 3 === 0
                                            ? "radial-gradient(circle, rgba(239,68,68,0.8) 0%, rgba(239,68,68,0) 70%)"
                                            : i % 3 === 1
                                                ? "radial-gradient(circle, rgba(249,115,22,0.8) 0%, rgba(249,115,22,0) 70%)"
                                                : "radial-gradient(circle, rgba(245,158,11,0.8) 0%, rgba(245,158,11,0) 70%)",
                                        top: `${Math.random() * 100}%`,
                                        left: `${Math.random() * 100}%`,
                                    }}
                                    animate={{
                                        y: [0, -(10 + Math.random() * 20)],
                                        x: [0, (Math.random() * 10) - 5],
                                        opacity: [0, 0.7, 0],
                                        scale: [0, 0.5 + Math.random() * 0.5, 0]
                                    }}
                                    transition={{
                                        repeat: Infinity,
                                        duration: 2 + Math.random() * 2,
                                        delay: Math.random() * 2
                                    }}
                                />
                            ))}
                        </motion.div>

                        <motion.div
                            initial={{ y: 20, opacity: 0 }}
                            animate={{ y: 0, opacity: 1 }}
                            transition={{ delay: 0.3, duration: 0.5 }}
                        >
                            <h3 className="text-2xl font-bold mb-3 text-gray-800 bg-gradient-to-r from-red-500 to-orange-500 bg-clip-text text-transparent">Something Went Wrong</h3>
                            <p className="text-gray-600 mb-6 max-w-md mx-auto">
                                {error || 'There was an issue determining the status of your image processing. Please try again or contact support if the problem persists.'}
                            </p>

                            {/* Error details */}
                            <motion.div
                                className="mb-6 max-w-md mx-auto bg-red-50 border border-red-200 rounded-lg p-4"
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.5, duration: 0.5 }}
                            >
                                <div className="flex items-start">
                                    <div className="flex-shrink-0 mt-0.5">
                                        <FaExclamationTriangle className="text-red-500 text-lg" />
                                    </div>
                                    <div className="ml-3 text-left">
                                        <h4 className="text-sm font-medium text-red-800">Error Details</h4>
                                        <p className="mt-1 text-xs text-red-700">
                                            The image processing encountered an error. This could be due to server load, network issues, or a problem with the input data.
                                        </p>
                                    </div>
                                </div>
                            </motion.div>

                            <div className="flex justify-center space-x-4">
                                <motion.div
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                >
                                    <Button
                                        label="Try Again"
                                        icon="pi pi-refresh"
                                        onClick={fetchStatus}
                                        className="p-button-outlined p-button-warning px-4 py-2 shadow-md"
                                        style={{
                                            borderColor: "#f59e0b",
                                            color: "#f59e0b",
                                            borderRadius: "8px"
                                        }}
                                    />
                                </motion.div>

                                <motion.div
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                >
                                    <Button
                                        label="Close"
                                        icon="pi pi-times"
                                        onClick={() => {
                                            onHide();
                                        }}
                                        className="p-button-danger px-4 py-2 shadow-md"
                                        style={{
                                            background: "linear-gradient(to right, #ef4444, #f97316)",
                                            border: "none",
                                            borderRadius: "8px"
                                        }}
                                    />
                                </motion.div>
                            </div>
                        </motion.div>
                    </div>
                );
        }
    };

    // Don't render the dialog if there's no designId
    if (visible && !designId) {
        console.log("Not rendering modal because designId is missing");
        return null;
    }

    // For debugging - log the current status and designId whenever they change
    console.log(`Modal rendering with designId: ${designId}, status: ${status}`);

    return (
        <Dialog
            visible={visible && !!designId}
            onHide={() => {
                // If status is completed and navigation option is set to go to templates, navigate
                if (status === 'completed' && navigationOptions.goToTemplates) {
                    handleNavigationAfterCompletion();
                }
                onHide();
            }}
            header={
                <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-teal-400 flex items-center justify-center mr-3">
                        <FaImage className="text-white text-sm" />
                    </div>
                    <span className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>Image Processing</span>
                    {status === 'processing' && (
                        <div className={`ml-3 px-2 py-1 rounded-full text-xs font-medium flex items-center ${isDarkMode ? 'bg-blue-900/50 text-blue-300' : 'bg-blue-100 text-blue-700'}`}>
                            <div className="w-2 h-2 bg-blue-500 rounded-full mr-1 animate-pulse"></div>
                            Processing
                        </div>
                    )}
                    {status === 'completed' && (
                        <div className={`ml-3 px-2 py-1 rounded-full text-xs font-medium flex items-center ${isDarkMode ? 'bg-green-900/50 text-green-300' : 'bg-green-100 text-green-700'}`}>
                            <FaCheckCircle className="mr-1 text-green-500" size={10} />
                            Completed
                        </div>
                    )}
                </div>
            }
            style={{ 
                width: '550px', 
                borderRadius: '12px', 
                overflow: 'hidden', 
                maxHeight: '90vh',
                backgroundColor: isDarkMode ? '#1f2937' : '#ffffff',
                border: isDarkMode ? '1px solid #374151' : '1px solid #e5e7eb'
            }}
            modal
            closeOnEscape={status === 'completed'}
            closable={status === 'completed'}
            className={`image-generation-modal ${isDarkMode ? 'dark-modal' : ''}`}
            contentClassName="p-0 overflow-auto"
            footer={null} // Remove footer buttons completely
        >
            {/* Decorative top border */}
            <div className="h-1 w-full bg-gradient-to-r from-blue-500 via-teal-400 to-blue-500"></div>

            <AnimatePresence mode="wait">
                <motion.div
                    key={status}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                >
                    {renderContent()}
                </motion.div>
            </AnimatePresence>
        </Dialog>
    );
};

ImageGenerationModal.propTypes = {
    visible: PropTypes.bool.isRequired,
    designId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    batchId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    useBatchApi: PropTypes.bool,
    useGroupsDesignApi: PropTypes.bool,
    onHide: PropTypes.func.isRequired,
    groupIds: PropTypes.array,
    navigationOptions: PropTypes.shape({
        stayOnPage: PropTypes.bool,
        goToTemplates: PropTypes.bool
    })
};

export default ImageGenerationModal;
