import React from 'react';
import { useDarkMode } from '@contexts/DarkModeContext';

const MainContainer = ({ 
  children, 
  className = '', 
  type = 'container', // container, content, section, wrapper, view
  size = 'default' // small, default, large
}) => {
  const { isDarkMode } = useDarkMode();

  const getContainerClass = () => {
    const baseClasses = 'transition-colors duration-300';
    const sizeClasses = {
      small: 'p-4',
      default: 'p-6',
      large: 'p-8'
    };
    
    const typeClasses = {
      container: 'main-container',
      content: 'main-content',
      section: 'main-section',
      wrapper: 'main-wrapper',
      view: 'main-view'
    };

    return `${baseClasses} ${sizeClasses[size]} ${typeClasses[type]} ${className}`;
  };

  return (
    <div className={getContainerClass()}>
      {children}
    </div>
  );
};

export default MainContainer;
