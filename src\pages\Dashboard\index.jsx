import { useState, useEffect, useMemo } from 'react'
import { useQuery } from 'react-query'
import axiosInstance from "../../config/Axios";
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import './Dashboard.css';

// Icons
import {
  FiCreditCard,
  FiPackage,
  FiUsers,
  FiUser,
  FiActivity,
  FiCalendar,
  FiSettings,
  FiAlertCircle,
  FiCheckCircle,
  FiInfo
} from 'react-icons/fi';

import { FaCreditCard } from 'react-icons/fa';

// Fetch user's own cards count and package info
const fetchUserCardsCount = async () => {
  try {
    const token = localStorage.getItem('token')
    const userId = localStorage.getItem('user_id')

    if (!token || !userId) {
      console.error("Token or user_id not found in localStorage")
      return { count: 0, packageInfo: null }
    }

    const response = await axiosInstance.get(`packages/show-package-by-id/${userId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      }
    })

    // Return the length of the cards array and the package info
    console.log('User Cards:', response.data?.cards)
    console.log('Package Info:', response.data)

    return {
      count: response.data?.cards?.length || 0,
      packageInfo: response.data || null
    }
  } catch (error) {
    console.error('Error fetching user cards count:', error)
    return { count: 0, packageInfo: null }
  }
}

// Fetch members count using the same API as the members data table
const fetchMembersCount = async () => {
  try {
    const token = localStorage.getItem('token')
    const userId = localStorage.getItem('user_id')

    if (!token || !userId) {
      console.error("Token or user_id not found in localStorage")
      return 0
    }

    // Using the same API endpoint as the members data table
    const response = await axiosInstance.post(`/datatable/users/view?page=1&per_page=1&render_html=0`, {
      "order_by": {
        "id": "desc"
      },
      "filters": {},
      "filters_date": null
    }, {
      headers: {
        Authorization: `Bearer ${token}`,
      }
    })

    // Return the total count from pagination
    console.log('Members count:', response.data?.pagination?.total)
    return response.data?.pagination?.total || 0
  } catch (error) {
    console.error('Error fetching members count:', error)
    return 0
  }
}

// Fetch groups count
const fetchGroupsCount = async () => {
  try {
    const token = localStorage.getItem('token')
    const userId = localStorage.getItem('user_id')

    if (!token || !userId) {
      console.error("Token or user_id not found in localStorage")
      return 0
    }

    // Using the groups API endpoint with the correct URL
    const backendUrl = import.meta.env.VITE_BACKEND_URL
    const response = await fetch(`${backendUrl}/groups?users=true`, {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: "application/json",
      }
    })

    if (!response.ok) {
      console.error(`Error fetching groups: ${response.status}`)
      return 0
    }

    const data = await response.json()
    const groupsCount = data.data?.length || 0

    // Log the count
    console.log('Groups count:', groupsCount)
    return groupsCount
  } catch (error) {
    console.error('Error fetching groups count:', error)
    return 0
  }
}

const fetchAllPackages = async () => {
  try {
    const token = localStorage.getItem('token');
    const userId = localStorage.getItem('user_id');

    if (!token || !userId) {
      console.error("Token or user_id not found in localStorage");
      return [];
    }

    const backendUrl = import.meta.env.VITE_BACKEND_URL;

    const apiUrl = `${backendUrl}/packages/show-all-packages`;

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error(`Error fetching packages: ${response.status}`);
      const errorText = await response.text();
      console.error('Error Response Body:', errorText);
      return [];
    }

    const contentType = response.headers.get('Content-Type');
    if (contentType && contentType.includes('application/json')) {
      const data = await response.json();
      console.log('Packages Data:', data);
      return data || [];
    } else {
      const responseText = await response.text();
      console.warn('Received non-JSON response:', responseText);
      return [];
    }

  } catch (error) {
    console.error('Error fetching all packages:', error);
    return [];
  }
};

// Function to fetch latest cards for the current user
const fetchLatestCards = async () => {
  try {
    const token = localStorage.getItem('token')
    const userId = localStorage.getItem('user_id')

    if (!token || !userId) {
      console.error("Token or user_id not found in localStorage")
      return []
    }

    // Using the same API endpoint as used for user cards count
    const response = await axiosInstance.get(`packages/show-package-by-id/${userId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      }
    })

    // Get the user's cards from the response
    const userCards = response.data?.cards || []

    // Sort by created_at date if available, otherwise just take the first few
    const sortedCards = userCards.sort((a, b) => {
      if (a.created_at && b.created_at) {
        return new Date(b.created_at) - new Date(a.created_at)
      }
      return 0
    })

    // Return the latest 3 cards
    return sortedCards.slice(0, 3)
  } catch (error) {
    console.error('Error fetching latest user cards:', error)
    return []
  }
};

// Function to fetch all user cards (used for filtering printed/unprinted cards)
const fetchAllUserCards = async () => {
  try {
    const token = localStorage.getItem('token')
    const userId = localStorage.getItem('user_id')

    if (!token || !userId) {
      console.error("Token or user_id not found in localStorage")
      return []
    }

    // Using the same API endpoint as used for latest cards
    const response = await axiosInstance.get(`packages/show-package-by-id/${userId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      }
    })

    // Get the user's cards from the response
    const userCards = response.data?.cards || []

    // Sort by created_at date if available
    const sortedCards = userCards.sort((a, b) => {
      if (a.created_at && b.created_at) {
        return new Date(b.created_at) - new Date(a.created_at)
      }
      return 0
    })

    return sortedCards
  } catch (error) {
    console.error('Error fetching user cards:', error)
    return []
  }
};

// Fetch member cards statistics for manager
const fetchMemberCardsStats = async () => {
  try {
    const token = localStorage.getItem('token')

    if (!token) {
      console.error("Token not found in localStorage")
      return null
    }

    const response = await axiosInstance.get('package-cards-statistics', {
      headers: {
        Authorization: `Bearer ${token}`,
      }
    })

    return response.data?.data || null
  } catch (error) {
    console.error('Error fetching member cards stats:', error)
    return null
  }
}

// Fetch groups data for manager
const fetchGroupsData = async () => {
  try {
    const token = localStorage.getItem('token')

    if (!token) {
      console.error("Token not found in localStorage")
      return []
    }

    const response = await axiosInstance.get('groups', {
      headers: {
        Authorization: `Bearer ${token}`,
      }
    })

    // تأكد من أن البيانات مصفوفة
    const groups = response.data?.data || response.data || []
    console.log('Groups Data Response:', response.data)
    console.log('Groups Array:', groups)
    return Array.isArray(groups) ? groups : []
  } catch (error) {
    console.error('Error fetching groups data:', error)
    return []
  }
}

// Fetch card types data for manager
const fetchCardTypesData = async () => {
  try {
    const token = localStorage.getItem('token')

    if (!token) {
      console.error("Token not found in localStorage")
      return []
    }

    const response = await axiosInstance.get('card-types', {
      headers: {
        Authorization: `Bearer ${token}`,
      }
    })

    // تأكد من أن البيانات مصفوفة
    const cardTypes = response.data?.data || response.data || []
    console.log('Card Types Data Response:', response.data)
    console.log('Card Types Array:', cardTypes)
    return Array.isArray(cardTypes) ? cardTypes : []
  } catch (error) {
    console.error('Error fetching card types data:', error)
    return []
  }
}

function Dashboard() {
  const { data: userCardsData, isLoading: userCardsLoading, isError: userCardsError } = useQuery('userCardsCount', fetchUserCardsCount)
  const { data: allPackages, isLoading: packagesLoading, isError: packagesError } = useQuery('allPackages', fetchAllPackages)
  const { data: membersCount, isLoading: membersLoading, isError: membersError } = useQuery('membersCount', fetchMembersCount)
  const { data: groupsCount, isLoading: groupsLoading, isError: groupsError } = useQuery('groupsCount', fetchGroupsCount)
  const { data: latestCards, isLoading: latestCardsLoading, isError: latestCardsError } = useQuery('latestCards', fetchLatestCards)
  // Use the same API for all card lists
  const { data: allUserCards, isLoading: allUserCardsLoading, isError: allUserCardsError } = useQuery('allUserCards', fetchAllUserCards)
  const { data: memberCards, isLoading: memberCardsLoading, isError: memberCardsError } = useQuery('memberCardsStats', fetchMemberCardsStats);
  const { data: groupsData, isLoading: groupsLoadingFromStats, isError: groupsErrorFromStats } = useQuery('groupsData', fetchGroupsData);
  const { data: cardTypesData, isLoading: cardTypesLoading, isError: cardTypesError } = useQuery('cardTypesData', fetchCardTypesData);
  
  const [greeting, setGreeting] = useState('');
  const [currentTime, setCurrentTime] = useState('');
  const [isGroupsCardsOverviewOpen, setIsGroupsCardsOverviewOpen] = useState(true);

  // Extract user cards count and package info
  const userCardsCount = userCardsData?.count || 0;
  const userPackageInfo = userCardsData?.packageInfo || null;
  const cardLimit = userPackageInfo?.card_limit || 0;

  // حساب إحصائيات البطاقات حسب النوع مع المجموعات المرتبطة
  const cardsStats = useMemo(() => {
    if (!memberCards || !memberCards.cards_by_type || !groupsData || !Array.isArray(groupsData) || groupsData.length === 0) {
      return {
        total: 0,
        byType: {},
        available: 0,
        assigned: 0
      };
    }

    const stats = {
      total: memberCards.total_cards_package || 0,
      byType: {},
      available: 0,
      assigned: 0
    };

    memberCards.cards_by_type.forEach(cardTypeData => {
      const cardType = cardTypeData.type_name;
      const typeId = cardTypeData.type_id;
      
      // البحث عن نوع البطاقة في cardTypesData للحصول على type_of_connection
      const cardTypeInfo = cardTypesData?.find(ct => ct.id === typeId);
      const typeOfConnection = cardTypeInfo?.type_of_connection || cardType;
      
      const relatedGroups = groupsData.filter(group => {
        return group.card_type_id === typeId;
      });

      const totalCards = cardTypeData[`total_cards_${typeId}`] || 0;
      const availableCards = cardTypeData[`total_cards_${typeId}_available`] || 0;
      const assignedCardsFromAPI = cardTypeData[`total_cards_${typeId}_assign`] || 0;

      const allCards = [
        ...(cardTypeData.cards_available || []).map(card => ({ ...card, isAvailable: true })),
        ...(cardTypeData.cards_assigns || []).map(card => ({ ...card, isAvailable: false }))
      ];

      stats.byType[cardType] = {
        total: totalCards,
        available: availableCards,
        assigned: assignedCardsFromAPI,
        typeId: typeId,
        typeName: cardType,
        cards: allCards.map(card => ({
          id: card.id,
          number: card.number,
          status: card.package_status || 'active',
          assigned: !card.isAvailable,
          userId: card.user_id,
          assignedTo: card.group_info?.assigned_user_name || (card.user_id ? 'Assigned' : null),
          isOwnedByCurrentUser: card.is_owned_by_current_user,
          imagePath: card.image_path,
          packageName: card.package_name,
          isAvailable: card.isAvailable,
          groupInfo: card.group_info ? {
            groupId: card.group_info.group_id,
            groupName: card.group_info.group_name,
            groupTitle: card.group_info.group_title,
            groupDescription: card.group_info.group_description,
            assignedUserName: card.group_info.assigned_user_name,
            assignedUserEmail: card.group_info.assigned_user_email,
            cardUserGroupId: card.group_info.card_user_group_id
          } : null
        })),
        status: 'active',
        settings: {
          width: cardTypeInfo?.setting?.width || cardTypeInfo?.width || 'N/A',
          height: cardTypeInfo?.setting?.height || cardTypeInfo?.height || 'N/A',
          numberOfColors: cardTypeInfo?.number_of_colors || 1,
          typeOfConnection: typeOfConnection
        },
        relatedGroups: relatedGroups.map(group => ({
          id: group.id,
          name: group.title,
          usersCount: group.users?.length || group.users_count || 0,
          usersWithCards: group.users_with_cards_count || 0
        }))
      };

      stats.available += availableCards;
      stats.assigned += assignedCardsFromAPI;
    });

    return stats;
  }, [memberCards, groupsData, cardTypesData]);

  // Calculate active packages count
  const activePackagesCount = allPackages?.filter(pkg => pkg.status === 'active').length || 0

  // Filter cards from the same data source (remove slice to show all cards)
  const printedCards = allUserCards?.filter(card => card.print_status === 'printed') || []
  const unprintedCards = allUserCards?.filter(card =>
    !card.print_status || card.print_status === 'unPrinted' || card.print_status === 'pending'
  ) || []

  // Calculate time-based greeting
  useEffect(() => {
    const updateGreeting = () => {
      const hour = new Date().getHours();
      let greetingText = '';

      if (hour < 12) greetingText = 'Good Morning';
      else if (hour < 18) greetingText = 'Good Afternoon';
      else greetingText = 'Good Evening';

      setGreeting(greetingText);
    };

    const updateTime = () => {
      const now = new Date();
      const options = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      };
      setCurrentTime(now.toLocaleDateString(undefined, options));
    };

    updateGreeting();
    updateTime();

    const interval = setInterval(updateTime, 60000); // Update time every minute

    return () => clearInterval(interval);
  }, []);



  if (userCardsLoading || packagesLoading || membersLoading || groupsLoading || latestCardsLoading || allUserCardsLoading || memberCardsLoading || groupsLoadingFromStats || cardTypesLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center main-container">
        <div className="animate-pulse flex flex-col items-center">
          <div className="w-16 h-16 border-4 border-t-transparent border-[#00c3ac] rounded-full animate-spin"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading dashboard data...</p>
        </div>
      </div>
    );
  }

  if (userCardsError || packagesError || membersError || groupsError || latestCardsError || allUserCardsError || memberCardsError || groupsErrorFromStats || cardTypesError) {
    return (
      <div className="w-full h-full flex items-center justify-center main-container">
        <div className="bg-red-100 dark:bg-red-900/20 border border-red-400 dark:border-red-500 text-red-700 dark:text-red-400 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> Unable to load dashboard data. Please try again later.</span>
        </div>
      </div>
    );
  }

  return (
    <section className='w-full flex flex-col rounded-[6px] min-h-full main-section p-[20px]'>
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full bg-gradient-to-r from-[#00c3ac] to-[#02aa96] p-6 rounded-[10px] shadow-lg mb-6 text-white"
      >
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">{greeting}, {localStorage.getItem("user_name") || "User"}</h1>
            <p className="text-white/80 mt-1">{currentTime}</p>
          </div>
          <div className="hidden md:flex space-x-2">
            <Link to="/manager/settings" className="bg-white/20 hover:bg-white/30 p-2 rounded-full transition-all">
              <FiSettings size={20} />
            </Link>
            <Link to="/manager/billing" className="bg-white/20 hover:bg-white/30 p-2 rounded-full transition-all">
              <FiActivity size={20} />
            </Link>
          </div>
        </div>
      </motion.div>

      {/* Stats Cards */}
      <div className="w-full grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className="dashboard-white-container p-5 rounded-[10px] shadow-md border border-gray-200 border-l-4 border-l-[#00c3ac] hover:shadow-lg transition-all"
        >
          <div className="flex items-start justify-between">
            <div>
              <p className="text-gray-500 text-sm font-medium">Your Cards</p>
              <h3 className="text-2xl font-bold mt-2">{userCardsCount}</h3>
            </div>
            <div className="bg-[#00c3ac]/10 p-3 rounded-full">
              <FiCreditCard size={20} className="text-[#00c3ac]" />
            </div>
          </div>
          <div className="mt-4 text-xs flex items-center text-green-600">
            <FiInfo className="mr-1" />
            <span>{unprintedCards.length} Unprinted Cards </span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="dashboard-white-container p-5 rounded-[10px] shadow-md border border-gray-200 border-l-4 border-l-blue-500 hover:shadow-lg transition-all"
        >
          <div className="flex items-start justify-between">
            <div>
              <p className="text-gray-500 text-sm font-medium">Active Packages</p>
              <h3 className="text-2xl font-bold mt-2">{activePackagesCount}</h3>
            </div>
            <div className="bg-blue-500/10 p-3 rounded-full">
              <FiPackage size={20} className="text-blue-500" />
            </div>
          </div>
          <div className="mt-4 text-xs flex items-center text-blue-600">
            {activePackagesCount === 0 ? (
              <>
                <FiAlertCircle className="mr-1" />
                <span>No active packages. You can purchase one
                  <Link to="/manager/Packages">
                    <span className="text-blue-600 hover:underline"> here</span>
                  </Link>
                </span>
              </>
            ) : (
              <>
                <FiCheckCircle className="mr-1" />
                <span>All packages running smoothly</span>
              </>
            )}
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: 0.3 }}
          className="dashboard-white-container p-5 rounded-[10px] shadow-md border border-gray-200 border-l-4 border-l-purple-500 hover:shadow-lg transition-all"
        >
          <div className="flex items-start justify-between">
            <div>
              <p className="text-gray-500 text-sm font-medium">Active Members</p>
              <h3 className="text-2xl font-bold mt-2">{membersCount || 0}</h3>
            </div>
            <div className="bg-purple-500/10 p-3 rounded-full">
              <FiUser size={20} className="text-purple-500" />
            </div>
          </div>
          {/* <div className="mt-4 text-xs flex items-center text-purple-600">
            <FiTrendingUp className="mr-1" />
            <span>{membersCount ? `${membersCount} active members` : 'No members yet'}</span>
          </div> */}
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: 0.4 }}
          className="dashboard-white-container p-5 rounded-[10px] shadow-md border border-gray-200 border-l-4 border-l-green-500 hover:shadow-lg transition-all"
        >
          <div className="flex items-start justify-between">
            <div>
              <p className="text-gray-500 text-sm font-medium">Groups</p>
              <h3 className="text-2xl font-bold mt-2">{groupsCount || 0}</h3>
            </div>
            <div className="bg-green-500/10 p-3 rounded-full">
              <FiUsers size={20} className="text-green-500" />
            </div>
          </div>
          {/* <div className="mt-4 text-xs flex items-center text-green-600">
            <FiCheckCircle className="mr-1" />
            <span>{groupsCount ? `${groupsCount} active groups` : 'No groups created yet'}</span>
          </div> */}
        </motion.div>
      </div>

      {/* Main Content Area - Three Card Lists */}
      <div className="w-full grid grid-cols-1 lg:grid-cols-3 md:grid-cols-2 gap-6">
        {/* Printed Cards Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="dashboard-white-container p-5 border border-gray-200 rounded-[10px] shadow-md"
        >
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-bold text-gray-700">Printed Cards</h2>
            <span className="text-green-600 text-sm font-medium bg-green-50 px-2 py-1 rounded-full">
              {printedCards.length} cards
            </span>
          </div>

          {printedCards && printedCards.length > 0 ? (
            <div className="space-y-4 max-h-96 overflow-y-auto pr-2">
              {printedCards.map((card) => (
                <div key={card.id} className="flex items-start space-x-3 pb-3 border-b border-gray-100 last:border-b-0">
                  <div className="p-2 rounded-full bg-green-600/10 text-green-600 flex-shrink-0">
                    <FiCreditCard />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex justify-between items-start">
                      <p className="text-sm font-medium truncate pr-2">{card.name || 'Unnamed Card'}</p>
                      <p className="text-xs text-gray-500 flex-shrink-0">{card.number || 'No Number'}</p>
                    </div>
                    <p className="text-xs text-gray-500 truncate">Type: {card.card_type?.name || 'Unknown Type'}</p>
                    <p className="text-xs text-gray-500 truncate">Template: {card.template?.name || card.design_template || 'Default'}</p>
                    <p className="text-xs text-gray-500 truncate">Group: {card.group?.title || card.group_name || 'No Group'}</p>
                    <p className="text-xs text-gray-400 mt-1">
                      Printed {card.printed_at
                        ? new Date(card.printed_at).toLocaleDateString(undefined, {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                          })
                        : 'Date unknown'}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-48 text-gray-500">
              <FiCreditCard size={32} className="mb-2" />
              <p className="text-sm">No printed cards</p>
            </div>
          )}
        </motion.div>

        {/* Unprinted Cards Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="dashboard-white-container p-5 border border-gray-200 rounded-[10px] shadow-md"
        >
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-bold text-gray-700">Unprinted Cards</h2>
            <span className="text-orange-600 text-sm font-medium bg-orange-50 px-2 py-1 rounded-full">
              {unprintedCards.length} cards
            </span>
          </div>

          {unprintedCards && unprintedCards.length > 0 ? (
            <div className="space-y-4 max-h-96 overflow-y-auto pr-2">
              {unprintedCards.map((card) => (
                <div key={card.id} className="flex items-start space-x-3 pb-3 border-b border-gray-100 last:border-b-0">
                  <div className="p-2 rounded-full bg-orange-600/10 text-orange-600 flex-shrink-0">
                    <FiCreditCard />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex justify-between items-start">
                      <p className="text-sm font-medium truncate pr-2">{card.name || 'Unnamed Card'}</p>
                      <p className="text-xs text-gray-500 flex-shrink-0">{card.number || 'No Number'}</p>
                    </div>
                    <p className="text-xs text-gray-500 truncate">Type: {card.card_type?.name || 'Unknown Type'}</p>
                    <p className="text-xs text-gray-500 truncate">Template: {card.template?.name || card.design_template || 'Default'}</p>
                    <p className="text-xs text-gray-500 truncate">Group: {card.group?.title || card.group_name || 'No Group'}</p>
                    <p className="text-xs text-gray-400 mt-1">
                      Created {card.created_at
                        ? new Date(card.created_at).toLocaleDateString(undefined, {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                          })
                        : 'Date unknown'}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-48 text-gray-500">
              <FiCreditCard size={32} className="mb-2" />
              <p className="text-sm">No unprinted cards</p>
            </div>
          )}
        </motion.div>

        {/* Latest Cards Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="dashboard-white-container p-5 border border-gray-200 rounded-[10px] shadow-md"
        >
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-bold text-gray-700">Latest Cards</h2>
            <Link to="/manager/my_cards" className="text-[#00c3ac] text-sm hover:underline">
              View All
            </Link>
          </div>

          {latestCards && latestCards.length > 0 ? (
            <>
              <div className="space-y-4">
                {latestCards.map((card) => (
                  <div key={card.id} className="flex items-start space-x-3 pb-3 border-b border-gray-100 last:border-b-0">
                    <div className="p-2 rounded-full bg-[#00c3ac]/10 text-[#00c3ac] flex-shrink-0">
                      <FiCreditCard />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex justify-between items-start">
                        <p className="text-sm font-medium truncate pr-2">{card.name || 'Unnamed Card'}</p>
                        <p className="text-xs text-gray-500 flex-shrink-0">{card.number || 'No Number'}</p>
                      </div>
                      <p className="text-xs text-gray-500 truncate">Type: {card.card_type?.name || 'Unknown Type'}</p>
                      <p className="text-xs text-gray-500 truncate">Template: {card.template?.name || card.design_template || 'Default'}</p>
                      <p className="text-xs text-gray-500 truncate">Group: {card.group?.title || card.group_name || 'No Group'}</p>
                      <p className="text-xs text-gray-400 mt-1">
                        Last update {card.created_at
                          ? new Date(card.updated_at).toLocaleDateString(undefined, {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric'
                            })
                          : 'Date unknown'}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Divider */}
              <div className="my-2 border-t border-gray-200"></div>

              {/* Card Usage Progress Section */}
              {cardLimit > 0 && (
                <div className="mt-2">
                  <div className="flex justify-between items-center mb-3">
                    <h3 className="text-sm font-medium text-gray-700">Card Usage</h3>
                    <p className="text-xs font-medium text-[#00c3ac] bg-transparent px-2 py-1 rounded-full border border-[#00c3ac]">
                      {userCardsCount}/{cardLimit}
                    </p>
                  </div>
                  <div className="w-full h-4 bg-gray-100 rounded-full overflow-hidden border border-gray-200 shadow-inner">
                    <div
                      className="h-full bg-gradient-to-r from-[#00c3ac] to-[#82f570] rounded-full transition-all duration-500 shadow-sm"
                      style={{ width: `${Math.min((userCardsCount / cardLimit) * 100, 100)}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    {cardLimit - userCardsCount > 0
                      ? `You have ${cardLimit - userCardsCount} cards remaining in your package`
                      : 'You have reached your card limit'}
                  </p>
                </div>
              )}
            </>
          ) : (
            <div className="flex flex-col items-center justify-center h-48 text-gray-500">
              <FiCreditCard size={32} className="mb-2" />
              <p className="text-sm">No cards available</p>
              <Link to="/manager/Packages" className="mt-2 text-[#00c3ac] hover:underline text-sm">
                Purchase a package to access cards
              </Link>
            </div>
          )}
        </motion.div>
      </div>

      {/* Groups Cards Overview Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
        className="w-full bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 rounded-xl shadow-lg p-6 border border-blue-200 mt-6"
      >
        <div 
          className="flex items-center justify-between mb-6"
        >
          <div className="flex items-center gap-4">
            <div className="p-4 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg">
              <FaCreditCard className="text-white text-2xl" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-800">Groups Cards Overview</h2>
              <p className="text-sm text-gray-600">Comprehensive view of your card inventory by group</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="text-right">
              <div className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                {cardsStats.total}
              </div>
              <div className="text-sm text-gray-500 font-medium">Total Cards</div>
            </div>
            <button
              onClick={() => setIsGroupsCardsOverviewOpen(!isGroupsCardsOverviewOpen)}
              className="group relative p-3 bg-gradient-to-r from-gray-50 to-gray-100 hover:from-blue-50 hover:to-indigo-50 rounded-xl border border-gray-200 hover:border-blue-300 transition-all duration-300 shadow-sm hover:shadow-md mr-5"
              title={isGroupsCardsOverviewOpen ? "Hide cards overview" : "Show cards overview"}
            >
              <svg 
                className={`w-5 h-5 text-gray-600 group-hover:text-blue-600 transition-all duration-300 ${isGroupsCardsOverviewOpen ? 'rotate-180' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
            </button>
          </div>
        </div>

        {isGroupsCardsOverviewOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            {Object.keys(cardsStats.byType).length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <FaCreditCard size={48} className="mx-auto mb-4 text-gray-300" />
                <p className="text-lg font-medium">No card data available</p>
                <p className="text-sm">Card statistics will appear here when available</p>
              </div>
            ) : (
              <>
                {/* Cards by Type */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                  {Object.entries(cardsStats.byType).map(([cardType, stats], index) => (
                    <motion.div
                      key={cardType}
                      className="bg-white rounded-xl p-5 border border-gray-200 hover:shadow-xl transition-all duration-300"
                      whileHover={{ scale: 1.02, y: -5 }}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                    >
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-3">
                          {(cardType === 'NFC' || stats.settings?.typeOfConnection === 'NFC') ? (
                            <div className="p-3 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl">
                              <svg className="w-5 h-5 text-blue-600" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 4C7.58 4 4 7.58 4 12C4 16.42 7.58 20 12 20C16.42 20 20 16.42 20 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                                <path d="M12 8C9.79 8 8 9.79 8 12C8 14.21 9.79 16 12 16C14.21 16 16 14.21 16 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                              </svg>
                            </div>
                          ) : (
                            <div className="p-3 bg-gradient-to-br from-purple-100 to-purple-200 rounded-xl">
                              <svg className="w-5 h-5 text-purple-600" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M6 8L18 16L12 22V2L18 8L6 16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              </svg>
                            </div>
                          )}
                          <div>
                            <span className="font-bold text-gray-800 text-lg">{stats.typeName || cardType} <span className="text-green-600">Groups</span></span>
                            
                            {/* Related Groups */}
                            {stats.relatedGroups && stats.relatedGroups.length > 0 && (
                              <div className="mt-1">
                                <div className="flex items-center gap-1 text-xs text-gray-500">
                                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                  </svg>
                                  <span>{stats.relatedGroups.length} groups</span>
                                </div>
                              </div>
                            )}
                            
                            {/* Card Type Settings */}
                            <div className="mt-2 space-y-1">
                              <div className="flex items-center gap-2 text-xs">
                                <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                                </svg>
                                <span className="text-gray-600">
                                  {stats.settings?.width || 'N/A'} × {stats.settings?.height || 'N/A'} px
                                </span>
                              </div>
                              <div className="flex items-center gap-2 text-xs">
                                <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
                                </svg>
                                <span className="text-gray-600">
                                  {stats.settings?.numberOfColors || 1} colors
                                </span>
                              </div>
                              <div className="flex items-center gap-2 text-xs">
                                <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                                </svg>
                                <span className="text-gray-600 capitalize">
                                  {stats.settings?.typeOfConnection || cardType}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <span className="text-2xl font-bold text-gray-700">{stats.total}</span>
                          <div className="text-xs text-gray-500">Total</div>
                        </div>
                      </div>
                      
                      {/* Progress Bar */}
                      <div className="mb-3">
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-gray-600">Usage</span>
                          <span className="font-medium text-gray-700">
                            {stats.total > 0 ? Math.round((stats.assigned / stats.total) * 100) : 0}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <motion.div 
                            className="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full"
                            initial={{ width: 0 }}
                            animate={{ width: `${stats.total > 0 ? (stats.assigned / stats.total) * 100 : 0}%` }}
                            transition={{ duration: 1, delay: 0.5 }}
                          ></motion.div>
                        </div>
                      </div>

                      <div className="flex justify-between text-sm mb-4">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                          <span className="text-green-700 font-medium">{stats.available}</span>
                          <span className="text-gray-500">Available</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                          <span className="text-blue-700 font-medium">{stats.assigned}</span>
                          <span className="text-gray-500">Assigned</span>
                        </div>
                      </div>

                      {/* Card Details Expandable Section */}
                      <div className="border-t border-gray-100 pt-3">
                        <details className="group">
                          <summary className="flex items-center justify-between cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900">
                            <span>View Card Details</span>
                            <svg className="w-4 h-4 transform group-open:rotate-180 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                            </svg>
                          </summary>
                          <div className="mt-3 space-y-3">
                            {/* Related Groups Section */}
                            {stats.relatedGroups && stats.relatedGroups.length > 0 && (
                              <div className="bg-blue-50 rounded-lg p-3">
                                <h5 className="text-xs font-semibold text-blue-800 mb-2 flex items-center gap-1">
                                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                  </svg>
                                  Related Groups ({stats.relatedGroups.length})
                                </h5>
                                <div className="space-y-1">
                                  {stats.relatedGroups.map((group) => (
                                    <div key={group.id} className="flex items-center justify-between text-xs bg-white rounded px-2 py-1">
                                      <span className="text-gray-700 font-medium">{group.name}</span>
                                      <span className="text-gray-500">{group.usersCount} members</span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                            
                            {/* Cards List */}
                            <div className="space-y-2 max-h-40 overflow-y-auto">
                              {stats.cards.map((card, cardIndex) => (
                                <div key={card.id || cardIndex} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg text-xs">
                                  <div className="flex items-center gap-2">
                                    <div className={`w-2 h-2 rounded-full ${card.assigned ? 'bg-blue-500' : 'bg-green-500'}`}></div>
                                    <span className="font-mono font-medium">{card.number}</span>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                                      card.assigned 
                                        ? 'bg-blue-100 text-blue-700' 
                                        : 'bg-green-100 text-green-700'
                                    }`}>
                                      {card.assigned ? 'Assigned' : 'Available'}
                                    </span>
                                    {card.assigned && card.assignedTo && (
                                      <span className="text-gray-600 truncate max-w-20" title={card.assignedTo}>
                                        → {card.assignedTo}
                                      </span>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </details>
                      </div>
                    </motion.div>
                  ))}
                </div>

                {/* Summary Stats */}
                <div className="bg-white rounded-xl p-6 border border-gray-200">
                  <h4 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
                    <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    Summary Statistics
                  </h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                    <motion.div 
                      className="text-center p-4 bg-green-50 rounded-lg border border-green-200"
                      whileHover={{ scale: 1.05 }}
                      transition={{ duration: 0.2 }}
                    >
                      <div className="text-3xl font-bold text-green-600 mb-1">{cardsStats.available}</div>
                      <div className="text-sm text-green-700 font-medium">Available</div>
                      <div className="text-xs text-green-600 mt-1">Ready to assign</div>
                    </motion.div>
                    <motion.div 
                      className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200"
                      whileHover={{ scale: 1.05 }}
                      transition={{ duration: 0.2 }}
                    >
                      <div className="text-3xl font-bold text-blue-600 mb-1">{cardsStats.assigned}</div>
                      <div className="text-sm text-blue-700 font-medium">Assigned</div>
                      <div className="text-xs text-blue-600 mt-1">In use</div>
                    </motion.div>
                    <motion.div 
                      className="text-center p-4 bg-purple-50 rounded-lg border border-purple-200"
                      whileHover={{ scale: 1.05 }}
                      transition={{ duration: 0.2 }}
                    >
                      <div className="text-3xl font-bold text-purple-600 mb-1">{Object.keys(cardsStats.byType).length}</div>
                      <div className="text-sm text-purple-700 font-medium">Card Types</div>
                      <div className="text-xs text-purple-600 mt-1">Different varieties</div>
                    </motion.div>
                    <motion.div 
                      className="text-center p-4 bg-orange-50 rounded-lg border border-orange-200"
                      whileHover={{ scale: 1.05 }}
                      transition={{ duration: 0.2 }}
                    >
                      <div className="text-3xl font-bold text-orange-600 mb-1">
                        {cardsStats.total > 0 ? Math.round((cardsStats.assigned / cardsStats.total) * 100) : 0}%
                      </div>
                      <div className="text-sm text-orange-700 font-medium">Usage Rate</div>
                      <div className="text-xs text-orange-600 mt-1">Efficiency</div>
                    </motion.div>
                  </div>
                </div>

                {/* Detailed Card Types Information */}
                <div className="bg-white rounded-xl p-6 border border-gray-200 mt-6">
                  <h4 className="text-lg font-bold text-gray-800 mb-6 flex items-center gap-2">
                    <svg className="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Detailed Card Types Information
                  </h4>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Card Type</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Available</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigned</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Usage %</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Related Groups</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {Object.entries(cardsStats.byType).map(([cardType, stats]) => (
                          <tr key={cardType} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 h-10 w-10">
                                  {(cardType === 'NFC' || stats.settings?.typeOfConnection === 'NFC') ? (
                                    <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                      <svg className="w-5 h-5 text-blue-600" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M12 4C7.58 4 4 7.58 4 12C4 16.42 7.58 20 12 20C16.42 20 20 16.42 20 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                                        <path d="M12 8C9.79 8 8 9.79 8 12C8 14.21 9.79 16 12 16C14.21 16 16 14.21 16 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                                      </svg>
                                    </div>
                                  ) : (
                                    <div className="h-10 w-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                      <svg className="w-5 h-5 text-purple-600" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M6 8L18 16L12 22V2L18 8L6 16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                      </svg>
                                    </div>
                                  )}
                                </div>
                                <div className="ml-4">
                                  <div className="text-sm font-medium text-gray-900">{stats.typeName || cardType}</div>
                                  <div className="text-sm text-gray-500">{stats.settings?.typeOfConnection || cardType}</div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{stats.total}</td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {stats.available}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {stats.assigned}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {stats.total > 0 ? Math.round((stats.assigned / stats.total) * 100) : 0}%
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {stats.relatedGroups?.length || 0} groups
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </>
            )}
          </motion.div>
        )}
      </motion.div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.6 }}
        className="dashboard-white-container w-full p-5 border border-gray-200 rounded-[10px] shadow-md mt-6"
      >
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-bold text-gray-700">Quick Actions</h2>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Link
            to="/users/groups"
            className="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-all duration-300 hover:-translate-y-2 hover:shadow-md"
          >
            <div className="p-3 bg-green-500/10 rounded-full mb-2">
              <FiUsers size={24} className="text-green-500" />
            </div>
            <span className="text-sm font-medium text-gray-700">Manage Groups</span>
          </Link>

          <Link
            to="/users/members"
            className="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-all duration-300 hover:-translate-y-2 hover:shadow-md"
          >
            <div className="p-3 bg-purple-500/10 rounded-full mb-2">
              <FiUser size={24} className="text-purple-500" />
            </div>
            <span className="text-sm font-medium text-gray-700">Manage Members</span>
          </Link>

          <Link
            to="/manager/Packages"
            className="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-all duration-300 hover:-translate-y-2 hover:shadow-md"
          >
            <div className="p-3 bg-blue-500/10 rounded-full mb-2">
              <FiPackage size={24} className="text-blue-500" />
            </div>
            <span className="text-sm font-medium text-gray-700">View Packages</span>
          </Link>

          <Link
            to="/manager/billing"
            className="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-all duration-300 hover:-translate-y-2 hover:shadow-md"
          >
            <div className="p-3 bg-amber-500/10 rounded-full mb-2">
              <FiCalendar size={24} className="text-amber-500" />
            </div>
            <span className="text-sm font-medium text-gray-700">Billing History</span>
          </Link>
        </div>
      </motion.div>
    </section>
  )
}

export default Dashboard