import { useGetCardsTypes } from '@quires';
import Container from '@components/Container';
import { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import CreateTypeForm from '../Cards/CreateTypeForm';
import { Toast } from 'primereact/toast';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import { motion, AnimatePresence } from 'framer-motion';
import { FiChevronRight, FiSearch, FiTrash } from 'react-icons/fi';
import CardCarousel from './components/CardCarousel';

function CardsIndex() {
  const { data, isLoading, refetch } = useGetCardsTypes();
  const [isCreateTypeModalOpen, setIsCreateTypeModalOpen] = useState(false);
  const [editData, setEditData] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [localData, setLocalData] = useState([]);
  const [selectedCard, setSelectedCard] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isMobile, setIsMobile] = useState(false);
  const toast = useRef(null);
  const backendUrl = import.meta.env.VITE_BACKEND_URL;
  const token = localStorage.getItem("token");

  // Mobile detection useEffect
  useEffect(() => {
    const handleResize = () => {
      const mobileView = window.innerWidth < 768;
      setIsMobile(mobileView);
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    if (data) {
      setLocalData(data);
    }
  }, [data]);

  // Filter cards based on search term (memoized for performance)
  const filteredCards = useMemo(() => {
    if (!searchTerm.trim()) return localData;

    const searchLower = searchTerm.toLowerCase();
    return localData.filter(card =>
      card.name?.toLowerCase().includes(searchLower) ||
      card.type_of_connection?.toLowerCase().includes(searchLower)
    );
  }, [localData, searchTerm]);

  const openCreateTypeModal = () => {
    setIsEditMode(false);
    setIsCreateTypeModalOpen(true);
    setSelectedCard(null);
  };

  const handleEdit = (cardData) => {
    setEditData(cardData);
    setIsEditMode(true);
    setIsCreateTypeModalOpen(true);
  };

  const resetEditMode = () => {
    setIsEditMode(false);
    setEditData(null);
  };

  const handleCardSelect = useCallback((card) => {
    setSelectedCard(card);
  }, []);

  const handleCardEdit = useCallback((card) => {
    handleEdit(card);
  }, []);

  const deleteCardType = useCallback(async (id) => {
    try {
      const response = await fetch(`${backendUrl}/card-types/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: 'Card type deleted successfully',
          life: 3000
        });
        setLocalData(localData.filter(item => item.id !== id));
      } else {
        const errorData = await response.json();
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: errorData.message || 'Failed to delete card type',
          life: 3000
        });
      }
    } catch (error) {
      console.error('Error:', error);
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'An error occurred while deleting card type',
        life: 3000
      });
    }
  }, [localData, toast, backendUrl, token]);

  const handleDelete = useCallback((id) => {
    confirmDialog({
      message: 'Are you sure you want to delete this card type?',
      header: 'Confirmation',
      icon: 'pi pi-exclamation-triangle',
      accept: () => deleteCardType(id),
      reject: () => {}
    });
  }, [deleteCardType]);

  const handleCardDelete = useCallback((card) => {
    handleDelete(card.id);
  }, [handleDelete]);

  return (
    <Container className="w-full">
      <Toast ref={toast} />
      <ConfirmDialog />

      {/* Professional Header Section */}
      <div className="w-full mb-8">
        <div className="w-full">
          {/* Header Background with Light Gradient */}
          <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 dark:from-gray-800 dark:via-gray-700 dark:to-gray-600 shadow-xl border border-gray-200 dark:border-gray-600 cards-header-container">
            {/* Animated Background Pattern */}
            <div className="absolute inset-0 opacity-5">
              <div className="absolute inset-0" style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                backgroundSize: '60px 60px'
              }}></div>
        </div>

            {/* Floating Elements */}
            <div className="absolute top-4 right-4 w-20 h-20 bg-blue-200/30 rounded-full blur-xl"></div>
            <div className="absolute bottom-4 left-4 w-16 h-16 bg-purple-200/30 rounded-full blur-xl"></div>
                
            {/* Header Content */}
            <div className="relative z-10 p-4">
              {/* Title Section - Improved Mobile Layout */}
              <div className={`${isMobile ? 'flex flex-col space-y-3 mb-4' : 'flex flex-row items-center justify-between mb-4'}`}>
                <div className={`${isMobile ? 'w-full' : 'mb-0'}`}>
                  <div className={`flex items-center gap-3 ${isMobile ? 'justify-center' : ''}`}>
                    <div className="relative">
                      <div className={`${isMobile ? 'w-10 h-10' : 'w-12 h-12'} bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center shadow-lg`}>
                        <svg className={`text-white ${isMobile ? 'text-base' : 'text-lg'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                        </svg>
                      </div>
                      <div className={`absolute -top-1 -right-1 ${isMobile ? 'w-4 h-4' : 'w-5 h-5'} bg-green-500 rounded-full flex items-center justify-center border-2 border-white shadow-md`}>
                        <span className={`text-white ${isMobile ? 'text-xs' : 'text-xs'} font-bold`}>
                          {filteredCards?.length || 0}
                        </span>
                      </div>
                    </div>
                    <div className={`${isMobile ? 'text-center' : ''}`}>
                      <h1 className={`${isMobile ? 'text-lg' : 'text-xl lg:text-2xl'} font-bold text-gray-900 dark:text-gray-100 mb-0.5`}>
                        Card Types Management
                      </h1>
                      <p className={`text-gray-600 dark:text-gray-400 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                        {isMobile ? 'Manage your card types' : 'Create and manage your card type designs efficiently'}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Desktop Create Button */}
                {!isMobile && (
                  <motion.button
                    onClick={openCreateTypeModal}
                    className="group relative px-6 py-3 bg-gradient-to-r from-emerald-500 to-teal-600 text-white font-semibold rounded-xl hover:from-emerald-600 hover:to-teal-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 flex items-center gap-2 border border-emerald-400/30"
                  >
                    <div className="relative">
                      <span className="text-base group-hover:rotate-90 transition-transform duration-300">+</span>
                    </div>
                    <span className="text-base">Create New Type</span>
                    <div className="absolute inset-0 bg-gradient-to-r from-emerald-400/20 to-teal-500/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </motion.button>
                )}
              </div>

              {/* Search and Filters Section - Improved Mobile Layout */}
              <div className={`${isMobile ? 'space-y-3' : 'grid grid-cols-1 lg:grid-cols-12 gap-3 items-end'}`}>
                {/* Search Input - Mobile Optimized */}
                <div className={`relative ${isMobile ? 'w-full order-1' : 'lg:col-span-5'}`}>
          <div className="relative">
                    <div className={`absolute inset-y-0 left-0 ${isMobile ? 'pl-3' : 'pl-4'} flex items-center pointer-events-none z-10`}>
                      <div className={`${isMobile ? 'w-6 h-6' : 'w-8 h-8'} bg-gradient-to-br from-slate-700 via-slate-600 to-slate-800 rounded-xl flex items-center justify-center shadow-lg border border-slate-500/30 group-hover:scale-110 transition-all duration-300 hover:shadow-xl hover:from-slate-600 hover:via-slate-500 hover:to-slate-700`}>
                        <FiSearch className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-white group-hover:scale-110 transition-transform duration-300 group-hover:rotate-12`} />
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      </div>
                    </div>
            <input
              type="text"
                      placeholder={isMobile ? "Search card types..." : "Search card types by name, connection type..."}
                      className={`w-full ${isMobile ? 'pl-12 pr-3 py-2' : 'pl-16 pr-4 py-2.5'} bg-white/90 dark:bg-gray-700/90 backdrop-blur-sm border border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300 ${isMobile ? 'text-sm' : 'text-sm'} shadow-sm group`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-50/50 to-purple-50/50 rounded-xl opacity-0 focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                  </div>
                </div>

                {/* Stats Section - Mobile Optimized */}
                <div className={`${isMobile ? 'order-3 mt-2' : 'lg:col-span-3'}`}>
                  <div className={`grid grid-cols-3 ${isMobile ? 'gap-2' : 'gap-3'}`}>
                    {/* Total Card Types */}
                    <div className={`bg-white/80 dark:bg-gray-700/80 backdrop-blur-sm rounded-xl ${isMobile ? 'p-1.5' : 'p-2'} border border-gray-200 dark:border-gray-600 shadow-sm hover:shadow-lg transition-all duration-300 group`}>
                      <div className="text-center">
                        <div className={`flex items-center justify-center ${isMobile ? 'gap-1 mb-0.5' : 'gap-2 mb-1'}`}>
                          <div className="relative group">
                            <svg className={`text-blue-600 group-hover:text-blue-700 transition-colors duration-300 group-hover:scale-110 group-hover:rotate-6`} width={isMobile ? 14 : 20} height={isMobile ? 14 : 20} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                            </svg>
                            <div className="absolute inset-0 bg-blue-100 dark:bg-blue-900 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                          </div>
                          <div className={`${isMobile ? 'text-lg' : 'text-2xl'} font-bold text-gray-900 dark:text-gray-100 group-hover:text-blue-700 transition-colors duration-300`}>
                            {localData?.length || 0}
                          </div>
                        </div>
                        <div className={`${isMobile ? 'text-xs' : 'text-xs'} text-gray-600 dark:text-gray-400 group-hover:text-blue-600 transition-colors duration-300`}>
                          {isMobile ? 'Total' : 'Card Types'}
                        </div>
                      </div>
                    </div>

                    {/* Bluetooth Types */}
                    <div className={`bg-white/80 dark:bg-gray-700/80 backdrop-blur-sm rounded-xl ${isMobile ? 'p-1.5' : 'p-2'} border border-gray-200 dark:border-gray-600 shadow-sm hover:shadow-lg transition-all duration-300 group`}>
                      <div className="text-center">
                        <div className={`flex items-center justify-center ${isMobile ? 'gap-1 mb-0.5' : 'gap-2 mb-1'}`}>
                          <div className="relative group">
                            <svg className={`text-green-600 group-hover:text-green-700 transition-colors duration-300 group-hover:scale-110 group-hover:rotate-6`} width={isMobile ? 14 : 20} height={isMobile ? 14 : 20} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
                            </svg>
                            <div className="absolute inset-0 bg-green-100 dark:bg-green-900 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                          </div>
                          <div className={`${isMobile ? 'text-lg' : 'text-2xl'} font-bold text-green-600 dark:text-green-400 group-hover:text-green-700 transition-colors duration-300`}>
                            {localData?.filter(c => c.type_of_connection?.toLowerCase() === 'bluetooth')?.length || 0}
                          </div>
                        </div>
                        <div className={`${isMobile ? 'text-xs' : 'text-xs'} text-gray-600 dark:text-gray-400 group-hover:text-green-600 transition-colors duration-300`}>Bluetooth</div>
                      </div>
                    </div>

                    {/* NFC Types */}
                    <div className={`bg-white/80 dark:bg-gray-700/80 backdrop-blur-sm rounded-xl ${isMobile ? 'p-1.5' : 'p-2'} border border-gray-200 dark:border-gray-600 shadow-sm hover:shadow-lg transition-all duration-300 group`}>
                      <div className="text-center">
                        <div className={`flex items-center justify-center ${isMobile ? 'gap-1 mb-0.5' : 'gap-2 mb-1'}`}>
                          <div className="relative group">
                            <svg className={`text-purple-600 group-hover:text-purple-700 transition-colors duration-300 group-hover:scale-110 group-hover:rotate-6`} width={isMobile ? 14 : 20} height={isMobile ? 14 : 20} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                            </svg>
                            <div className="absolute inset-0 bg-purple-100 dark:bg-purple-900 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                          </div>
                          <div className={`${isMobile ? 'text-lg' : 'text-2xl'} font-bold text-purple-600 dark:text-purple-400 group-hover:text-purple-700 transition-colors duration-300`}>
                            {localData?.filter(c => c.type_of_connection?.toLowerCase() === 'nfc')?.length || 0}
                          </div>
                        </div>
                        <div className={`${isMobile ? 'text-xs' : 'text-xs'} text-gray-600 dark:text-gray-400 group-hover:text-purple-600 transition-colors duration-300`}>NFC</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="mt-6 flex flex-wrap gap-3">
                <button 
                  onClick={() => setSearchTerm('')}
                  className="px-4 py-2 bg-white/80 dark:bg-gray-700/80 backdrop-blur-sm border border-gray-200 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-600 hover:border-gray-300 dark:hover:border-gray-500 transition-all duration-300 text-sm font-medium shadow-sm flex items-center gap-2 group"
                >
                  <div className="relative">
                    <svg className="text-gray-600 group-hover:text-gray-800 transition-colors duration-300 group-hover:scale-110 group-hover:rotate-12" width={16} height={16} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    <div className="absolute inset-0 bg-gray-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                  </div>
                  Clear Search
                </button>
                <div className="px-4 py-2 bg-blue-100/80 dark:bg-blue-900/80 backdrop-blur-sm border border-blue-200 dark:border-blue-700 rounded-lg text-blue-700 dark:text-blue-300 text-sm font-medium shadow-sm flex items-center gap-2 group">
                  <div className="relative">
                    <svg className="text-blue-600 group-hover:text-blue-700 transition-colors duration-300 group-hover:scale-110 group-hover:-rotate-12" width={16} height={16} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <div className="absolute inset-0 bg-blue-200 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                  </div>
                  Showing {filteredCards?.length || 0} of {localData?.length || 0} card types
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading card types...</p>
          </div>
        </div>
      )}

      {/* Carousel Section */}
      {!isLoading && filteredCards.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <CardCarousel
            cards={filteredCards}
            onCardSelect={handleCardSelect}
            selectedCard={selectedCard}
            itemsPerView={{
              desktop: 3,
              tablet: 2,
              mobile: 1
            }}
          />
        </motion.div>
      )}

      {/* Empty State */}
      {!isLoading && filteredCards.length === 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="flex flex-col items-center justify-center h-64 text-gray-500 dark:text-gray-400"
        >
          <div className="text-center">
            <div className="text-6xl mb-4">🔍</div>
            {searchTerm ? (
              <>
                <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  No card types found
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  No card types match your search for &quot;{searchTerm}&quot;
                </p>
                <motion.button
                  onClick={() => setSearchTerm('')}
                  className="main-btn px-4 py-2"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Clear Search
                </motion.button>
              </>
            ) : (
              <>
                <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  No card types available
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Get started by creating your first card type
                </p>
                <motion.button
                  onClick={openCreateTypeModal}
                  className="main-btn px-6 py-3 flex items-center gap-2"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <span className="text-lg">+</span>
                  Create First Card Type
                </motion.button>
              </>
            )}
          </div>
        </motion.div>
      )}

      {/* Action Panel for Selected Card */}
      <AnimatePresence>
        {selectedCard && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="mt-8 p-6 bg-gradient-to-r from-teal-50 to-cyan-50 dark:from-gray-800 dark:to-gray-700 rounded-xl border border-teal-200 dark:border-gray-600 shadow-lg"
          >
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
              <div className="mb-4 lg:mb-0">
                <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                  {selectedCard.name}
                </h3>
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-600 dark:text-gray-400">Type:</span>
                    <p className="text-gray-900 dark:text-gray-100">{selectedCard.type_of_connection}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600 dark:text-gray-400">Colors:</span>
                    <p className="text-gray-900 dark:text-gray-100">{selectedCard.number_of_colors}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600 dark:text-gray-400">Width:</span>
                    <p className="text-gray-900 dark:text-gray-100">{selectedCard.setting?.width}px</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600 dark:text-gray-400">Height:</span>
                    <p className="text-gray-900 dark:text-gray-100">{selectedCard.setting?.height}px</p>
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-3">
                <motion.button
                  onClick={() => handleCardEdit(selectedCard)}
                  className="main-btn px-6 py-3 flex items-center justify-center gap-2 font-medium shadow-md"
                  //whileHover={{ scale: 1.02, y: -1 }}
                  //whileTap={{ scale: 0.98 }}
                >
                  <FiChevronRight size={18} />
                  Edit Type
                </motion.button>

                <motion.button
                  onClick={() => handleCardDelete(selectedCard)}
                  className="px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors duration-200 flex items-center justify-center gap-2 font-medium shadow-md"
                  //whileHover={{ scale: 1.02, y: -1 }}
                  //whileTap={{ scale: 0.98 }}
                >
                  <FiTrash size={18} />
                  Delete Type
                </motion.button>

                <motion.button
                  onClick={() => setSelectedCard(null)}
                  className="gray-btn px-6 py-3 flex items-center justify-center gap-2 font-medium shadow-md"
                  whileHover={{ scale: 1.02, y: -1 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Clear Selection
                </motion.button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Create/Edit Modal */}
      <CreateTypeForm
        isModalOpen={isCreateTypeModalOpen}
        setIsModalOpen={setIsCreateTypeModalOpen}
        fetchCardTypes={refetch}
        editData={editData}
        isEditMode={isEditMode}
        resetEditMode={resetEditMode}
      />

      {/* Mobile Floating Action Button (FAB) */}
      {isMobile && (
        <div className="fixed bottom-6 right-6 z-50">
          <motion.button
            onClick={openCreateTypeModal}
            className="w-14 h-14 bg-gradient-to-r from-emerald-500 to-teal-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center border border-emerald-400/30"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
          >
            <span className="text-xl font-bold">+</span>
          </motion.button>
        </div>
      )}
    </Container>
  );
}

export default CardsIndex;