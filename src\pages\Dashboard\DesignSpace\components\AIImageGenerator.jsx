import { useState } from 'react';
import { useDesignSpace } from '@contexts/DesignSpaceContext';
import { useDarkMode } from '@contexts/DarkModeContext';
import { InputText } from 'primereact/inputtext';
import { Button } from 'primereact/button';
import { Slider } from 'primereact/slider';
import { Dropdown } from 'primereact/dropdown';
import { TabView, TabPanel } from 'primereact/tabview';
import { Message } from 'primereact/message';
import { ToggleButton } from 'primereact/togglebutton';
import { Chip } from 'primereact/chip';

// Icons
import { FaImage } from 'react-icons/fa';
import { RiAiGenerate } from 'react-icons/ri';

// Expanded sample images for different styles and categories
const sampleImages = {
    // Art styles
    artStyles: {
        'oil-painting': 'https://img.freepik.com/free-photo/abstract-colorful-oil-painting-texture_1258-30.jpg',
        'watercolor': 'https://img.freepik.com/free-photo/watercolor-paper_53876-31761.jpg',
        'digital-art': 'https://img.freepik.com/free-photo/colorful-abstract-textured-background-design_53876-108265.jpg',
        'sketch': 'https://img.freepik.com/free-photo/pencil-sketch-texture_1194-6838.jpg',
        'cartoon': 'https://img.freepik.com/free-vector/comic-style-background_52683-4690.jpg',
        'pixel-art': 'https://img.freepik.com/free-vector/pixel-art-background_52683-87329.jpg',
        '3d-render': 'https://img.freepik.com/free-photo/abstract-3d-background-with-geometric-shapes_23-2149024514.jpg'
    },

    // Categories - expanded with more specific images
    categories: {
        // Nature & Landscapes
        'landscape': 'https://img.freepik.com/free-photo/painting-mountain-lake-with-mountain-background_188544-9126.jpg',
        'mountain': 'https://img.freepik.com/free-photo/beautiful-shot-mountains-trees-covered-fog_181624-24505.jpg',
        'beach': 'https://img.freepik.com/free-photo/tropical-beach_74190-188.jpg',
        'desert': 'https://img.freepik.com/free-photo/beautiful-shot-desert_181624-19508.jpg',
        'forest': 'https://img.freepik.com/free-photo/beautiful-shot-forest-with-tall-green-trees_181624-20615.jpg',
        'jungle': 'https://img.freepik.com/free-photo/beautiful-tropical-forest_181624-24137.jpg',
        'waterfall': 'https://img.freepik.com/free-photo/beautiful-shot-waterfall-forest_181624-46297.jpg',
        'lake': 'https://img.freepik.com/free-photo/beautiful-shot-crystal-clear-lake-surrounded-by-mountains-during-sunset_181624-41502.jpg',
        'river': 'https://img.freepik.com/free-photo/beautiful-shot-river-surrounded-by-trees-forest_181624-4574.jpg',
        'ocean': 'https://img.freepik.com/free-photo/beautiful-paradise-island-with-beach-sea_74190-1023.jpg',
        'sunset': 'https://img.freepik.com/free-photo/beautiful-sunset-beach-tropical-sea_74190-6102.jpg',
        'sunrise': 'https://img.freepik.com/free-photo/beautiful-tropical-beach-sea-ocean-with-coconut-palm-tree-sunrise-time_74190-7454.jpg',
        'night': 'https://img.freepik.com/free-photo/silhouette-palm-tree-beach-sunset-twilight-sky-background_74190-13701.jpg',
        'sky': 'https://img.freepik.com/free-photo/dramatic-white-clouds-blue-sky_53876-167215.jpg',
        'stars': 'https://img.freepik.com/free-photo/milky-way-night-landscape-with-stars_1150-11234.jpg',
        'galaxy': 'https://img.freepik.com/free-photo/colorful-abstract-nebula-space-background_53876-111357.jpg',

        // People & Portraits
        'portrait': 'https://img.freepik.com/free-photo/young-beautiful-woman-pink-warm-sweater-natural-look-smiling-portrait-isolated-long-hair_285396-896.jpg',
        'woman': 'https://img.freepik.com/free-photo/portrait-young-beautiful-woman-with-smoky-eyes-makeup-pretty-young-adult-girl-posing-studio-closeup-attractive-female-face_186202-4439.jpg',
        'man': 'https://img.freepik.com/free-photo/handsome-confident-smiling-man-with-hands-crossed-chest_176420-18743.jpg',
        'child': 'https://img.freepik.com/free-photo/little-girl-yellow-jacket-red-hat-sits-autumn-leaves-park_1321-2842.jpg',
        'baby': 'https://img.freepik.com/free-photo/cute-baby-sleeping_1301-2632.jpg',
        'family': 'https://img.freepik.com/free-photo/happy-young-family-with-child-home_1303-26823.jpg',
        'couple': 'https://img.freepik.com/free-photo/couple-love_144627-17187.jpg',
        'wedding': 'https://img.freepik.com/free-photo/groom-putting-ring-bride-s-finger_1157-338.jpg',

        // Abstract & Art
        'abstract': 'https://img.freepik.com/free-photo/abstract-colorful-splash-3d-background-generative-ai-background_60438-2509.jpg',
        'pattern': 'https://img.freepik.com/free-photo/abstract-luxury-plain-blur-grey-black-gradient-used-as-background-studio-wall-display-your-products_1258-63767.jpg',
        'texture': 'https://img.freepik.com/free-photo/abstract-surface-textures-white-concrete-stone-wall_74190-8189.jpg',
        'geometric': 'https://img.freepik.com/free-photo/abstract-luxury-gradient-blue-background-smooth-dark-blue-with-black-vignette-studio-banner_1258-63452.jpg',
        'fractal': 'https://img.freepik.com/free-photo/abstract-fractal-blue-purple-light-effect-background_53876-108180.jpg',

        // Places & Architecture
        'architecture': 'https://img.freepik.com/free-photo/beautiful-manhattan-bridge-new-york-usa_181624-48458.jpg',
        'city': 'https://img.freepik.com/free-photo/aerial-shot-manhattan-cityscape-with-tall-skyscrapers_181624-5066.jpg',
        'building': 'https://img.freepik.com/free-photo/modern-business-buildings-finance-district-blue-sky_1127-3215.jpg',
        'house': 'https://img.freepik.com/free-photo/luxury-pool-villa-spectacular-contemporary-design-digital-art-real-estate-home-house-property-ge_1258-150765.jpg',
        'castle': 'https://img.freepik.com/free-photo/beautiful-shot-neuschwanstein-castle-germany-surrounded-by-mountains-fog_181624-24183.jpg',
        'temple': 'https://img.freepik.com/free-photo/beautiful-temple-thailand_181624-21586.jpg',
        'church': 'https://img.freepik.com/free-photo/beautiful-shot-christian-church-with-cloudy-sky-background_181624-27739.jpg',
        'mosque': 'https://img.freepik.com/free-photo/mosque-silhouette-sunset_1150-10064.jpg',
        'bridge': 'https://img.freepik.com/free-photo/beautiful-shot-golden-gate-bridge-san-francisco-california-usa_181624-6.jpg',
        'street': 'https://img.freepik.com/free-photo/empty-road-city_1127-3361.jpg',

        // Food & Drinks
        'food': 'https://img.freepik.com/free-photo/top-view-table-full-delicious-food-composition_23-**********.jpg',
        'fruit': 'https://img.freepik.com/free-photo/fruits-berries-assortment-dark-surface_114579-45450.jpg',
        'vegetable': 'https://img.freepik.com/free-photo/healthy-vegetables-wooden-table_1150-38014.jpg',
        'cake': 'https://img.freepik.com/free-photo/close-up-delicious-chocolate-cake_23-**********.jpg',
        'coffee': 'https://img.freepik.com/free-photo/cup-coffee-with-heart-drawn-foam_1286-70.jpg',
        'pizza': 'https://img.freepik.com/free-photo/pizza-pizza-filled-with-tomatoes-salami-olives_140725-1200.jpg',
        'burger': 'https://img.freepik.com/free-photo/front-view-burger-stand_141793-15542.jpg',

        // Animals & Wildlife
        'animals': 'https://img.freepik.com/free-photo/view-wild-lion-nature_23-**********.jpg',
        'cat': 'https://img.freepik.com/free-photo/cute-domestic-kitten-sits-window-staring-outside-generative-ai_188544-12519.jpg',
        'dog': 'https://img.freepik.com/free-photo/cute-domestic-pet-portrait-indoors_23-**********.jpg',
        'bird': 'https://img.freepik.com/free-photo/beautiful-shot-colorful-bird-sitting-tree-branch_181624-45152.jpg',
        'fish': 'https://img.freepik.com/free-photo/colorful-fish-swimming-aquarium_23-2150692734.jpg',
        'horse': 'https://img.freepik.com/free-photo/beautiful-horse-nature_23-2150460834.jpg',
        'lion': 'https://img.freepik.com/free-photo/lion-king-animal-kingdom_475641-215.jpg',
        'tiger': 'https://img.freepik.com/free-photo/tiger-looking-side_1150-18316.jpg',
        'elephant': 'https://img.freepik.com/free-photo/elephant-walking-savanna_181624-45664.jpg',
        'butterfly': 'https://img.freepik.com/free-photo/beautiful-butterfly-sits-daisy-flower_1150-9895.jpg',

        // Fantasy & Sci-Fi
        'fantasy': 'https://img.freepik.com/free-photo/fantasy-landscape-with-colorful-sky-generative-ai_191095-510.jpg',
        'dragon': 'https://img.freepik.com/free-photo/dragon-flying-night-generative-ai_260559-465.jpg',
        'unicorn': 'https://img.freepik.com/free-photo/unicorn-forest_23-2150165501.jpg',
        'fairy': 'https://img.freepik.com/free-photo/fairy-tale-forest-with-magical-atmosphere-generative-ai_260559-465.jpg',
        'magic': 'https://img.freepik.com/free-photo/glowing-blue-magical-portal-forest-generative-ai_260559-465.jpg',
        'space': 'https://img.freepik.com/free-photo/space-background-realistic-starry-night-cosmos-shining-stars-milky-way-stardust-color-galaxy_1258-154643.jpg',
        'robot': 'https://img.freepik.com/free-photo/robot-doing-peace-sign_1048-4270.jpg',
        'alien': 'https://img.freepik.com/free-photo/alien-planet-landscape-with-mountains-extraterrestrial-life-generative-ai_260559-465.jpg',
        'cyberpunk': 'https://img.freepik.com/free-photo/futuristic-city-night-with-neon-lights-generative-ai_260559-465.jpg',
        'steampunk': 'https://img.freepik.com/free-photo/steampunk-mechanical-heart-generative-ai_260559-465.jpg'
    },

    // Moods & Atmospheres
    moods: {
        'vibrant': 'https://img.freepik.com/free-photo/colorful-abstract-nebula-space-background_53876-111357.jpg',
        'dark': 'https://img.freepik.com/free-photo/dark-concrete-wall-background_53876-92635.jpg',
        'serene': 'https://img.freepik.com/free-photo/beautiful-view-greenery-bridge-forest-perfect-background_181624-17827.jpg',
        'dramatic': 'https://img.freepik.com/free-photo/dramatic-white-clouds-blue-sky_53876-167215.jpg',
        'minimalist': 'https://img.freepik.com/free-photo/white-concrete-wall_53876-92803.jpg',
        'vintage': 'https://img.freepik.com/free-photo/old-paper-texture-background_1232-1932.jpg',
        'futuristic': 'https://img.freepik.com/free-photo/abstract-luxury-gradient-blue-background-smooth-dark-blue-with-black-vignette-studio-banner_1258-63452.jpg',
        'retro': 'https://img.freepik.com/free-photo/vintage-grunge-paper-background_1048-10911.jpg',
        'dreamy': 'https://img.freepik.com/free-photo/dreamy-pastel-sky-cloud-background-soft-color-filter_53876-129504.jpg',
        'mysterious': 'https://img.freepik.com/free-photo/mystical-foggy-forest-path-generative-ai_260559-465.jpg',
        'romantic': 'https://img.freepik.com/free-photo/valentines-day-background-with-hearts-roses_1308-41693.jpg',
        'peaceful': 'https://img.freepik.com/free-photo/beautiful-shot-calm-sea-with-mountain-distance-sunset_181624-27189.jpg',
        'energetic': 'https://img.freepik.com/free-photo/abstract-colorful-splash-3d-background-generative-ai-background_60438-2509.jpg',
        'melancholic': 'https://img.freepik.com/free-photo/rainy-window-with-city-bokeh-lights_23-2148814291.jpg',
        'nostalgic': 'https://img.freepik.com/free-photo/old-vintage-camera-table_144627-12230.jpg',
        'ethereal': 'https://img.freepik.com/free-photo/dreamy-fantasy-forest-with-glowing-fireflies-generative-ai_260559-465.jpg',
        'cozy': 'https://img.freepik.com/free-photo/cup-coffee-with-autumn-leaves-wooden-table_1150-17587.jpg',
        'festive': 'https://img.freepik.com/free-photo/christmas-background-with-gifts-decorations_1220-4063.jpg'
    }
};

// Advanced AI image processing function with improved keyword matching
const generateAIImage = (options) => {
    return new Promise((resolve) => {
        console.log("generateAIImage called with options:", options);
        
        // Simulate processing time
        setTimeout(() => {
            let result = { success: true };

            // Generate a more dynamic image based on the prompt
            if (options.prompt) {
                const prompt = options.prompt.toLowerCase();
                let matchFound = false;

                console.log("Processing prompt:", prompt);

                // Split the prompt into words for better matching
                const words = prompt.split(/\s+/);

                // First try to find exact matches for specific objects/subjects
                // This gives priority to the main subject of the image
                for (const word of words) {
                    // Check if this word is a direct match for any category
                    if (sampleImages.categories[word]) {
                        result.imageUrl = sampleImages.categories[word];
                        matchFound = true;
                        console.log(`Direct match found for: ${word}`);
                        break;
                    }
                }

                // If no direct match, try partial matches in categories
                if (!matchFound) {
                    for (const [category, url] of Object.entries(sampleImages.categories)) {
                        // Check if any category name appears in the prompt
                        if (prompt.includes(category)) {
                            result.imageUrl = url;
                            matchFound = true;
                            console.log(`Partial match found for category: ${category}`);
                            break;
                        }
                    }
                }

                // If still no match, check for mood/atmosphere words
                if (!matchFound) {
                    for (const [mood, url] of Object.entries(sampleImages.moods)) {
                        if (prompt.includes(mood)) {
                            result.imageUrl = url;
                            matchFound = true;
                            console.log(`Mood match found for: ${mood}`);
                            break;
                        }
                    }
                }

                // If no specific match found, use a deterministic but seemingly random selection
                if (!matchFound) {
                    // Create a hash of the prompt
                    const promptHash = prompt.split('').reduce((acc, char) => {
                        return acc + char.charCodeAt(0);
                    }, 0);

                    // Get all available images
                    const allImages = [
                        ...Object.values(sampleImages.categories),
                        ...Object.values(sampleImages.moods)
                    ];

                    // Select image based on hash
                    const selectedIndex = promptHash % allImages.length;
                    result.imageUrl = allImages[selectedIndex];
                    console.log(`No direct match, using hash-based selection: ${result.imageUrl}`);
                }

                // Apply style modification if specified
                if (options.style) {
                    // In a real implementation, this would apply the style to the generated image
                    // For now, we'll just note that the style was applied
                    result.style = options.style;

                    // Always use the style if it was explicitly selected
                    if (options.styleIsExplicit) {
                        result.imageUrl = sampleImages.artStyles[options.style] || result.imageUrl;
                        console.log(`Using explicit style: ${options.style}`);
                    }
                }

                // Apply aspect ratio adjustments (in a real implementation)
                if (options.aspectRatio) {
                    // Just noting that we would adjust the aspect ratio here
                    console.log(`Would apply aspect ratio: ${options.aspectRatio}`);
                }

                // Apply quality adjustments (in a real implementation)
                if (options.quality) {
                    // Just noting that we would adjust the quality here
                    console.log(`Would apply quality: ${options.quality}%`);
                }
            }
            // Generate image based on style only
            else if (options.style) {
                result.imageUrl = sampleImages.artStyles[options.style] || sampleImages.artStyles['digital-art'];
                console.log(`Using style-only generation: ${options.style}`);
            }
            // Default image
            else {
                result.imageUrl = 'https://img.freepik.com/free-photo/painting-mountain-lake-with-mountain-background_188544-9126.jpg';
                console.log(`Using default image`);
            }

            console.log("Final result:", result);
            resolve(result);
        }, 1500);
    });
};

const AIImageGenerator = () => {
    const { addElement } = useDesignSpace();
    const { isDarkMode } = useDarkMode();

    // State for text-to-image generation
    const [prompt, setPrompt] = useState('');
    const [isGenerating, setIsGenerating] = useState(false);
    const [activeTab, setActiveTab] = useState(0);
    const [generatedImage, setGeneratedImage] = useState(null);
    const [resultMessage, setResultMessage] = useState(null);

    // Advanced options
    const [selectedStyle, setSelectedStyle] = useState(null);
    const [negativePrompt, setNegativePrompt] = useState('');
    const [aspectRatio, setAspectRatio] = useState('1:1');
    const [enhancePrompt, setEnhancePrompt] = useState(true);
    const [imageQuality, setImageQuality] = useState(80);

    // Style options
    const styleOptions = [
        { label: 'Oil Painting', value: 'oil-painting' },
        { label: 'Watercolor', value: 'watercolor' },
        { label: 'Digital Art', value: 'digital-art' },
        { label: 'Sketch', value: 'sketch' },
        { label: 'Cartoon', value: 'cartoon' },
        { label: 'Pixel Art', value: 'pixel-art' },
        { label: '3D Render', value: '3d-render' }
    ];

    // Aspect ratio options
    const aspectRatioOptions = [
        { label: 'Square (1:1)', value: '1:1' },
        { label: 'Portrait (2:3)', value: '2:3' },
        { label: 'Landscape (3:2)', value: '3:2' },
        { label: 'Widescreen (16:9)', value: '16:9' }
    ];

    // Suggested prompts
    const suggestedPrompts = [
        "Mountain landscape with lake",
        "Futuristic city at sunset",
        "Portrait in vintage style",
        "Abstract colorful painting",
        "Fantasy forest with magic"
    ];

    // Handle prompt suggestion click
    const handleSuggestionClick = (suggestion) => {
        console.log("Suggestion clicked:", suggestion);
        setPrompt(suggestion);
    };

    // Handle image generation
    const handleGenerateImage = async () => {
        if (!prompt && !selectedStyle) return;

        setIsGenerating(true);
        setResultMessage(null);

        try {
            console.log("Starting image generation with:", {
                prompt,
                style: selectedStyle,
                aspectRatio,
                quality: imageQuality
            });

            const result = await generateAIImage({
                prompt: prompt,
                style: selectedStyle,
                styleIsExplicit: !!selectedStyle, // Flag to indicate if style was explicitly selected
                negativePrompt: negativePrompt,
                aspectRatio: aspectRatio,
                enhancePrompt: enhancePrompt,
                quality: imageQuality
            });

            console.log("Image generation result:", result);

            if (result.success && result.imageUrl) {
                // Preload the image to ensure it's available
                const img = new Image();
                img.onload = () => {
                    console.log("Generated image loaded successfully:", result.imageUrl);
                    setGeneratedImage(result.imageUrl);
                    
                    setResultMessage({
                        severity: 'success',
                        summary: 'Success',
                        detail: 'Image generated successfully'
                    });

                    // Automatically switch to the result tab
                    setActiveTab(1);
                };
                img.onerror = () => {
                    console.error("Failed to load generated image:", result.imageUrl);
                    setResultMessage({
                        severity: 'error',
                        summary: 'Error',
                        detail: 'Failed to load generated image'
                    });
                };
                img.src = result.imageUrl;
            } else {
                console.error("Image generation failed:", result);
                setResultMessage({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to generate image'
                });
            }
        } catch (error) {
            console.error("Error during image generation:", error);
            setResultMessage({
                severity: 'error',
                summary: 'Error',
                detail: 'An error occurred during generation'
            });
        } finally {
            setIsGenerating(false);
        }
    };

    // Add generated image to canvas
    const handleAddToCanvas = () => {
        if (generatedImage) {
            console.log("Adding image to canvas:", generatedImage);
            
            try {
                const elementId = addElement("img", generatedImage, {
                    width: 200,  // Set default width
                    height: 200, // Set default height
                    x: 100,      // Set default x position
                    y: 100       // Set default y position
                });
                
                console.log("Image element added with ID:", elementId);

                setResultMessage({
                    severity: 'info',
                    summary: 'Added',
                    detail: 'Image added to canvas'
                });
                
                // Reset the form after adding to canvas
                setTimeout(() => {
                    setActiveTab(0);
                    setGeneratedImage(null);
                    setResultMessage(null);
                }, 1500);
                
            } catch (error) {
                console.error("Error adding image to canvas:", error);
                setResultMessage({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to add image to canvas'
                });
            }
        }
    };

    // Reset form
    const handleReset = () => {
        console.log("Resetting form");
        setPrompt('');
        setSelectedStyle(null);
        setNegativePrompt('');
        setAspectRatio('1:1');
        setEnhancePrompt(true);
        setImageQuality(80);
        setGeneratedImage(null);
        setResultMessage(null);
        setActiveTab(0);
    };

    return (
        <div className={`ai-image-generator h-full ${isDarkMode ? 'dark-mode' : ''}`} style={{ display: 'flex', flexDirection: 'column', overflow: 'hidden', backgroundColor: isDarkMode ? '#1f2937' : 'white' }}>
            <div className={`p-4 border-b z-10 ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-white'}`} style={{ flexShrink: 0 }}>
                <h3 className={`text-lg font-medium flex items-center ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    <RiAiGenerate className={`mr-2 ${isDarkMode ? 'text-purple-400' : 'text-purple-600'}`} />
                    AI Image Generator
                </h3>

                {resultMessage && (
                    <div className="mt-3">
                        <Message
                            severity={resultMessage.severity}
                            text={resultMessage.detail}
                            style={{ width: '100%' }}
                        />
                    </div>
                )}
            </div>

            <div style={{
                flexGrow: 1,
                overflowY: 'auto',
                padding: '16px',
                height: '100%',
                position: 'relative'
            }}>
                <div style={{ position: 'relative', width: '100%', padding: '0 8px' }}>
                    <TabView activeIndex={activeTab} onTabChange={(e) => setActiveTab(e.index)}>
                        <TabPanel header="Generate" leftIcon="pi pi-pencil mr-2">
                            <div className="mb-3">
                                <label htmlFor="prompt" className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>Describe the image you want</label>
                                <InputText
                                    id="prompt"
                                    value={prompt}
                                    onChange={(e) => setPrompt(e.target.value)}
                                    placeholder="e.g., A serene mountain landscape with a lake"
                                    className={`w-full ${isDarkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : ''}`}
                                    disabled={isGenerating}
                                />

                                <div className="mt-2">
                                    <label className={`block text-xs mb-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Suggested prompts:</label>
                                    <div className="flex flex-wrap gap-1">
                                        {suggestedPrompts.map((suggestion, index) => (
                                            <Chip
                                                key={index}
                                                label={suggestion.length > 15 ? suggestion.substring(0, 15) + '...' : suggestion}
                                                className={`text-xs cursor-pointer ${isDarkMode ? 'bg-gray-600 text-gray-200 hover:bg-gray-500' : ''}`}
                                                onClick={() => handleSuggestionClick(suggestion)}
                                            />
                                        ))}
                                    </div>
                                </div>
                            </div>

                            <div className="grid grid-cols-2 gap-2 mb-3">
                                <div>
                                    <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>Art Style</label>
                                    <Dropdown
                                        value={selectedStyle}
                                        options={styleOptions}
                                        onChange={(e) => setSelectedStyle(e.value)}
                                        placeholder="Select style"
                                        className={`w-full ${isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : ''}`}
                                        disabled={isGenerating}
                                    />
                                </div>

                                <div>
                                    <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>Aspect Ratio</label>
                                    <Dropdown
                                        value={aspectRatio}
                                        options={aspectRatioOptions}
                                        onChange={(e) => setAspectRatio(e.value)}
                                        className={`w-full ${isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : ''}`}
                                        disabled={isGenerating}
                                    />
                                </div>
                            </div>

                            <div className="mb-3">
                                <div className="flex items-center justify-between">
                                    <label className={`text-sm font-medium ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>Image Quality: {imageQuality}%</label>
                                    <ToggleButton
                                        checked={enhancePrompt}
                                        onChange={(e) => setEnhancePrompt(e.value)}
                                        onLabel="AI Enhance: ON"
                                        offLabel="AI Enhance: OFF"
                                        onIcon="pi pi-check"
                                        offIcon="pi pi-times"
                                        size="small"
                                        disabled={isGenerating}
                                        style={{ fontSize: '0.7rem' }}
                                        className={isDarkMode ? 'bg-gray-700 border-gray-600' : ''}
                                    />
                                </div>
                                <Slider
                                    value={imageQuality}
                                    onChange={(e) => setImageQuality(e.value)}
                                    min={50}
                                    max={100}
                                    disabled={isGenerating}
                                    className={isDarkMode ? 'text-white' : ''}
                                />
                            </div>

                            <div className="flex justify-between">
                                <Button
                                    label="Reset"
                                    icon="pi pi-refresh"
                                    className="p-button-outlined p-button-sm"
                                    onClick={handleReset}
                                    disabled={isGenerating}
                                />
                                <Button
                                    label={isGenerating ? "Generating..." : "Generate Image"}
                                    icon="pi pi-image"
                                    loading={isGenerating}
                                    onClick={handleGenerateImage}
                                    disabled={(!prompt && !selectedStyle) || isGenerating}
                                    className="p-button-sm"
                                />
                            </div>
                        </TabPanel>

                        <TabPanel header="Result" leftIcon="pi pi-image mr-2" disabled={!generatedImage}>
                            {generatedImage ? (
                                <div className="text-center">
                                    <div className={`mb-3 border rounded overflow-hidden ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
                                        <img
                                            src={generatedImage}
                                            alt="Generated"
                                            className="max-w-full h-auto mx-auto"
                                            style={{ maxHeight: '200px', objectFit: 'contain' }}
                                        />
                                    </div>

                                    <div className="flex justify-center">
                                        <Button
                                            label="Add to Canvas"
                                            icon="pi pi-plus"
                                            onClick={handleAddToCanvas}
                                            className="mr-2 p-button-sm"
                                        />
                                        <Button
                                            label="Generate New"
                                            icon="pi pi-refresh"
                                            className="p-button-outlined p-button-sm"
                                            onClick={() => setActiveTab(0)}
                                        />
                                    </div>
                                </div>
                            ) : (
                                <div className="text-center py-4">
                                    <FaImage size={32} className={`mx-auto mb-2 ${isDarkMode ? 'text-gray-500' : 'text-gray-300'}`} />
                                    <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>No image generated yet</p>
                                </div>
                            )}
                        </TabPanel>
                    </TabView>
                </div>
            </div>
        </div>
    );
};

export default AIImageGenerator;
