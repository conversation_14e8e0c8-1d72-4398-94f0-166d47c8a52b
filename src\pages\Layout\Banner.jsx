import { useRef, useState, useEffect } from 'react'
import { Image } from 'primereact/image';
import { Menu } from 'primereact/menu';
import { MdKeyboardArrowDown } from "react-icons/md";
import { RiBillLine } from "react-icons/ri";
import { LuLogOut } from "react-icons/lu";
import { FiSettings } from 'react-icons/fi';
import { useLogoutMutation } from '@quires/auth';
import { useGlobalContext } from '@contexts/GlobalContext';
import { useNavigate } from 'react-router-dom';

// Default user image
const DEFAULT_USER_IMAGE = 'https://storage.inknull.com/uploads/user-image-14-591-1751789627.png';

function Banner() {
    const { userType } = useGlobalContext();
    const navigate = useNavigate();
    const [userImage, setUserImage] = useState('');

    const logout = useLogoutMutation()

    // جلب صورة المستخدم مباشرة من localStorage
    useEffect(() => {
        const userImageFromStorage = localStorage.getItem('user_image');
        if (userImageFromStorage) {
            setUserImage(userImageFromStorage);
        }
    }, []);

    // الاستماع لتغييرات صورة المستخدم في localStorage
    useEffect(() => {
        const handleStorageChange = () => {
            const userImageFromStorage = localStorage.getItem('user_image');
            if (userImageFromStorage) {
                setUserImage(userImageFromStorage);
            }
        };

        window.addEventListener('storage', handleStorageChange);
        
        // الاستماع للتغييرات في نفس التبويب
        const originalSetItem = localStorage.setItem;
        localStorage.setItem = function(key, value) {
            if (key === 'user_image') {
                setUserImage(value);
            }
            originalSetItem.apply(this, arguments);
        };

        return () => {
            window.removeEventListener('storage', handleStorageChange);
            localStorage.setItem = originalSetItem;
        };
    }, []);

    const menu = useRef(null);
    const items = [
        {
            label: 'Options',
            items: [
                {
                    label: 'Settings',
                    icon: <FiSettings className='me-2' />,
                    command: () => {navigate('/manager/settings');

                    }
                },
                {
                    label: 'Billing',
                    icon: <RiBillLine className='me-2' />,
                    command: () => {
                        const prefix = userType === 'admin' ? '/admin' : '/manager';  //Keep it incase I need to give admin a setting page
                        navigate(`${prefix}/billing`);
                    }
                },
                {
                    label: 'Logout',
                    icon: <LuLogOut className='me-2' />,
                    command: async () => {
                        await logout.mutateAsync()
                    }
                }
            ]
        }
    ];

    return (
        <nav className='w-full flex justify-end items-center bg-gradient-to-r from-white via-gray-50/30 to-white dark:from-dark-surface dark:via-gray-800/20 dark:to-dark-surface border-b border-gray-200/60 dark:border-gray-700/60 backdrop-blur-sm transition-all duration-500 p-0 shadow-sm -mx-4'>                       {/*If you wish to return it to normal navbar functionallity add these (justify-end p-5) to the class*/}
            <Menu 
                model={items} 
                popup 
                ref={menu} 
                id="popup_menu"
                className="dark:bg-gray-800 dark:border-gray-700"
                pt={{
                    root: { className: 'dark:bg-gray-800 dark:border-gray-700' },
                    content: { className: 'dark:bg-gray-800' },
                    menuitem: { className: 'dark:hover:bg-gray-700' },
                    action: { className: 'dark:text-gray-200 dark:hover:text-white' }
                }}
            />

            {/* User Profile Section */}
            <div className='flex items-center space-x-4 bg-gradient-to-br from-white/80 via-gray-50/60 to-white/80 dark:from-gray-800/80 dark:via-gray-700/60 dark:to-gray-800/80 backdrop-blur-xl rounded-none px-6 py-4 shadow-2xl border border-white/20 dark:border-gray-600/30 hover:shadow-3xl transition-all duration-500 hover:scale-[1.02] w-full'>
                {/* User Profile */}
                <div className='flex items-center space-x-4'>
                    <div className="w-12 h-12 rounded-full overflow-hidden ring-4 ring-white/50 dark:ring-gray-600/50 shadow-xl relative group">
                        <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-purple-500/20 rounded-full"></div>
                        <Image 
                            src={userImage ? (userImage.startsWith('http') ? userImage : userImage) : DEFAULT_USER_IMAGE} 
                            alt="profile image" 
                            imageClassName="w-full h-full object-cover relative z-10 group-hover:scale-110 transition-transform duration-300" 
                            width="48" 
                            height="48"
                            onError={(e) => {
                                e.target.src = DEFAULT_USER_IMAGE;
                            }}
                        />
                    </div>
                    <button
                        onClick={(event) => menu.current.toggle(event)}
                        aria-controls="popup_menu"
                        aria-haspopup
                        className='flex flex-col items-start hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-purple-50/50 dark:hover:from-gray-700/50 dark:hover:to-gray-600/50 rounded-xl px-4 py-2 transition-all duration-300 hover:shadow-lg group'>
                        <div className='text-sm font-bold flex items-center text-gray-800 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300'>
                            <span className='capitalize tracking-wide'>{localStorage.getItem("user_name") || "User name"}</span>
                            <MdKeyboardArrowDown size={16} className="text-gray-500 dark:text-gray-400 ml-1 group-hover:text-blue-500 dark:group-hover:text-blue-400 transition-colors duration-300" />
                        </div>
                        <small className='capitalize text-xs text-gray-500 dark:text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors duration-300 font-medium'>{userType}</small>
                    </button>
                </div>
            </div>
        </nav>
    )
}

export default Banner