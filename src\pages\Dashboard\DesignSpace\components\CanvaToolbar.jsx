import { useDesignSpace } from '@contexts/DesignSpaceContext';
import { useDarkMode } from '@contexts/DarkModeContext';
import PropTypes from 'prop-types';
import { useEffect, useState, useCallback } from 'react';
import { Dialog } from 'primereact/dialog';

// Icons
import { FiRotateCcw, FiRotateCw, FiLock, FiUnlock, FiSave, FiMinus, FiPlus, FiMaximize, FiCopy, <PERSON>Check, FiX } from 'react-icons/fi';
import {
    BiSolidLayerPlus,
    BiSolidLayerMinus,
    BiGroup,
    BiLayer
} from 'react-icons/bi';

const CanvaToolbar = ({ saveDesign<PERSON>and<PERSON>, saveAsHandler, isDirty = true, isCreateMode = false, isSaving = false }) => {
    const {
        selectedIds,
        undo,
        redo,
        toggleLock,
        groupElements,
        ungroupElements,
        zoomLevel,
        zoom,
        bringToFront,
        sendToBack,
        setSelectedIds,
        cardType, // <-- add cardType
        elements,  // <-- add elements
        userSelectedCardType // <-- add userSelectedCardType
    } = useDesignSpace();
    const { groupId } = useDesignSpace();
    const { isDarkMode } = useDarkMode();
    const [showGroupModal, setShowGroupModal] = useState(false);
    
    // Debug: تحقق من قيمة isDarkMode
    console.log('CanvaToolbar - isDarkMode:', isDarkMode);
    
    // إضافة CSS مخصص احترافي للوضع المظلم والفاتح
    useEffect(() => {
        const style = document.createElement('style');
        style.textContent = `
            /* تصميم احترافي لشريط الأدوات */
            .canva-toolbar {
                backdrop-filter: blur(20px) !important;
                -webkit-backdrop-filter: blur(20px) !important;
                border-radius: 16px !important;
                margin: 8px !important;
                padding: 24px 24px !important;
                min-height: 65px !important;
                box-shadow: 
                    0 8px 32px rgba(0, 0, 0, 0.12),
                    0 2px 8px rgba(0, 0, 0, 0.08),
                    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
                position: relative !important;
                overflow: hidden !important;
            }
            
            .canva-toolbar::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(135deg, 
                    rgba(255, 255, 255, 0.1) 0%, 
                    rgba(255, 255, 255, 0.05) 50%, 
                    rgba(255, 255, 255, 0.02) 100%);
                pointer-events: none;
                z-index: 1;
            }
            
            .canva-toolbar > * {
                position: relative;
                z-index: 2;
            }
            
            /* الوضع المظلم */
            .canva-toolbar.dark-mode {
                background: linear-gradient(135deg, 
                    rgba(31, 41, 55, 0.95) 0%, 
                    rgba(17, 24, 39, 0.98) 100%) !important;
                border: 1px solid rgba(75, 85, 99, 0.3) !important;
                box-shadow: 
                    0 8px 32px rgba(0, 0, 0, 0.3),
                    0 2px 8px rgba(0, 0, 0, 0.2),
                    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
            }
            
            .canva-toolbar.dark-mode::before {
                background: linear-gradient(135deg, 
                    rgba(255, 255, 255, 0.08) 0%, 
                    rgba(255, 255, 255, 0.03) 50%, 
                    rgba(255, 255, 255, 0.01) 100%);
            }
            
            /* الوضع الفاتح */
            .canva-toolbar:not(.dark-mode) {
                background: linear-gradient(135deg, 
                    rgba(255, 255, 255, 0.95) 0%, 
                    rgba(248, 250, 252, 0.98) 100%) !important;
                border: 1px solid rgba(226, 232, 240, 0.5) !important;
            }
            
            /* الفواصل المحسنة */
            .canva-toolbar .toolbar-divider {
                width: 1px !important;
                height: 32px !important;
                background: linear-gradient(to bottom, 
                    transparent 0%, 
                    rgba(156, 163, 175, 0.3) 20%, 
                    rgba(156, 163, 175, 0.6) 50%, 
                    rgba(156, 163, 175, 0.3) 80%, 
                    transparent 100%) !important;
                margin: 0 8px !important;
                border-radius: 1px !important;
            }
            
            .canva-toolbar.dark-mode .toolbar-divider {
                background: linear-gradient(to bottom, 
                    transparent 0%, 
                    rgba(75, 85, 99, 0.4) 20%, 
                    rgba(75, 85, 99, 0.7) 50%, 
                    rgba(75, 85, 99, 0.4) 80%, 
                    transparent 100%) !important;
            }
            
            /* الأزرار المحسنة */
            .canva-toolbar .toolbar-btn {
                position: relative !important;
                padding: 10px 12px !important;
                border-radius: 12px !important;
                border: 1px solid transparent !important;
                background: linear-gradient(135deg, 
                    rgba(255, 255, 255, 0.1) 0%, 
                    rgba(255, 255, 255, 0.05) 100%) !important;
                color: #374151 !important;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
                backdrop-filter: blur(10px) !important;
                -webkit-backdrop-filter: blur(10px) !important;
                box-shadow: 
                    0 2px 8px rgba(0, 0, 0, 0.05),
                    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
                overflow: hidden !important;
            }
            
            .canva-toolbar .toolbar-btn::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, 
                    transparent, 
                    rgba(255, 255, 255, 0.2), 
                    transparent);
                transition: left 0.5s ease;
            }
            
            .canva-toolbar .toolbar-btn:hover::before {
                left: 100%;
            }
            
            .canva-toolbar .toolbar-btn:hover {
                transform: translateY(-2px) scale(1.05) !important;
                background: linear-gradient(135deg, 
                    rgba(59, 130, 246, 0.1) 0%, 
                    rgba(59, 130, 246, 0.05) 100%) !important;
                border-color: rgba(59, 130, 246, 0.3) !important;
                box-shadow: 
                    0 8px 25px rgba(59, 130, 246, 0.15),
                    0 4px 12px rgba(0, 0, 0, 0.1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
                color: #1d4ed8 !important;
            }
            
            .canva-toolbar .toolbar-btn:active {
                transform: translateY(0) scale(0.98) !important;
                transition: all 0.1s ease !important;
            }
            
            /* الوضع المظلم للأزرار */
            .canva-toolbar.dark-mode .toolbar-btn {
                background: linear-gradient(135deg, 
                    rgba(75, 85, 99, 0.2) 0%, 
                    rgba(55, 65, 81, 0.3) 100%) !important;
                color: #e5e7eb !important;
                border-color: rgba(75, 85, 99, 0.3) !important;
            }
            
            .canva-toolbar.dark-mode .toolbar-btn:hover {
                background: linear-gradient(135deg, 
                    rgba(255, 255, 255, 0.15) 0%, 
                    rgba(255, 255, 255, 0.1) 100%) !important;
                color: #1f2937 !important;
                border-color: rgba(255, 255, 255, 0.3) !important;
                box-shadow: 
                    0 8px 25px rgba(255, 255, 255, 0.1),
                    0 4px 12px rgba(0, 0, 0, 0.2),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
            }
            
            /* الأزرار المعطلة */
            .canva-toolbar .toolbar-btn:disabled {
                opacity: 0.4 !important;
                cursor: not-allowed !important;
                transform: none !important;
                background: linear-gradient(135deg, 
                    rgba(156, 163, 175, 0.1) 0%, 
                    rgba(156, 163, 175, 0.05) 100%) !important;
            }
            
            /* عرض مستوى التكبير المحسن */
            .canva-toolbar .zoom-level-display {
                font-weight: 600 !important;
                font-size: 13px !important;
                padding: 8px 12px !important;
                background: linear-gradient(135deg, 
                    rgba(59, 130, 246, 0.1) 0%, 
                    rgba(59, 130, 246, 0.05) 100%) !important;
                border-radius: 10px !important;
                border: 1px solid rgba(59, 130, 246, 0.2) !important;
                color: #1d4ed8 !important;
                min-width: 50px !important;
                text-align: center !important;
                backdrop-filter: blur(10px) !important;
                -webkit-backdrop-filter: blur(10px) !important;
                box-shadow: 
                    0 2px 8px rgba(59, 130, 246, 0.1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
            }
            
            .canva-toolbar.dark-mode .zoom-level-display {
                background: linear-gradient(135deg, 
                    rgba(59, 130, 246, 0.2) 0%, 
                    rgba(59, 130, 246, 0.1) 100%) !important;
                border-color: rgba(59, 130, 246, 0.3) !important;
                color: #60a5fa !important;
            }
            
            /* تأثيرات خاصة للأيقونات */
            .canva-toolbar .toolbar-btn svg {
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
                filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1)) !important;
            }
            
            .canva-toolbar .toolbar-btn:hover svg {
                transform: scale(1.1) !important;
                filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2)) !important;
            }
            
            /* تأثيرات خاصة لزر Reset Zoom */
            .canva-toolbar .toolbar-btn.reset-zoom {
                background: linear-gradient(135deg, 
                    rgba(34, 197, 94, 0.1) 0%, 
                    rgba(34, 197, 94, 0.05) 100%) !important;
                border-color: rgba(34, 197, 94, 0.2) !important;
                color: #16a34a !important;
            }
            
            .canva-toolbar .toolbar-btn.reset-zoom:hover {
                background: linear-gradient(135deg, 
                    rgba(34, 197, 94, 0.2) 0%, 
                    rgba(34, 197, 94, 0.1) 100%) !important;
                border-color: rgba(34, 197, 94, 0.4) !important;
                box-shadow: 
                    0 8px 25px rgba(34, 197, 94, 0.2),
                    0 4px 12px rgba(0, 0, 0, 0.1) !important;
            }
            
            .canva-toolbar.dark-mode .toolbar-btn.reset-zoom {
                background: linear-gradient(135deg, 
                    rgba(34, 197, 94, 0.2) 0%, 
                    rgba(34, 197, 94, 0.1) 100%) !important;
                color: #4ade80 !important;
            }
            
            /* تحسين الأزرار الخاصة */
            .canva-toolbar .toolbar-btn.group-btn:hover {
                background: linear-gradient(135deg, 
                    rgba(168, 85, 247, 0.1) 0%, 
                    rgba(168, 85, 247, 0.05) 100%) !important;
                border-color: rgba(168, 85, 247, 0.3) !important;
                color: #9333ea !important;
            }
            
            .canva-toolbar .toolbar-btn.lock-btn:hover {
                background: linear-gradient(135deg, 
                    rgba(245, 158, 11, 0.1) 0%, 
                    rgba(245, 158, 11, 0.05) 100%) !important;
                border-color: rgba(245, 158, 11, 0.3) !important;
                color: #d97706 !important;
            }
            
            .canva-toolbar .toolbar-btn.layer-btn:hover {
                background: linear-gradient(135deg, 
                    rgba(236, 72, 153, 0.1) 0%, 
                    rgba(236, 72, 153, 0.05) 100%) !important;
                border-color: rgba(236, 72, 153, 0.3) !important;
                color: #db2777 !important;
            }
            
            /* تحسين الأزرار في الوضع المظلم */
            .canva-toolbar.dark-mode .toolbar-btn.group-btn:hover {
                background: linear-gradient(135deg, 
                    rgba(168, 85, 247, 0.2) 0%, 
                    rgba(168, 85, 247, 0.1) 100%) !important;
                color: #c084fc !important;
            }
            
            .canva-toolbar.dark-mode .toolbar-btn.lock-btn:hover {
                background: linear-gradient(135deg, 
                    rgba(245, 158, 11, 0.2) 0%, 
                    rgba(245, 158, 11, 0.1) 100%) !important;
                color: #fbbf24 !important;
            }
            
            .canva-toolbar.dark-mode .toolbar-btn.layer-btn:hover {
                background: linear-gradient(135deg, 
                    rgba(236, 72, 153, 0.2) 0%, 
                    rgba(236, 72, 153, 0.1) 100%) !important;
                color: #f472b6 !important;
            }
            
            /* تحسين الأزرار الكبيرة (Save) */
            .canva-toolbar .main-btn {
                border-radius: 12px !important;
                font-weight: 600 !important;
                padding: 12px 20px !important;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
                box-shadow: 
                    0 4px 12px rgba(0, 0, 0, 0.1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
                backdrop-filter: blur(10px) !important;
                -webkit-backdrop-filter: blur(10px) !important;
                border: 1px solid rgba(255, 255, 255, 0.1) !important;
                position: relative !important;
                overflow: hidden !important;
            }
            
            .canva-toolbar .main-btn::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, 
                    transparent, 
                    rgba(255, 255, 255, 0.2), 
                    transparent);
                transition: left 0.5s ease;
            }
            
            .canva-toolbar .main-btn:hover::before {
                left: 100%;
            }
            
            .canva-toolbar .main-btn:hover {
                transform: translateY(-3px) scale(1.02) !important;
                box-shadow: 
                    0 12px 30px rgba(0, 0, 0, 0.2),
                    0 6px 15px rgba(0, 0, 0, 0.15),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
            }
            
            .canva-toolbar .main-btn:active {
                transform: translateY(-1px) scale(0.98) !important;
                transition: all 0.1s ease !important;
            }
            
            /* تأثيرات خاصة لأزرار الحفظ */
            .canva-toolbar .main-btn:not(:disabled):hover {
                filter: brightness(1.1) !important;
            }
            
            /* زر حفظ التصميم - الوضع الفاتح */
            .canva-toolbar:not(.dark-mode) .main-btn:not(:disabled) {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
                box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3) !important;
            }
            
            .canva-toolbar:not(.dark-mode) .main-btn:not(:disabled):hover {
                background: linear-gradient(135deg, #059669 0%, #047857 100%) !important;
                box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4) !important;
            }
            
            /* زر حفظ التصميم - الوضع المظلم */
            .canva-toolbar.dark-mode .main-btn:not(:disabled) {
                background: linear-gradient(135deg, #00c3ac 0%, #00a896 100%) !important;
                box-shadow: 0 4px 12px rgba(0, 195, 172, 0.3) !important;
            }
            
            .canva-toolbar.dark-mode .main-btn:not(:disabled):hover {
                background: linear-gradient(135deg, #00a896 0%, #008f7a 100%) !important;
                box-shadow: 0 8px 25px rgba(0, 195, 172, 0.4) !important;
            }
            
            /* تأثيرات للأزرار المعطلة */
            .canva-toolbar .main-btn:disabled {
                background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%) !important;
                box-shadow: 0 4px 12px rgba(156, 163, 175, 0.3) !important;
                cursor: not-allowed !important;
                transform: none !important;
                filter: grayscale(0.3) !important;
            }
            
            .canva-toolbar .main-btn:disabled:hover {
                transform: none !important;
                box-shadow: 
                    0 4px 12px rgba(0, 0, 0, 0.1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
            }
        `;
        document.head.appendChild(style);
        
        return () => {
            document.head.removeChild(style);
        };
    }, []);

    const handleLockToggle = () => {
        if (selectedIds.length > 0) {
            toggleLock(selectedIds);
        }
    };

    const handleGroup = () => {
        if (selectedIds.length > 1) {
            groupElements(selectedIds);
        }
    };

    const handleUngroup = () => {
        if (selectedIds.length === 1) {
            const groupId = selectedIds[0];
            if (groupId.startsWith('group_')) {
                ungroupElements(groupId);
            }
        }
    };

    // دوال التحكم في الزوم
    const handleZoomIn = useCallback(() => {
        const newZoomLevel = Math.min(zoomLevel + 10, 200);
        if (zoomLevel !== newZoomLevel) {
            zoom(newZoomLevel);
        }
    }, [zoomLevel, zoom]);
    
    const handleZoomOut = useCallback(() => {
        const newZoomLevel = Math.max(zoomLevel - 10, 50);
        if (zoomLevel !== newZoomLevel) {
            zoom(newZoomLevel);
        }
    }, [zoomLevel, zoom]);
    
    const handleZoomReset = useCallback(() => {
        if (zoomLevel !== 100) {
            zoom(100);
        }
    }, [zoomLevel, zoom]);

    // إضافة اختصارات لوحة المفاتيح للتكبير والتصغير
    useEffect(() => {
        const handleKeyDown = (e) => {
            // تجاهل إذا كان التركيز على input أو textarea
            const active = document.activeElement;
            if (active && (active.tagName === 'INPUT' || active.tagName === 'TEXTAREA' || active.isContentEditable)) {
                return;
            }

            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case '=':
                    case '+':
                        e.preventDefault();
                        handleZoomIn();
                        break;
                    case '-':
                        e.preventDefault();
                        handleZoomOut();
                        break;
                    case '0':
                        e.preventDefault();
                        handleZoomReset();
                        break;
                }
            }
        };

        window.addEventListener('keydown', handleKeyDown);
        return () => window.removeEventListener('keydown', handleKeyDown);
    }, [zoomLevel, handleZoomIn, handleZoomOut, handleZoomReset]);
    // دالة قفل العنصر
    const isElementLocked = () => {
        // إذا كان لديك منطق للقفل، ضعه هنا. مؤقتاً يرجع false دائماً
        return false;
    };

    // تعطيل زر Save As New إذا لم يتم اختيار مجموعة
    console.log('Current groupId:', groupId, 'Type:', typeof groupId);
    console.log('Current cardType:', cardType);
    console.log('userSelectedCardType:', userSelectedCardType);
    
    // فحص بسيط - هل اختار المستخدم نوع البطاقة فعلاً؟
    const isCardTypeSelected = userSelectedCardType;
    
    console.log('isCardTypeSelected:', isCardTypeSelected, 'cardType.id:', cardType?.id);
    const isSaveAsDisabled = !isCardTypeSelected || !elements || elements.length === 0 || (!groupId && groupId !== 0);
    console.log('isSaveAsDisabled:', isSaveAsDisabled);

    return (
        <div className={`canva-toolbar w-full flex items-center justify-between px-6 py-8 ${isDarkMode ? 'dark-mode' : ''}`}>
            <div className="flex items-center space-x-2">
                {/* Divider */}
                <div className="toolbar-divider"></div>

                {/* Undo/Redo */}
                <div className="flex items-center space-x-1">
                    <button
                        className="toolbar-btn"
                        onClick={undo}
                        title="Undo (Ctrl+Z)"
                    >
                        <FiRotateCcw />
                    </button>
                    <button
                        className="toolbar-btn"
                        onClick={redo}
                        title="Redo (Ctrl+Y)"
                    >
                        <FiRotateCw />
                    </button>
                </div>

                {/* Divider */}
                <div className="toolbar-divider"></div>

                {/* Zoom Controls */}
                <div className="flex items-center space-x-1 zoom-controls">
                    <button
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleZoomOut();
                        }}
                        className={`toolbar-btn ${zoomLevel <= 50 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        title="Zoom Out (Ctrl + -)"
                        disabled={zoomLevel <= 50}
                    >
                        <FiMinus />
                    </button>
                    <span className="zoom-level-display">
                        {zoomLevel}%
                    </span>
                    <button
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleZoomIn();
                        }}
                        className={`toolbar-btn ${zoomLevel >= 200 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        title="Zoom In (Ctrl + +)"
                        disabled={zoomLevel >= 200}
                    >
                        <FiPlus />
                    </button>
                    <button
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleZoomReset();
                        }}
                        className={`toolbar-btn reset-zoom ${zoomLevel === 100 ? 'active' : ''}`}
                        title="Reset Zoom (Ctrl + 0)"
                    >
                        <FiMaximize />
                    </button>
                </div>

                {/* Divider */}
                <div className="toolbar-divider"></div>

                {/* Element Controls */}
                <div className="flex items-center space-x-1">
                    <button
                        className={`toolbar-btn lock-btn ${selectedIds.length === 0 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={handleLockToggle}
                        disabled={selectedIds.length === 0}
                        title={selectedIds.length > 0 && isElementLocked() ? "Unlock Element" : "Lock Element"}
                    >
                        {selectedIds.length > 0 && isElementLocked() ? <FiUnlock /> : <FiLock />}
                    </button>
                    <button
                        className={`toolbar-btn group-btn ${selectedIds.length < 2 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={handleGroup}
                        disabled={selectedIds.length < 2}
                        title="Group Elements (Ctrl+G)"
                    >
                        <BiGroup />
                    </button>
                    <button
                        className={`toolbar-btn group-btn ${selectedIds.length !== 1 || !selectedIds[0].startsWith('group_') ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={handleUngroup}
                        disabled={selectedIds.length !== 1 || !selectedIds[0].startsWith('group_')}
                        title="Ungroup Elements (Ctrl+Shift+G)"
                    >
                        <BiLayer />
                    </button>
                </div>

                {/* Divider */}
                <div className="toolbar-divider"></div>

                {/* Layer Controls */}
                <div className="flex items-center space-x-1">
                    <button
                        className={`toolbar-btn layer-btn ${selectedIds.length !== 1 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={() => selectedIds.length === 1 && bringToFront(selectedIds[0])}
                        disabled={selectedIds.length !== 1}
                        title="Bring to Front (Ctrl+])"
                    >
                        <BiSolidLayerPlus />
                    </button>
                    <button
                        className={`toolbar-btn layer-btn ${selectedIds.length !== 1 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={() => selectedIds.length === 1 && sendToBack(selectedIds[0])}
                        disabled={selectedIds.length !== 1}
                        title="Send to Back (Ctrl+[)"
                    >
                        <BiSolidLayerMinus />
                    </button>
                </div>

                {/* Divider */}
                <div className="toolbar-divider"></div>

                {/* Image Editing Tools */}
               
            </div>

            {/* Save Design Button */}
            <div className="flex items-center space-x-3 py-4">
                {!isCreateMode && (
                    <button
                        className={`main-btn flex items-center space-x-2 ${(!isDirty || isSaving) ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={isDirty && !isSaving ? (() => {
                            setSelectedIds([]);
                            setTimeout(() => {
                                if (saveDesignHandler) saveDesignHandler();
                            }, 0);
                        }) : undefined}
                        style={{
                            color: '#ffffff',
                            border: 'none',
                            fontWeight: '600',
                            fontSize: '14px',
                            padding: '12px 20px',
                            borderRadius: '12px',
                            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                            backdropFilter: 'blur(10px)',
                            WebkitBackdropFilter: 'blur(10px)',
                        }}
                        disabled={!isDirty || isSaving}
                        title={!isDirty ? 'No changes to save' : (isSaving ? 'Saving...' : 'Save Design (Ctrl+S)')}
                    >
                        <FiSave className="w-4 h-4" />
                        <span>
                            {isSaving ? (
                                <>
                                    <span className="animate-spin inline-block mr-2 w-4 h-4 border-2 border-white border-t-transparent rounded-full"></span>
                                    Saving...
                                </>
                            ) : 'Save Design'}
                        </span>
                    </button>
                )}
                <button
                    className={`main-btn flex items-center space-x-2 ${isSaveAsDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                    onClick={() => {
                        if (isSaveAsDisabled) {
                            setShowGroupModal(true);
                            return;
                        }
                        setSelectedIds([]);
                        setTimeout(() => {
                            if (saveAsHandler) saveAsHandler();
                        }, 0);
                    }}
                    style={{
                        background: isSaveAsDisabled 
                            ? 'linear-gradient(135deg, #9ca3af 0%, #6b7280 100%)' 
                            : 'linear-gradient(135deg, #6c63ff 0%, #5a52d5 100%)',
                        color: '#ffffff',
                        border: 'none',
                        fontWeight: '600',
                        fontSize: '14px',
                        padding: '12px 20px',
                        borderRadius: '12px',
                        boxShadow: isSaveAsDisabled 
                            ? '0 4px 12px rgba(156, 163, 175, 0.3)' 
                            : '0 4px 12px rgba(108, 99, 255, 0.3)',
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                        backdropFilter: 'blur(10px)',
                        WebkitBackdropFilter: 'blur(10px)',
                    }}
                    title={isSaveAsDisabled ? 'You must select a group and have elements in the design area' : 'Save As New Design (Ctrl+Shift+S)'}
                >
                    <FiCopy className="w-4 h-4" />
                    <span>Save As New</span>
                </button>
               

                <Dialog
                    visible={showGroupModal}
                    onHide={() => setShowGroupModal(false)}
                    header={<span className={`font-bold ${isDarkMode ? 'text-blue-300' : 'text-blue-700'}`}>Requirements Not Met</span>}
                    style={{ 
                        width: '500px', 
                        borderRadius: '12px',
                        backgroundColor: isDarkMode ? '#1f2937' : '#ffffff',
                        border: isDarkMode ? '1px solid #374151' : '1px solid #e5e7eb'
                    }}
                    className={`p-fluid text-center ${isDarkMode ? 'dark-modal' : ''}`}
                    modal
                    closable
                    draggable={false}
                    resizable={false}
                    footer={<button className={`main-btn px-6 py-2 rounded text-white transition ${isDarkMode ? 'bg-blue-500 hover:bg-blue-600' : 'bg-blue-600 hover:bg-blue-700'}`} onClick={() => setShowGroupModal(false)}>OK</button>}
                >
                    <div className="flex flex-col items-center justify-center">
                        <i className={`pi pi-exclamation-triangle text-4xl mb-3 ${isDarkMode ? 'text-blue-400' : 'text-blue-500'}`} />
                        <p className={`mb-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                            To <b className={isDarkMode ? 'text-white' : 'text-gray-900'}>Save Design</b>, you must meet the following requirements:
                        </p>
                        
                        {/* Requirements List with Icons */}
                        <div className="w-full space-y-3 mb-4">
                            {/* Card Type Requirement */}
                            <div className={`flex items-center justify-between p-3 rounded-lg border transition-all duration-300 ${
                                userSelectedCardType
                                    ? (isDarkMode ? 'bg-green-900/30 border-green-700/50 shadow-sm' : 'bg-green-50 border-green-200 shadow-sm')
                                    : (isDarkMode ? 'bg-red-900/30 border-red-700/50 shadow-sm' : 'bg-red-50 border-red-200 shadow-sm')
                            }`}>
                                <div className="flex items-center space-x-3">
                                    <div className={`flex items-center justify-center w-8 h-8 rounded-full transition-all duration-300 ${
                                        userSelectedCardType
                                            ? 'bg-green-500 text-white shadow-lg scale-110' 
                                            : 'bg-red-500 text-white shadow-lg'
                                    }`}>
                                        {userSelectedCardType ? (
                                            <FiCheck className="w-5 h-5 animate-pulse" />
                                        ) : (
                                            <FiX className="w-5 h-5" />
                                        )}
                                    </div>
                                    <span className={`font-medium ${
                                        userSelectedCardType 
                                            ? (isDarkMode ? 'text-green-300' : 'text-green-700')
                                            : (isDarkMode ? 'text-red-300' : 'text-red-700')
                                    }`}>
                                        Select a card type from the dropdown
                                    </span>
                                </div>
                                {userSelectedCardType && (
                                    <div className="text-green-500 text-sm font-medium animate-bounce">
                                        ✓ Complete
                                    </div>
                                )}
                            </div>

                            {/* Elements Requirement */}
                            <div className={`flex items-center justify-between p-3 rounded-lg border transition-all duration-300 ${
                                elements && elements.length > 0 
                                    ? (isDarkMode ? 'bg-green-900/30 border-green-700/50 shadow-sm' : 'bg-green-50 border-green-200 shadow-sm')
                                    : (isDarkMode ? 'bg-red-900/30 border-red-700/50 shadow-sm' : 'bg-red-50 border-red-200 shadow-sm')
                            }`}>
                                <div className="flex items-center space-x-3">
                                    <div className={`flex items-center justify-center w-8 h-8 rounded-full transition-all duration-300 ${
                                        elements && elements.length > 0 
                                            ? 'bg-green-500 text-white shadow-lg scale-110' 
                                            : 'bg-red-500 text-white shadow-lg'
                                    }`}>
                                        {elements && elements.length > 0 ? (
                                            <FiCheck className="w-5 h-5 animate-pulse" />
                                        ) : (
                                            <FiX className="w-5 h-5" />
                                        )}
                                    </div>
                                    <span className={`font-medium ${
                                        elements && elements.length > 0 
                                            ? (isDarkMode ? 'text-green-300' : 'text-green-700')
                                            : (isDarkMode ? 'text-red-300' : 'text-red-700')
                                    }`}>
                                        Add elements to the design area
                                    </span>
                                </div>
                                {elements && elements.length > 0 && (
                                    <div className="text-green-500 text-sm font-medium animate-bounce">
                                        ✓ Complete
                                    </div>
                                )}
                            </div>

                            {/* Group Requirement */}
                            <div className={`flex items-center justify-between p-3 rounded-lg border transition-all duration-300 ${
                                groupId !== undefined && groupId !== null 
                                    ? (isDarkMode ? 'bg-green-900/30 border-green-700/50 shadow-sm' : 'bg-green-50 border-green-200 shadow-sm')
                                    : (isDarkMode ? 'bg-red-900/30 border-red-700/50 shadow-sm' : 'bg-red-50 border-red-200 shadow-sm')
                            }`}>
                                <div className="flex items-center space-x-3">
                                    <div className={`flex items-center justify-center w-8 h-8 rounded-full transition-all duration-300 ${
                                        groupId !== undefined && groupId !== null 
                                            ? 'bg-green-500 text-white shadow-lg scale-110' 
                                            : 'bg-red-500 text-white shadow-lg'
                                    }`}>
                                        {groupId !== undefined && groupId !== null ? (
                                            <FiCheck className="w-5 h-5 animate-pulse" />
                                        ) : (
                                            <FiX className="w-5 h-5" />
                                        )}
                                    </div>
                                    <span className={`font-medium ${
                                        groupId !== undefined && groupId !== null 
                                            ? (isDarkMode ? 'text-green-300' : 'text-green-700')
                                            : (isDarkMode ? 'text-red-300' : 'text-red-700')
                                    }`}>
                                        Choose a group from the group selection
                                    </span>
                                </div>
                                {groupId !== undefined && groupId !== null && (
                                    <div className="text-green-500 text-sm font-medium animate-bounce">
                                        ✓ Complete
                                    </div>
                                )}
                            </div>
                        </div>

                        <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                            Please complete these requirements before saving your design as a new template.
                        </p>
                    </div>
                </Dialog>
            </div>
        </div>
    );
};

CanvaToolbar.propTypes = {
    saveDesignHandler: PropTypes.func.isRequired,
    saveAsHandler: PropTypes.func.isRequired,
    isDirty: PropTypes.bool,
    isCreateMode: PropTypes.bool,
    isSaving: PropTypes.bool
};

export default CanvaToolbar;