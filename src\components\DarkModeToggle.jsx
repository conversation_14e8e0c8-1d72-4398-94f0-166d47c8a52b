import React, { useState, useEffect } from 'react';
import { useDarkMode } from '@contexts/DarkModeContext';

const DarkModeToggle = ({ className = '', showLabel = false, size = 'default' }) => {
  const { isDarkMode, toggleDarkMode } = useDarkMode();
  const [isAnimating, setIsAnimating] = useState(false);

  const handleToggle = () => {
    setIsAnimating(true);
    toggleDarkMode();
    
    // إزالة تأثير التحريك بعد انتهاءه
    setTimeout(() => {
      setIsAnimating(false);
    }, 300);
  };

  const sizeClasses = {
    small: 'w-12 h-6',
    default: 'w-16 h-8',
    large: 'w-20 h-10'
  };

  const iconSizes = {
    small: 'text-xs',
    default: 'text-sm',
    large: 'text-base'
  };

  const toggleSizes = {
    small: 'w-4 h-4',
    default: 'w-6 h-6',
    large: 'w-8 h-8'
  };

  const translateSizes = {
    small: 'translateX(24px)',
    default: 'translateX(32px)',
    large: 'translateX(40px)'
  };

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      {showLabel && (
        <span className={`text-xs font-bold tracking-wide transition-all duration-300 ${
          isDarkMode ? 'text-blue-400 drop-shadow-sm' : 'text-gray-600'
        }`}>
          {isDarkMode ? 'Dark Mode' : 'Light Mode'}
        </span>
      )}
      
      <div className="relative">
        <button
          onClick={handleToggle}
          className={`
            ${sizeClasses[size]}
            relative
            rounded-full
            transition-all
            duration-300
            ease-in-out
            focus:outline-none
            focus:ring-4
            focus:ring-opacity-50
            ${isDarkMode 
              ? 'bg-gradient-to-r from-blue-600 to-blue-500 focus:ring-blue-300' 
              : 'bg-gradient-to-r from-gray-300 to-gray-400 focus:ring-gray-300'
            }
            ${isAnimating ? 'animate-pulse' : ''}
            hover:scale-105
            active:scale-95
            shadow-lg
            hover:shadow-xl
            ${isDarkMode ? 'hover:shadow-blue-500/25' : 'hover:shadow-gray-500/25'}
          `}
          aria-label={isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
        >
          {/* خلفية متدرجة مع تأثيرات */}
          <div className={`
            absolute inset-0 rounded-full
            ${isDarkMode 
              ? 'bg-gradient-to-r from-blue-600 via-blue-500 to-indigo-600' 
              : 'bg-gradient-to-r from-yellow-400 via-orange-400 to-yellow-500'
            }
            opacity-80
            transition-opacity duration-300
          `} />
          
          {/* النجوم في الوضع المظلم */}
          {isDarkMode && (
            <div className="absolute inset-0 rounded-full overflow-hidden">
              <div className="absolute top-1 left-2 w-1 h-1 bg-white rounded-full opacity-60 animate-pulse" />
              <div className="absolute top-3 right-3 w-0.5 h-0.5 bg-white rounded-full opacity-40 animate-pulse" style={{ animationDelay: '0.5s' }} />
              <div className="absolute bottom-2 left-4 w-0.5 h-0.5 bg-white rounded-full opacity-50 animate-pulse" style={{ animationDelay: '1s' }} />
            </div>
          )}
          
          {/* أيقونة الشمس/القمر */}
          <div className={`
            absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2
            transition-all duration-300 ease-in-out
            ${isDarkMode ? 'opacity-0 scale-0' : 'opacity-100 scale-100'}
          `}>
            <svg 
              className={`${iconSizes[size]} text-yellow-500`} 
              fill="currentColor" 
              viewBox="0 0 20 20"
            >
              <path 
                fillRule="evenodd" 
                d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" 
                clipRule="evenodd" 
              />
            </svg>
          </div>
          
          {/* أيقونة القمر */}
          <div className={`
            absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2
            transition-all duration-300 ease-in-out
            ${isDarkMode ? 'opacity-100 scale-100' : 'opacity-0 scale-0'}
          `}>
            <svg 
              className={`${iconSizes[size]} text-blue-200`} 
              fill="currentColor" 
              viewBox="0 0 20 20"
            >
              <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
            </svg>
          </div>
          
          {/* الزر المتحرك */}
          <div className={`
            ${toggleSizes[size]}
            absolute top-1 left-1
            bg-white
            rounded-full
            shadow-lg
            transition-all
            duration-300
            ease-in-out
            transform
            ${isDarkMode ? translateSizes[size] : 'translateX(0)'}
            ${isAnimating ? 'animate-bounce' : ''}
            flex items-center justify-center
            ${isDarkMode ? 'bg-gray-800' : 'bg-white'}
          `}>
            {/* تأثير الإضاءة */}
            <div className={`
              absolute inset-0 rounded-full
              ${isDarkMode 
                ? 'bg-gradient-to-br from-blue-400 to-blue-600 opacity-20' 
                : 'bg-gradient-to-br from-yellow-300 to-orange-400 opacity-30'
              }
            `} />
          </div>
          
          {/* تأثير التوهج */}
          <div className={`
            absolute inset-0 rounded-full
            ${isDarkMode 
              ? 'bg-blue-500 opacity-0 group-hover:opacity-20' 
              : 'bg-yellow-400 opacity-0 group-hover:opacity-20'
            }
            transition-opacity duration-300
          `} />
        </button>
        
        {/* تأثيرات إضافية */}
        {isAnimating && (
          <div className={`
            absolute inset-0 rounded-full
            ${isDarkMode 
              ? 'bg-blue-500 opacity-30' 
              : 'bg-yellow-400 opacity-30'
            }
            animate-ping
          `} />
        )}
      </div>
    </div>
  );
};

export default DarkModeToggle;
