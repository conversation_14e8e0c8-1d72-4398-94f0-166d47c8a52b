import { useState, useEffect } from 'react';
import { useQuery } from 'react-query';
import Container from '@components/Container';
import axiosInstance from '../../../config/Axios';
import { motion } from 'framer-motion';
import { useLayout } from '@contexts/LayoutContext';
import SalesDetailModal from './SalesDetailModal';

import { Chart } from 'primereact/chart';
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';

// Icons
import { FiDollarSign, FiPackage, FiTrendingUp, FiUsers, FiBarChart2, FiDownload, FiArrowUp, FiArrowDown } from 'react-icons/fi';
import { FaGift, FaCreditCard } from 'react-icons/fa';
import { BsBank2 } from 'react-icons/bs';

function SalesManagementDashboard() {
    const { isMobile } = useLayout();

    // State for filters and date ranges
    const [timeFilter, setTimeFilter] = useState('last30Days');
    const [chartView, setChartView] = useState('revenue');
    const [salesData, setSalesData] = useState([]);
    const [mostSoldPackages, setMostSoldPackages] = useState([]);
    const [salesMetrics, setSalesMetrics] = useState({
        totalRevenue: 0,
        totalSales: 0,
        averageSale: 0,
        growthRate: 0
    });

    // Pagination state
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(10);
    const [selectedSale, setSelectedSale] = useState(null);
    const [isModalOpen, setIsModalOpen] = useState(false);

    // Sorting state
    const [sortField, setSortField] = useState('purchased_at');
    const [sortOrder, setSortOrder] = useState('desc'); // 'asc' or 'desc'

    // Fetch sold packages data
    const fetchSoldPackages = async () => {
        try {
            const token = localStorage.getItem('token');
            if (!token) return [];

            const response = await axiosInstance.get('packages/show-sold-packages', {
                headers: { Authorization: `Bearer ${token}` }
            });
            return response.data || [];
        } catch (error) {
            console.error('Error fetching sold packages:', error);
            return [];
        }
    };



    // React Query hooks
    const { data: soldPackages, isLoading: packagesLoading } = useQuery('soldPackages', fetchSoldPackages);

    // Process data when it's loaded
    useEffect(() => {
        if (soldPackages && soldPackages.length > 0) {
            // Process sales data for charts
            const processedData = processSalesData(soldPackages);
            setSalesData(processedData);

            // Calculate metrics
            const metrics = calculateSalesMetrics(soldPackages);
            setSalesMetrics(metrics);

            // Get most sold packages
            const packages = getMostSoldPackages(soldPackages);
            setMostSoldPackages(packages);
        }
    }, [soldPackages]);

    // Process sales data for charts based on time filter
    const processSalesData = (data) => {
        if (!data || data.length === 0) return [];

        // Sort by purchase date
        const sortedData = [...data].sort((a, b) =>
            new Date(a.purchased_at) - new Date(b.purchased_at)
        );

        // Group by date based on time filter
        const groupedData = {};

        sortedData.forEach(item => {
            const date = new Date(item.purchased_at);
            let key;

            if (timeFilter === 'last7Days') {
                key = date.toLocaleDateString();
            } else if (timeFilter === 'lastMonth') {
                key = `Week ${Math.ceil(date.getDate() / 7)}`;
            } else if (timeFilter === 'last6Months') {
                key = `${date.toLocaleString('default', { month: 'short' })} ${date.getFullYear()}`;
            } else {
                key = `${date.toLocaleString('default', { month: 'short' })} ${date.getFullYear()}`;
            }

            if (!groupedData[key]) {
                groupedData[key] = { revenue: 0, count: 0 };
            }

            groupedData[key].revenue += parseFloat(item.total_price || 0);
            groupedData[key].count += 1;
        });

        return Object.entries(groupedData).map(([label, data]) => ({
            label,
            revenue: data.revenue,
            count: data.count
        }));
    };

    // Calculate sales metrics
    const calculateSalesMetrics = (data) => {
        if (!data || data.length === 0) return { totalRevenue: 0, totalSales: 0, averageSale: 0, growthRate: 0 };

        const totalRevenue = data.reduce((sum, item) => sum + parseFloat(item.total_price || 0), 0);
        const totalSales = data.length;
        const averageSale = totalRevenue / totalSales;

        // Calculate growth rate (simplified - comparing last 30 days vs previous 30 days)
        const now = new Date();
        const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);

        const recentSales = data.filter(item => new Date(item.purchased_at) >= thirtyDaysAgo);
        const previousSales = data.filter(item => {
            const date = new Date(item.purchased_at);
            return date >= sixtyDaysAgo && date < thirtyDaysAgo;
        });

        const recentRevenue = recentSales.reduce((sum, item) => sum + parseFloat(item.total_price || 0), 0);
        const previousRevenue = previousSales.reduce((sum, item) => sum + parseFloat(item.total_price || 0), 0);

        const growthRate = previousRevenue > 0 ? ((recentRevenue - previousRevenue) / previousRevenue) * 100 : 0;

        return { totalRevenue, totalSales, averageSale, growthRate };
    };

    // Get most sold packages
    const getMostSoldPackages = (data) => {
        if (!data || data.length === 0) return [];

        const packageData = {};

        data.forEach(item => {
            const packageName = item.name || 'Unknown Package';
            if (!packageData[packageName]) {
                packageData[packageName] = {
                    name: packageName,
                    totalRevenue: 0,
                    salesCount: 0,
                    averagePrice: 0,
                    cardLimit: item.card_limit || 0
                };
            }
            packageData[packageName].totalRevenue += parseFloat(item.total_price || 0);
            packageData[packageName].salesCount += 1;
        });

        // Calculate average price for each package
        Object.values(packageData).forEach(pkg => {
            pkg.averagePrice = pkg.salesCount > 0 ? pkg.totalRevenue / pkg.salesCount : 0;
        });

        return Object.values(packageData)
            .sort((a, b) => b.salesCount - a.salesCount)
            .slice(0, 5);
    };

    // Time filter options
    const timeFilterOptions = [
        { label: 'Last 7 Days', value: 'last7Days' },
        { label: 'Last 30 Days', value: 'last30Days' },
        { label: 'Last 6 Months', value: 'last6Months' },
        { label: 'Last Year', value: 'lastYear' }
    ];

    // Prepare chart data
    const getChartData = () => {
        if (!salesData || salesData.length === 0) return null;

        const labels = salesData.map(item => item.label);
        const revenueData = salesData.map(item => item.revenue);
        const countData = salesData.map(item => item.count);

        return {
            labels,
            datasets: [
                {
                    label: chartView === 'revenue' ? 'Revenue ($)' : 'Sales Count',
                    data: chartView === 'revenue' ? revenueData : countData,
                    borderColor: '#00c3ac',
                    backgroundColor: 'rgba(0, 195, 172, 0.1)',
                    tension: 0.4,
                    fill: true
                }
            ]
        };
    };

    // Chart options
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
            },
            title: {
                display: true,
                text: chartView === 'revenue' ? 'Revenue Over Time' : 'Sales Count Over Time'
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return chartView === 'revenue' ? '$' + value.toLocaleString() : value;
                    }
                }
            }
        }
    };
    // Format currency
    const formatCurrency = (value) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        }).format(value);
    };

    // Get payment method icon
    const getPaymentMethodIcon = (paymentMethod) => {
        switch (paymentMethod) {
            case 'gift':
                return <FaGift className="text-purple-500" size={16} />;
            case 'bank_transfer':
                return <BsBank2 className="text-blue-500" size={16} />;
            case 'regular':
            default:
                return <FaCreditCard className="text-green-500" size={16} />;
        }
    };

    // Format price with gift handling
    const formatPrice = (sale) => {
        const paymentMethod = sale.payment_method || 'regular';
        const totalPrice = parseFloat(sale.total_price || 0);

        if (paymentMethod === 'gift' || totalPrice === 0) {
            return (
                <div className="flex items-center space-x-2">
                    <span className="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm font-semibold">
                        GIFT
                    </span>
                    {getPaymentMethodIcon(paymentMethod)}
                </div>
            );
        }

        return (
            <div className="flex items-center space-x-2">
                <span className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                    {formatCurrency(totalPrice)}
                </span>
                {getPaymentMethodIcon(paymentMethod)}
            </div>
        );
    };

    // Format date
    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    // Handle time filter change
    const handleTimeFilterChange = (value) => {
        setTimeFilter(value);
        // Reprocess data with new filter
        if (soldPackages) {
            const processedData = processSalesData(soldPackages);
            setSalesData(processedData);
        }
    };

    // Handle chart view change
    const handleChartViewChange = (view) => {
        setChartView(view);
    };

    // Handle column sorting
    const handleSort = (field) => {
        if (sortField === field) {
            setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
        } else {
            setSortField(field);
            setSortOrder('asc');
        }
    };

    // Get sorted and paginated sales data
    const getSortedSalesData = () => {
        if (!soldPackages || soldPackages.length === 0) return [];

        // Sort data based on current sort field and order
        const sorted = [...soldPackages].sort((a, b) => {
            let valueA, valueB;

            switch (sortField) {
                case 'name':
                    valueA = (a.name || '').toLowerCase();
                    valueB = (b.name || '').toLowerCase();
                    break;
                case 'total_price':
                    valueA = parseFloat(a.total_price || 0);
                    valueB = parseFloat(b.total_price || 0);
                    break;
                case 'purchased_by_manager_name':
                    valueA = (a.purchased_by_manager_name || '').toLowerCase();
                    valueB = (b.purchased_by_manager_name || '').toLowerCase();
                    break;
                case 'purchased_at':
                    valueA = new Date(a.purchased_at || 0);
                    valueB = new Date(b.purchased_at || 0);
                    break;
                case 'expiry_date':
                    valueA = new Date(a.expiry_date || 0);
                    valueB = new Date(b.expiry_date || 0);
                    break;
                case 'status':
                    valueA = (a.status || 'active').toLowerCase();
                    valueB = (b.status || 'active').toLowerCase();
                    break;
                default:
                    valueA = new Date(a.purchased_at || 0);
                    valueB = new Date(b.purchased_at || 0);
            }

            if (valueA < valueB) return sortOrder === 'asc' ? -1 : 1;
            if (valueA > valueB) return sortOrder === 'asc' ? 1 : -1;
            return 0;
        });

        return sorted;
    };

    // Render sortable column header
    const renderSortableHeader = (label, field) => {
        return (
            <button
                onClick={() => handleSort(field)}
                className="flex items-center space-x-1 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
            >
                <span>{label}</span>
                {sortField === field && (
                    sortOrder === 'asc' ?
                        <FiArrowUp size={12} className="text-gray-600 dark:text-gray-400" /> :
                        <FiArrowDown size={12} className="text-gray-600 dark:text-gray-400" />
                )}
            </button>
        );
    };

    // Get paginated data
    const getPaginatedData = () => {
        const sortedData = getSortedSalesData();
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        return sortedData.slice(startIndex, endIndex);
    };

    // Calculate total pages
    const getTotalPages = () => {
        const sortedData = getSortedSalesData();
        return Math.ceil(sortedData.length / itemsPerPage);
    };

    // Handle page navigation
    const handlePreviousPage = () => {
        setCurrentPage(prev => Math.max(prev - 1, 1));
    };

    const handleNextPage = () => {
        const totalPages = getTotalPages();
        setCurrentPage(prev => Math.min(prev + 1, totalPages));
    };

    // Handle row click
    const handleRowClick = (sale) => {
        setSelectedSale(sale);
        setIsModalOpen(true);
    };

    // Close modal
    const closeModal = () => {
        setIsModalOpen(false);
        setSelectedSale(null);
    };



    return (
        <Container className="w-full px-[20px]">
            <div className="w-full mb-6">
                <div className="flex justify-between items-center">
                    <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-100">Sales Management Dashboard</h1>
                    <div className="flex items-center gap-4">
                        <Dropdown
                            value={timeFilter}
                            options={timeFilterOptions}
                            onChange={(e) => handleTimeFilterChange(e.value)}
                            className="w-40"
                        />
                        <Button
                            label="Export Data"
                            icon={<FiDownload className="mr-2" />}
                            className="bg-[#00c3ac] text-white border border-[#00c3ac] px-4 py-2 rounded-md hover:bg-[#02aa96] hover:border-[#02aa96] flex items-center transition-all duration-300"
                        />
                    </div>
                </div>
            </div>

            {/* Sales Metrics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700"
                >
                    <div className="flex items-center mb-4">
                        <div className="p-3 rounded-full bg-[#00c3ac]/10 text-[#00c3ac] mr-4">
                            <FiDollarSign size={24} />
                        </div>
                        <div>
                            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Revenue</h3>
                            <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{formatCurrency(salesMetrics.totalRevenue)}</p>
                        </div>
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                        {salesMetrics.growthRate >= 0 ? (
                            <span className="text-green-500 flex items-center">
                                <FiTrendingUp className="mr-1" /> +{salesMetrics.growthRate.toFixed(1)}% from previous period
                            </span>
                        ) : (
                            <span className="text-red-500 flex items-center">
                                <FiTrendingUp className="mr-1 transform rotate-180" /> {salesMetrics.growthRate.toFixed(1)}% from previous period
                            </span>
                        )}
                    </div>
                </motion.div>

                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 }}
                    className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700"
                >
                    <div className="flex items-center mb-4">
                        <div className="p-3 rounded-full bg-[#00c3ac]/10 text-[#00c3ac] mr-4">
                            <FiPackage size={24} />
                        </div>
                        <div>
                            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Sales</h3>
                            <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{salesMetrics.totalSales}</p>
                        </div>
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                        Packages sold in selected period
                    </div>
                </motion.div>

                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.2 }}
                    className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700"
                >
                    <div className="flex items-center mb-4">
                        <div className="p-3 rounded-full bg-[#00c3ac]/10 text-[#00c3ac] mr-4">
                            <FiBarChart2 size={24} />
                        </div>
                        <div>
                            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Average Sale</h3>
                            <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{formatCurrency(salesMetrics.averageSale)}</p>
                        </div>
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                        Per package average
                    </div>
                </motion.div>

                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.3 }}
                    className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700"
                >
                    <div className="flex items-center mb-4">
                        <div className="p-3 rounded-full bg-[#00c3ac]/10 text-[#00c3ac] mr-4">
                            <FiUsers size={24} />
                        </div>
                        <div>
                            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Top Package</h3>
                            <p className="text-xl font-bold text-gray-900 dark:text-gray-100 truncate">
                                {mostSoldPackages.length > 0 ? mostSoldPackages[0].name : 'No data'}
                            </p>
                        </div>
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                        {mostSoldPackages.length > 0 ? `${mostSoldPackages[0].salesCount} sales` : 'No sales yet'}
                    </div>
                </motion.div>
            </div>

            {/* Chart and Top Customers */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                    className="lg:col-span-2 bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700"
                >
                    <div className="flex justify-between items-center mb-6">
                        <h2 className="text-lg font-bold text-gray-700 dark:text-gray-100">Sales Overview</h2>
                        <div className="flex items-center space-x-2 bg-gray-100 dark:bg-gray-700 p-1 rounded-lg">
                            <button
                                className={`px-3 py-1 text-xs font-medium rounded-md transition-all ${
                                    chartView === 'revenue' ? 'bg-white dark:bg-gray-600 shadow-sm' : 'text-gray-500 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                                }`}
                                onClick={() => handleChartViewChange('revenue')}
                            >
                                Revenue
                            </button>
                            <button
                                className={`px-3 py-1 text-xs font-medium rounded-md transition-all ${
                                    chartView === 'count' ? 'bg-white dark:bg-gray-600 shadow-sm' : 'text-gray-500 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                                }`}
                                onClick={() => handleChartViewChange('count')}
                            >
                                Sales Count
                            </button>
                        </div>
                    </div>
                    <div className="h-64 flex items-center justify-center">
                        {getChartData() ? (
                            <Chart type="line" data={getChartData()} options={chartOptions} className="w-full h-full" />
                        ) : (
                            <div className="w-full h-full bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                                <div className="text-center text-gray-500 dark:text-gray-400">
                                    <FiBarChart2 size={40} className="mx-auto mb-2 text-gray-400 dark:text-gray-500" />
                                    <p>No sales data available</p>
                                </div>
                            </div>
                        )}
                    </div>
                </motion.div>

                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.5 }}
                    className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700"
                >
                    <h2 className="text-lg font-bold text-gray-700 dark:text-gray-100 mb-4">Most Sold Packages</h2>
                    {mostSoldPackages.length > 0 ? (
                        <div className="space-y-4">
                            {mostSoldPackages.map((pkg, index) => (
                                <div key={index} className="flex items-start space-x-3 pb-3 border-b border-gray-100 dark:border-gray-700">
                                    <div className="p-2 rounded-full bg-[#00c3ac]/10 text-[#00c3ac]">
                                        <FiPackage />
                                    </div>
                                    <div className="flex-1">
                                        <div className="flex justify-between">
                                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{pkg.name}</p>
                                            <p className="text-xs text-gray-500 dark:text-gray-400">{pkg.salesCount} sales</p>
                                        </div>
                                        <p className="text-xs text-gray-500 dark:text-gray-400">
                                            {formatCurrency(pkg.totalRevenue)} total revenue
                                        </p>
                                        <p className="text-xs text-gray-400 dark:text-gray-500">
                                            Avg: {formatCurrency(pkg.averagePrice)}
                                        </p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                            <p>No package data available</p>
                        </div>
                    )}
                </motion.div>
            </div>

            {/* Modern Sales Table */}
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 mb-8 overflow-hidden"
            >
                <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h2 className="text-lg font-bold text-gray-700 dark:text-gray-100">Recent Sales</h2>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Latest package sales and transactions</p>
                </div>

                {packagesLoading ? (
                    <div className="flex items-center justify-center py-12">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#00c3ac]"></div>
                        <span className="ml-3 text-gray-500 dark:text-gray-400">Loading sales data...</span>
                    </div>
                ) : !soldPackages || soldPackages.length === 0 ? (
                    <div className="text-center py-12">
                        <FiPackage size={48} className="mx-auto text-gray-400 dark:text-gray-500 mb-4" />
                        <p className="text-gray-500 dark:text-gray-400 text-lg">No sales data available</p>
                        <p className="text-gray-400 dark:text-gray-500 text-sm">Sales will appear here once packages are purchased</p>
                    </div>
                ) : (
                    <>
                        {/* Desktop Table View */}
                        {!isMobile ? (
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead className="bg-gray-50 dark:bg-gray-700">
                                        <tr>
                                            <th className="px-6 py-4 text-left">{renderSortableHeader('Package', 'name')}</th>
                                            <th className="px-6 py-4 text-left">{renderSortableHeader('Price', 'total_price')}</th>
                                            <th className="px-6 py-4 text-left">{renderSortableHeader('Customer', 'purchased_by_manager_name')}</th>
                                            <th className="px-6 py-4 text-left">{renderSortableHeader('Purchase Date', 'purchased_at')}</th>
                                            <th className="px-6 py-4 text-left">{renderSortableHeader('Expiry Date', 'expiry_date')}</th>
                                            <th className="px-6 py-4 text-left">{renderSortableHeader('Status', 'status')}</th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                        {getPaginatedData().map((sale, index) => (
                                            <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 cursor-pointer" onClick={() => handleRowClick(sale)}>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="flex items-center">
                                                        <div className="p-2 rounded-full bg-[#00c3ac]/10 text-[#00c3ac] mr-3">
                                                            <FiPackage size={16} />
                                                        </div>
                                                        <div>
                                                            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">{sale.name || 'Unnamed Package'}</div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    {formatPrice(sale)}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-900 dark:text-gray-100">{sale.purchased_by_manager_name || 'Unknown'}</div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-900 dark:text-gray-100">{formatDate(sale.purchased_at)}</div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-900 dark:text-gray-100">{formatDate(sale.expiry_date)}</div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full ${
                                                        (sale.status || 'active') === 'active'
                                                            ? 'bg-green-100 text-green-800'
                                                            : 'bg-red-100 text-red-800'
                                                    }`}>
                                                        {sale.status || 'active'}
                                                    </span>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        ) : (
                            /* Mobile Card View */
                            <div className="p-4 space-y-4">
                                {getPaginatedData().map((sale, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.3, delay: index * 0.05 }}
                                        className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600 hover:shadow-md transition-all duration-200 cursor-pointer"
                                        onClick={() => handleRowClick(sale)}
                                    >
                                        <div className="flex items-start justify-between mb-3">
                                            <div className="flex items-center">
                                                <div className="p-2 rounded-full bg-[#00c3ac]/10 text-[#00c3ac] mr-3">
                                                    <FiPackage size={18} />
                                                </div>
                                                <div>
                                                    <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">{sale.name || 'Unnamed Package'}</h3>
                                                    <p className="text-xs text-gray-500 dark:text-gray-400">{sale.purchased_by_manager_name || 'Unknown Customer'}</p>
                                                </div>
                                            </div>
                                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                                (sale.status || 'active') === 'active'
                                                    ? 'bg-green-100 text-green-800'
                                                    : 'bg-red-100 text-red-800'
                                            }`}>
                                                {sale.status || 'active'}
                                            </span>
                                        </div>

                                        <div className="grid grid-cols-2 gap-4 text-sm">
                                            <div>
                                                <span className="text-gray-500 dark:text-gray-400">Price:</span>
                                                <div className="mt-1">{formatPrice(sale)}</div>
                                            </div>
                                            <div>
                                                <span className="text-gray-500 dark:text-gray-400">Expiry Date:</span>
                                                <p className="font-semibold text-gray-900 dark:text-gray-100">{formatDate(sale.expiry_date)}</p>
                                            </div>
                                            <div className="col-span-2">
                                                <span className="text-gray-500 dark:text-gray-400">Purchase Date:</span>
                                                <p className="font-semibold text-gray-900 dark:text-gray-100">{formatDate(sale.purchased_at)}</p>
                                            </div>
                                        </div>
                                    </motion.div>
                                ))}
                            </div>
                        )}

                        {/* Pagination */}
                        {getSortedSalesData().length > itemsPerPage && (
                            <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
                                <div className="flex items-center justify-between">
                                    <p className="text-sm text-gray-700 dark:text-gray-300">
                                        Showing <span className="font-medium">{((currentPage - 1) * itemsPerPage) + 1}</span> to{' '}
                                        <span className="font-medium">{Math.min(currentPage * itemsPerPage, getSortedSalesData().length)}</span> of{' '}
                                        <span className="font-medium">{getSortedSalesData().length}</span> results
                                    </p>
                                    <div className="flex items-center space-x-2">
                                        <button
                                            onClick={handlePreviousPage}
                                            disabled={currentPage === 1}
                                            className={`px-3 py-1 text-sm border rounded-md transition-colors ${
                                                currentPage === 1
                                                    ? 'bg-gray-100 dark:bg-gray-600 border-gray-300 dark:border-gray-500 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                                                    : 'bg-white dark:bg-gray-600 border-gray-300 dark:border-gray-500 hover:bg-gray-50 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200'
                                            }`}
                                        >
                                            Previous
                                        </button>
                                        <span className="text-sm text-gray-700 dark:text-gray-300">
                                            Page {currentPage} of {getTotalPages()}
                                        </span>
                                        <button
                                            onClick={handleNextPage}
                                            disabled={currentPage === getTotalPages()}
                                            className={`px-3 py-1 text-sm border rounded-md transition-colors ${
                                                currentPage === getTotalPages()
                                                    ? 'bg-gray-100 dark:bg-gray-600 border-gray-300 dark:border-gray-500 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                                                    : 'bg-white dark:bg-gray-600 border-gray-300 dark:border-gray-500 hover:bg-gray-50 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200'
                                            }`}
                                        >
                                            Next
                                        </button>
                                    </div>
                                </div>
                            </div>
                        )}
                    </>
                )}
            </motion.div>

            {/* Sales Detail Modal */}
            <SalesDetailModal
                isOpen={isModalOpen}
                onClose={closeModal}
                saleData={selectedSale}
            />
        </Container>
    );
}

export default SalesManagementDashboard;

