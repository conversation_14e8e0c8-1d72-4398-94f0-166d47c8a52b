/* Professional Image Gallery Styles */

.professional-image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 1px;
  padding: 5px;
  overflow-y: auto;
  overflow-x: hidden;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.6),
    0 2px 8px rgba(0, 0, 0, 0.04);
  width: 100%;
  min-height: 200px;
}

/* Dark mode styles for professional image gallery */
.dark .professional-image-gallery {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%) !important;
  border: 1px solid rgba(75, 85, 99, 0.8) !important;
  box-shadow:
    inset 0 1px 0 rgba(55, 65, 81, 0.6),
    0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

.professional-image-gallery::-webkit-scrollbar {
  width: 6px;
}

.professional-image-gallery::-webkit-scrollbar-track {
  background: rgba(241, 245, 249, 0.5);
  border-radius: 3px;
}

.professional-image-gallery::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #8b3dff, #6d28d9);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.professional-image-gallery::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #7c3aed, #5b21b6);
}

.professional-image-item {
  position: relative;
  border-radius: 10px;
  overflow: hidden;
  background: #ffffff;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.08),
    0 2px 4px rgba(0, 0, 0, 0.04);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  border: 2px solid transparent;
  transform: translateZ(0);
  backface-visibility: hidden;
  flex: 0 0 auto;
  display: inline-block;
  width: fit-content;
  height: fit-content;
  max-width: 200px;
  max-height: 150px;
  min-width: 60px;
  min-height: 40px;
}

/* Dark mode styles for professional image item */
.dark .professional-image-item {
  background: #374151 !important;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

.professional-image-item:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 
    0 12px 32px rgba(139, 61, 255, 0.15),
    0 8px 16px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(139, 61, 255, 0.2);
  border-color: rgba(139, 61, 255, 0.3);
}

.professional-image-container {
  position: relative;
  width: fit-content;
  height: fit-content;
  overflow: hidden;
  border-radius: 8px;
}

.professional-image {
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border-radius: 8px;
  display: block;
}

.gallery-image {
  width: auto;
  height: auto;
  max-width: 200px;
  max-height: 150px;
  min-width: 60px;
  min-height: 40px;
  object-fit: contain;
  display: block;
}

.qr-image {
  width: auto;
  height: auto;
  max-width: 120px;
  max-height: 120px;
  object-fit: contain;
  background: #ffffff;
  padding: 8px;
}

.professional-image-item:hover .professional-image {
  transform: scale(1.05);
  filter: brightness(1.1) contrast(1.05);
}

.professional-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(240, 248, 255, 0.15) 50%,
    rgba(230, 240, 255, 0.2) 100%
  );
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(0.5px);
  border-radius: 8px;
}

.professional-image-item:hover .professional-image-overlay {
  opacity: 1;
}

.professional-image-actions {
  display: flex;
  gap: 3px;
  transform: translateY(10px);
  transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.professional-image-item:hover .professional-image-actions {
  transform: translateY(0);
}

.professional-action-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.95);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  color: #8b3dff;
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.15),
    0 2px 4px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.professional-action-btn:hover {
  background: #ffffff;
  transform: scale(1.1);
  box-shadow: 
    0 6px 20px rgba(0, 0, 0, 0.2),
    0 4px 8px rgba(0, 0, 0, 0.15);
  color: #6d28d9;
}

.professional-action-btn:active {
  transform: scale(0.95);
}

/* Empty State Styles */
.professional-empty-state {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px;
  border: 2px dashed rgba(139, 61, 255, 0.2);
  margin: 20px 0;
}

.professional-empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.professional-empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

/* Dark mode styles for professional empty title */
.dark .professional-empty-title {
  color: #f3f4f6 !important;
}

.professional-empty-subtitle {
  font-size: 14px;
  color: #6b7280;
  opacity: 0.8;
}

/* Dark mode styles for professional empty subtitle */
.dark .professional-empty-subtitle {
  color: #9ca3af !important;
}

/* Loading State Styles */
.professional-loading-state {
  grid-column: 1 / -1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3px;
  padding: 10px;
  color: #6b7280;
  font-weight: 500;
}

/* Dark mode styles for professional loading state */
.dark .professional-loading-state {
  color: #9ca3af !important;
}

.professional-loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(139, 61, 255, 0.2);
  border-top: 3px solid #8b3dff;
  border-radius: 50%;
  animation: professional-spin 1s linear infinite;
}

/* Animations */
@keyframes professional-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes gentle-bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .professional-image-gallery {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 4px;
    padding: 12px;
  }
  
  .professional-action-btn {
    width: 36px;
    height: 36px;
  }
  
  .professional-empty-state {
    padding: 30px 15px;
  }
  
  .professional-empty-icon {
    font-size: 36px;
  }
  
  .professional-empty-title {
    font-size: 16px;
  }
  
  .professional-empty-subtitle {
    font-size: 13px;
  }
}

/* Special effects for QR images */
.professional-image-item:has(.qr-image) {
  aspect-ratio: 1/1;
}

.professional-image-item:has(.qr-image):hover {
  transform: translateY(-2px) scale(1.01);
}

/* Hover effects with neon glow */
.professional-image-item::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #8b3dff, #6d28d9, #5b21b6, #8b3dff);
  border-radius: 12px;
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
  animation: professional-gradient-shift 3s ease infinite;
}

.professional-image-item:hover::before {
  opacity: 0.6;
}

@keyframes professional-gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Professional Library Section Styles */
.professional-library-section {
  margin: 16px 8px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  padding: 20px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.04),
    0 2px 8px rgba(0, 0, 0, 0.02),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Dark mode styles for professional library section */
.dark .professional-library-section {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%) !important;
  border: 1px solid rgba(75, 85, 99, 0.6) !important;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(55, 65, 81, 0.8) !important;
}

.professional-library-section:hover {
  box-shadow:
    0 8px 24px rgba(139, 61, 255, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
}

.professional-library-header {
  margin-bottom: 16px;
  text-align: center;
}

.professional-library-title {
  font-size: 18px;
  font-weight: 700;
  color: #8b3dff;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}



.professional-library-icon {
  font-size: 20px;
}

.professional-library-subtitle {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
  opacity: 0.8;
  letter-spacing: 0.5px;
}

/* Dark mode styles for professional library subtitle */
.dark .professional-library-subtitle {
  color: #9ca3af !important;
}

/* Enhanced image effects */
.professional-image-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 70%
  );
  opacity: 0;
  transform: translateX(-100%);
  transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border-radius: 8px;
  pointer-events: none;
}

.professional-image-item:hover::after {
  opacity: 1;
  transform: translateX(100%);
}

/* Beautiful grid layout adjustments */
.professional-image-gallery {
  background-attachment: fixed;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(139, 61, 255, 0.02) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(109, 40, 217, 0.02) 0%, transparent 50%);
}

/* Improved spacing and proportions */
@media (min-width: 1024px) {
  .professional-image-gallery {
    gap: 8px;
    padding: 20px;
    min-height: 250px;
  }

  .professional-image-item {
    max-width: 180px;
    max-height: 140px;
    min-width: 80px;
    min-height: 60px;
  }

  .gallery-image {
    max-width: 180px;
    max-height: 140px;
    min-width: 80px;
    min-height: 60px;
  }
}

@media (min-width: 1280px) {
  .professional-image-gallery {
    gap: 10px;
    min-height: 300px;
  }

  .professional-image-item {
    max-width: 200px;
    max-height: 150px;
    min-width: 100px;
    min-height: 80px;
  }

  .gallery-image {
    max-width: 200px;
    max-height: 150px;
    min-width: 100px;
    min-height: 80px;
  }
}

/* Professional loading animation */
.professional-loading-spinner {
  background: conic-gradient(from 0deg, transparent, #8b3dff, transparent);
  border: none;
  border-radius: 50%;
  position: relative;
}

.professional-loading-spinner::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  background: #f8fafc;
  border-radius: 50%;
}

/* Smooth entrance animations */
.professional-image-item {
  animation: professional-fade-in 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
  opacity: 0;
  transform: translateY(20px) scale(0.9);
}

.professional-image-item:nth-child(1) { animation-delay: 0.1s; }
.professional-image-item:nth-child(2) { animation-delay: 0.2s; }
.professional-image-item:nth-child(3) { animation-delay: 0.3s; }
.professional-image-item:nth-child(4) { animation-delay: 0.4s; }
.professional-image-item:nth-child(5) { animation-delay: 0.5s; }
.professional-image-item:nth-child(6) { animation-delay: 0.6s; }
.professional-image-item:nth-child(n+7) { animation-delay: 0.7s; }

@keyframes professional-fade-in {
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced hover states */
.professional-image-item:hover {
  z-index: 10;
}

.professional-image-item:hover .professional-image {
  filter: brightness(1.1) contrast(1.05) saturate(1.1);
}

.qr-link-label {
  word-break: break-all;
  overflow-wrap: anywhere;
  display: block;
  max-width: 100%;
  white-space: normal;
  z-index: 2;
  position: relative;
  padding: 2px 4px;
  border-radius: 3px;
  margin-bottom: 4px;
  transition: all 0.3s ease;
}

/* Dark mode styles for QR link label */
.dark .qr-link-label {
  background: rgba(31, 41, 55, 0.8) !important;
  color: #d1d5db !important;
  border: 1px solid rgba(75, 85, 99, 0.3);
}

.dark .qr-link-label:hover {
  background: rgba(55, 65, 81, 0.9) !important;
  color: #f3f4f6 !important;
}

.delete-image-btn {
  position: absolute;
  top: 6px;
  right: 6px;
  z-index: 3;
  background: rgba(255,255,255,0.85);
  border: none;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #e53e3e;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  cursor: pointer;
  transition: background 0.2s;
}

/* Dark mode styles for delete image button */
.dark .delete-image-btn {
  background: rgba(55, 65, 81, 0.9) !important;
  color: #f87171 !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.3) !important;
}
.delete-image-btn:hover {
  background: #ffeaea;
}

/* Dark mode styles for delete image button hover */
.dark .delete-image-btn:hover {
  background: rgba(75, 85, 99, 0.95) !important;
}
