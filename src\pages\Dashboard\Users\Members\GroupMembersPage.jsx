import React, { useEffect, useState, useCallback } from "react";
import { Link } from "react-router-dom";
import { isEmpty } from "lodash";
import parse from "html-react-parser";
import { useQueryClient } from "react-query";

import AssignGroupDialog from "../Groups/AssignGroupDialog";
import AddMemberDialog from "./AddMemberDialog";
import GroupForm from "../../Backages/CreateGroupForm";
import axiosInstance from "../../../../config/Axios";
import { Dropdown } from 'primereact/dropdown';
import { usersTableConfig } from "@constants";
import { useMembersDataTableContext } from "@contexts/MembersDataTableContext";
import { useGlobalContext } from "@contexts/GlobalContext";
import { useLayout } from "@contexts/LayoutContext";
import { useQueryParams } from "@utils/helper";
import { Button } from 'primereact/button';
import { DataTable } from "primereact/datatable";
import { Tooltip } from "primereact/tooltip";
import { Column } from "primereact/column";
import { confirmDialog, ConfirmDialog } from 'primereact/confirmdialog';
import { Dialog } from 'primereact/dialog';
import { defaultTableConfig } from "@constants";
import GroupFilter from "./GroupFilter";
import { TfiTrash } from "react-icons/tfi";
import { FiEdit } from "react-icons/fi";
import { FiArrowLeft } from "react-icons/fi";
import { FaUsers } from "react-icons/fa";
import { BiGroup } from "react-icons/bi";
import { BsCheckCircle, BsXCircle } from "react-icons/bs";
import { MdClear, MdTrendingUp } from "react-icons/md";
import { Toast } from "primereact/toast";
import profile_img from "@images/Profile_img.jpg";
import ImageGenerationModal from "../../DesignSpace/components/ImageGenerationModal";
import { createPortal } from 'react-dom';
import { HiDotsVertical } from 'react-icons/hi';
import { HiViewGrid, HiViewList } from 'react-icons/hi';
import { HiUser, HiOfficeBuilding, HiBriefcase, HiIdentification } from 'react-icons/hi';
import { FaUserTie, FaBuilding, FaIdCard, FaUserTag } from 'react-icons/fa';
// أضف كلاس CSS في أعلى الملف
import "./GroupMembersPage.css";

// مكون Card Member Component
const CardMemberComponent = ({ 
  member, 
  isSelected, 
  onSelect, 
  onEdit, 
  onDelete, 
  onTemplateView,
  lazyParams,
  statusStyles,
  profile_img,
  handleAssignCard,
  handleUnassignCard,
  checkCardAssignmentStatus,
  getAvailableCards,
  pendingCardAssignments,
  memberCards,
  setSelectedMember,
  dialogHandler,
  handleDeleteMemberClick,
  confirmDialog,
  setSelectedTemplate,
  setTemplateModalVisible,
  groupId
}) => {
  const [cardDetails, setCardDetails] = useState(null);
  const [cardDetailsLoading, setCardDetailsLoading] = useState(true);
  const [assignmentStatus, setAssignmentStatus] = useState(null);
  const [assignmentLoading, setAssignmentLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);

  // جلب تفاصيل البطاقة
  useEffect(() => {
    const fetchCardDetails = async () => {
      setCardDetailsLoading(true);
      const status = await checkCardAssignmentStatus(member.id);
      if (status && status.status === 'assigned') {
        setCardDetails(status.card);
      } else {
        setCardDetails(null);
      }
      setCardDetailsLoading(false);
    };

    fetchCardDetails();
  }, [member.id, pendingCardAssignments]);

  // جلب حالة التعيين
  useEffect(() => {
    const fetchAssignmentStatus = async () => {
      setAssignmentLoading(true);
      const status = await checkCardAssignmentStatus(member.id);
      setAssignmentStatus(status);
      setAssignmentLoading(false);
    };

    fetchAssignmentStatus();
  }, [member.id, memberCards, pendingCardAssignments]);

  const handleCardAssign = (memberId, card) => {
    handleAssignCard(memberId, card);
  };

  const handleCardUnassign = (memberId, cardNumber) => {
    handleUnassignCard(memberId, cardNumber);
  };

  const availableCards = getAvailableCards(member.id);
  const pendingAssignment = pendingCardAssignments[member.id];
  const hasPendingAssignment = pendingAssignment && pendingAssignment.action === 'assign';
  const hasPendingUnassignment = pendingAssignment && pendingAssignment.action === 'unassign';

  return (
    <div className={`group relative bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 border-2 overflow-hidden ${
      isSelected ? 'border-blue-500 ring-2 ring-blue-200 dark:ring-blue-800' : 'border-gray-200 dark:border-gray-700'
    }`}>
      {/* Checkbox Selection - moved to top-right */}
      <div className="absolute top-3 right-3 z-10">
        <input
          type="checkbox"
          checked={isSelected}
          onChange={onSelect}
          className="w-5 h-5 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
        />
      </div>

      {/* Member Profile Header - moved to top */}
      <div className="relative px-4 pt-4 pb-3 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-700 dark:to-gray-800 border-b border-gray-200 dark:border-gray-600">
        <div className="flex items-center space-x-3">
          <div className="relative">
            <img
              src={member.image || profile_img}
              alt={member.name}
              className="w-12 h-12 rounded-full border-3 border-white dark:border-gray-800 shadow-lg object-cover"
            />
            <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-emerald-500 rounded-full border-2 border-white dark:border-gray-800 shadow-md"></div>
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white truncate flex items-center" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>
              {member.name}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-300 truncate flex items-center font-medium" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>
              <HiBriefcase className="w-3 h-3 mr-1 text-gray-500 dark:text-gray-400" />
              {member.position || 'Position'}
            </p>
          </div>
          {/* Status Indicator - moved here */}
          <div className="relative">
            {cardDetails ? (
              <>
                <div className="w-3 h-3 bg-emerald-400 rounded-full shadow-lg animate-pulse"></div>
                <div className="absolute inset-0 w-3 h-3 bg-emerald-400 rounded-full animate-ping opacity-75"></div>
              </>
            ) : (
              <>
                <div className="w-3 h-3 bg-red-400 rounded-full shadow-lg animate-pulse"></div>
                <div className="absolute inset-0 w-3 h-3 bg-red-400 rounded-full animate-ping opacity-75"></div>
              </>
            )}
          </div>
        </div>

        {/* Member Type and Department - moved here and made smaller */}
        <div className="mt-2 flex flex-wrap gap-1">
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 dark:from-purple-900/30 dark:to-pink-900/30 dark:text-purple-300 shadow-sm border border-purple-200/50 dark:border-purple-700/50" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>
            <FaUserTag className="w-3 h-3 mr-1 text-purple-600 dark:text-purple-400" />
            {member.type || 'Member'}
          </span>
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 dark:from-blue-900/30 dark:to-indigo-900/30 dark:text-blue-300 shadow-sm border border-blue-200/50 dark:border-blue-700/50" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>
            <FaBuilding className="w-3 h-3 mr-1 text-blue-600 dark:text-blue-400" />
            {member.department || 'Department'}
          </span>
        </div>
      </div>

      {/* Card Status Section - simplified and reduced */}
      <div className={`relative px-4 py-3 ${
        cardDetails
          ? 'bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20'
          : 'bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20'
      }`}>
        {cardDetailsLoading ? (
          <div className="flex justify-center py-2">
            <i className="pi pi-spin pi-spinner text-blue-500"></i>
          </div>
        ) : cardDetails ? (
          <div className="text-center">
            <div className="text-xs font-semibold mb-1 tracking-wider flex items-center justify-center text-blue-600 dark:text-blue-400">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
              CARD NUMBER
            </div>
            <div className="font-mono text-lg font-bold tracking-widest text-blue-800 dark:text-blue-200">
              {cardDetails.number || 'N/A'}
            </div>
          </div>
        ) : (
          <div className="text-center">
            <div className="text-xs font-semibold mb-1 tracking-wider flex items-center justify-center text-red-600 dark:text-red-400">
              <div className="w-2 h-2 bg-red-500 rounded-full mr-2 animate-pulse"></div>
              STATUS
            </div>
            <div className="text-sm font-semibold text-red-700 dark:text-red-300">
              No Card Assigned
            </div>
          </div>
        )}
      </div>

      {/* Main Content Section */}
      <div className="relative px-3 pb-3">

        {/* Card Design/Number Display - reduced size */}
        <div className="space-y-1 mb-2">
          <div className="bg-gradient-to-r from-blue-50 to-gray-50 dark:from-blue-900/20 dark:to-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-1.5 shadow-sm">
            <div className="flex justify-between items-center mb-1">
              <span className="text-xs font-semibold text-blue-600 dark:text-blue-400 flex items-center">
                <FaIdCard className="w-3 h-3 mr-1" />
                {member.template_image_html ? 'CARD DESIGN' : 'CARD NUMBER'}
              </span>
            </div>
            {cardDetailsLoading ? (
              <div className="flex justify-center py-1">
                <i className="pi pi-spin pi-spinner text-blue-500 text-sm"></i>
              </div>
            ) : cardDetails ? (
              member.template_image_html ? (
                // Display template design - truly compact
                <div className="relative bg-white dark:bg-slate-700 rounded border border-gray-200 dark:border-slate-500 overflow-hidden card-design-preview" style={{ height: '60px' }}>
                  <div
                    className="w-full h-full flex items-center justify-center"
                    style={{
                      background: '#ffffff',
                      overflow: 'hidden'
                    }}
                  >
                    {/* Extract and display only the capture section - much smaller and contained */}
                    <div
                      style={{
                        width: '100%',
                        height: '100%',
                        background: '#ffffff',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        position: 'relative',
                        transform: 'scale(0.3)',
                        transformOrigin: 'center center'
                      }}
                      dangerouslySetInnerHTML={{
                        __html: (() => {
                          const fullTemplate = member.template_image_html.replace(
                            'https://www.gravatar.com/avatar/?d=mp',
                            member.image || 'https://www.gravatar.com/avatar/?d=mp'
                          );

                          // Create a temporary div to parse the HTML
                          const tempDiv = document.createElement('div');
                          tempDiv.innerHTML = fullTemplate;

                          // Find the capture section (usually a div with specific class or id)
                          const captureSection = tempDiv.querySelector('[class*="capture"], [id*="capture"], .photo-section, .image-section, .profile-section, [class*="photo"], [class*="image"], [class*="profile"]');

                          if (captureSection) {
                            return captureSection.outerHTML;
                          }

                          // If no specific capture section found, try to extract the first image or profile area
                          const firstImage = tempDiv.querySelector('img');
                          if (firstImage) {
                            return firstImage.outerHTML;
                          }

                          // Fallback to the full template
                          return fullTemplate;
                        })()
                      }}
                    />
                  </div>

                  {/* Design Label - overlay */}
                  <div className="absolute bottom-0 right-0 z-10">
                    <div className="px-1 py-0.5 bg-slate-700/80 text-white text-xs rounded-tl">
                      Design
                    </div>
                  </div>
                </div>
              ) : (
                // Display card number
                <div className="flex justify-between items-center">
                  <span className="font-mono text-sm font-bold tracking-wider text-gray-900 dark:text-gray-100">
                    {cardDetails.number || 'N/A'}
                  </span>
                  <i className="pi pi-credit-card text-gray-400 dark:text-gray-500 ml-2"></i>
                </div>
              )
            ) : (
              <span className="text-sm text-gray-500 dark:text-gray-400 italic flex items-center">
                <i className="pi pi-info-circle mr-1"></i>
                No card assigned
              </span>
            )}
          </div>

          {/* Card Assignment Section - compact */}
          <div className="p-1.5 bg-gradient-to-r from-gray-50 to-slate-50 dark:from-gray-700/50 dark:to-slate-700/50 rounded border border-gray-200 dark:border-gray-600 shadow-sm">
            <h4 className="text-xs font-semibold text-gray-700 dark:text-gray-300 mb-1 flex items-center">
              <FaUserTie className="w-3 h-3 mr-1 text-indigo-600 dark:text-indigo-400" />
              Card Assignment
            </h4>
            {assignmentLoading ? (
              <div className="flex justify-center py-1">
                <i className="pi pi-spin pi-spinner text-blue-500 text-sm"></i>
              </div>
            ) : hasPendingAssignment ? (
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded p-1">
                <div className="flex justify-between items-center mb-1">
                  <span className="text-xs font-semibold text-yellow-600 dark:text-yellow-400">PENDING</span>
                  <button
                    onClick={() => handleCardUnassign(member.id, pendingAssignment.cardNumber)}
                    className="text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300"
                    title="Cancel Assignment"
                  >
                    <i className="pi pi-times text-xs"></i>
                  </button>
                </div>
                <div className="font-mono text-xs font-bold text-yellow-800 dark:text-yellow-300">
                  {pendingAssignment.cardNumber}
                </div>
              </div>
            ) : hasPendingUnassignment ? (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded p-1">
                <div className="flex justify-between items-center mb-1">
                  <span className="text-xs font-semibold text-red-600 dark:text-red-400">UNASSIGNING</span>
                  <button
                    onClick={() => handleCardUnassign(member.id, pendingAssignment.cardNumber)}
                    className="text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300"
                    title="Cancel Unassignment"
                  >
                    <i className="pi pi-times text-xs"></i>
                  </button>
                </div>
                <div className="font-mono text-xs font-bold text-red-800 dark:text-red-300">
                  {pendingAssignment.cardNumber}
                </div>
              </div>
            ) : assignmentStatus?.status !== 'assigned' ? (
              availableCards.length > 0 ? (
                <div className="relative">
                  <Dropdown
                    value={null}
                    options={availableCards}
                    onChange={(e) => e.value && handleCardAssign(member.id, e.value)}
                    optionLabel="label"
                    className="w-full text-xs"
                    panelClassName="border border-gray-200 dark:border-gray-600 shadow-xl rounded-lg mt-1"
                    dropdownIcon="pi pi-chevron-down"
                    placeholder={`Assign (${availableCards.length})`}
                    pt={{
                      root: { className: 'border border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400 transition-colors text-xs' },
                      trigger: { className: 'bg-white dark:bg-gray-800 text-xs' },
                      panel: { className: 'shadow-lg bg-white dark:bg-gray-800' },
                      item: ({ context }) => ({
                        className: context.selected ? 'bg-blue-50 dark:bg-blue-900/20' : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                      }),
                    }}
                    itemTemplate={(option) => (
                      <div className="flex items-center justify-between p-1">
                        <div className="flex items-center">
                          {option.cardData?.card_type?.type_of_connection === 'NFC' ? (
                            <div className="text-blue-500 dark:text-blue-400 mr-2">
                              <svg className="w-3 h-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 4C7.58 4 4 7.58 4 12C4 16.42 7.58 20 12 20C16.42 20 20 16.42 20 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                              </svg>
                            </div>
                          ) : (
                            <div className="text-purple-500 dark:text-purple-400 mr-2">
                              <svg className="w-3 h-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M6 8L18 16L12 22V2L18 8L6 16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              </svg>
                            </div>
                          )}
                          <div>
                            <div className="font-medium text-gray-900 dark:text-gray-100 text-xs">{option.label}</div>
                          </div>
                        </div>
                        {option.number && (
                          <div className="font-mono text-xs bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100 px-1 py-0.5 rounded">
                            {option.number.slice(-4)}
                          </div>
                        )}
                      </div>
                    )}
                  />
                  {isUpdating && (
                    <div className="absolute inset-0 bg-white bg-opacity-50 flex items-center justify-center">
                      <i className="pi pi-spinner pi-spin text-blue-500 text-xs"></i>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center p-1 border border-dashed border-gray-300 dark:border-gray-600 rounded bg-gray-50 dark:bg-gray-800">
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    No cards available
                  </p>
                </div>
              )
            ) : (
              <Button
                severity="danger"
                label="Unassign"
                tooltip="Unassign Card"
                tooltipOptions={{ position: 'top' }}
                onClick={() => handleCardUnassign(member.id, assignmentStatus.card.number)}
                disabled={isUpdating}
                className="w-full text-xs"
                style={{
                  backgroundColor: '#ef4444',
                  borderColor: '#ef4444',
                  color: 'white',
                  borderRadius: '6px',
                  fontSize: '11px',
                  padding: '4px 8px'
                }}
              />
            )}
          </div>

          {/* Print Status - compact */}
          <div className="flex items-center justify-between p-1.5 bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 border border-amber-200 dark:border-amber-700 rounded shadow-sm">
            <span className="text-xs font-medium text-gray-700 dark:text-gray-300 flex items-center">
              <i className="pi pi-print mr-1 text-amber-600 dark:text-amber-400 text-xs"></i>
              Print Status
            </span>
            {!lazyParams?.designID ? (
              <div className="flex items-center gap-1">
                <span className="text-xs font-medium text-amber-700 dark:text-amber-300">
                  Design Required
                </span>
              </div>
            ) : (
              <span className={`px-1 py-0.5 rounded-full text-xs font-medium text-white shadow-sm ${
                statusStyles[member.print_status?.toLowerCase() || 'unprinted']
              }`}>
                {member.print_status || 'Unprinted'}
              </span>
            )}
          </div>
        </div>

        {/* Actions - compact */}
        <div className="flex items-center justify-between pt-2 border-t border-gray-200 dark:border-gray-700">
          <div className="flex space-x-1">
            <button
              onClick={onEdit}
              className="p-1.5 text-gray-500 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded transition-all duration-200"
            >
              <FiEdit className="w-3 h-3" />
            </button>
            <button
              onClick={onDelete}
              className="p-1.5 text-gray-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-all duration-200"
            >
              <TfiTrash className="w-3 h-3" />
            </button>
          </div>

          {/* Template Preview */}
          {member.template_image_html && (
            <button
              onClick={onTemplateView}
              className="px-2 py-1 text-xs font-medium text-indigo-600 bg-indigo-100 hover:bg-indigo-200 dark:bg-indigo-900/30 dark:text-indigo-300 dark:hover:bg-indigo-900/50 rounded transition-all duration-200"
            >
              View
            </button>
          )}
        </div>
      </div>

      {/* Hover Effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
    </div>
  );
};

const statusStyles = {
  printed: "bg-[#22C55E] ",
  unprinted: "bg-[#64748B] ",
  onProgress: "bg-[#D97706] ",
};

function GroupMembersPage() {
  // State للتبديل بين عرض الجدول والـ Cards
  const [viewMode, setViewMode] = useState('table'); // 'table' أو 'cards'
  
  const {
    totalRecords,
    lazyParams,
    setLazyParams,
    data,
    dataHandler,
    loading,
    rawData,
    toast,
  } = useMembersDataTableContext();
  const { dialogHandler, openDialog, selectedMembers, setSelectedMembers } =
    useGlobalContext();
  const { isMobile } = useLayout();
  const queryParams = useQueryParams();
  const groupID = queryParams.get("group-id");
  const toastRef = React.useRef(null);
  const [isEditGroupModalOpen, setIsEditGroupModalOpen] = useState(false);
  const [groupBeingEdited, setGroupBeingEdited] = useState(null);
  const [selectedMember, setSelectedMember] = useState();
  const [actionType, setActionType] = useState("create");
  const [currentGroupData, setCurrentGroupData] = useState(null);
  const [memberCards, setMemberCards] = useState([]);
  const [templateModalVisible, setTemplateModalVisible] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [templateZoomLevel, setTemplateZoomLevel] = useState(1.0);
  const [pendingCardAssignments, setPendingCardAssignments] = useState({}); // Store pending assignments locally
  const [showExitWarningModal, setShowExitWarningModal] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState(null);
  const [showImageGenerationModal, setShowImageGenerationModal] = useState(false);
  const [currentDesignId, setCurrentDesignId] = useState(null);

  const [mobileActionMenuOpen, setMobileActionMenuOpen] = useState(null);
  const [selectedUnassignUsers, setSelectedUnassignUsers] = useState([]);
  const [usersWithAssignedCards, setUsersWithAssignedCards] = useState(new Set());
  const [groupDesigns, setGroupDesigns] = useState([]);

  const queryClient = useQueryClient();

  // Function to check and update users with assigned cards
  const updateUsersWithAssignedCards = useCallback(async () => {
    if (!data || data.length === 0) return;
    
    const assignedUsers = new Set();
    
    for (const user of data) {
      try {
        const status = await checkCardAssignmentStatus(user.id);
        if (status && status.status === 'assigned' && status.card && status.card.number) {
          assignedUsers.add(user.id);
        }
      } catch (error) {
        console.error(`Error checking card status for user ${user.id}:`, error);
      }
    }
    
    setUsersWithAssignedCards(assignedUsers);
  }, [data]);

  // Update users with assigned cards when data changes
  useEffect(() => {
    updateUsersWithAssignedCards();
  }, [updateUsersWithAssignedCards]);

  // Extract fetchMemberCards as a separate function so it can be reused
  const fetchMemberCards = useCallback(async () => {
    try {
      const token = localStorage.getItem('token');
      const userId = localStorage.getItem('user_id');
      const groupId = new URLSearchParams(window.location.search).get('group-id');

      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        }
      };

      if (groupId) {
        config.params = {
          group_id: groupId
        };
      }

      const response = await axiosInstance.get(`packages/show-package-by-id/${userId}`, config);

      if (response.data && response.data.cards) {
        setMemberCards(response.data.cards);
      }
    } catch (error) {
      console.error('Error fetching member cards:', error);
    }
  }, []);

  useEffect(() => {
    fetchMemberCards();
  }, [fetchMemberCards]);

  // Fetch selected design data when design ID changes
  useEffect(() => {
    const fetchSelectedDesign = async () => {
      if (lazyParams?.designID) {
        try {
          const response = await axiosInstance.get(`/designs/${lazyParams.designID}`);
          if (response.data && response.data.data) {
            // Design data fetched successfully, but we don't need to store it
            console.log("Design data fetched:", response.data.data);
          }
        } catch (error) {
          console.error('Error fetching selected design:', error);
        }
      }
    };

    fetchSelectedDesign();
  }, [lazyParams?.designID]);

  useEffect(() => {
    const fetchGroupDesigns = async () => {
      if (!groupID) return;
      try {
        const token = localStorage.getItem('token');
        const response = await axiosInstance.get(`/groups-designs?group_id=${groupID}`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        let designs = [];
        if (Array.isArray(response.data.data)) {
          designs = response.data.data.map(item => item.design || item);
        } else if (Array.isArray(response.data)) {
          designs = response.data.map(item => item.design || item);
        }
        setGroupDesigns(designs.filter(Boolean));
      } catch (error) {
        setGroupDesigns([]);
      }
    };
    fetchGroupDesigns();
  }, [groupID]);

  // Close mobile action menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (mobileActionMenuOpen && !event.target.closest('.mobile-action-menu') && !event.target.closest('[data-mobile-menu-trigger]')) {
        setMobileActionMenuOpen(null);
      }
    };

    if (mobileActionMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('touchstart', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
        document.removeEventListener('touchstart', handleClickOutside);
      };
    }
  }, [mobileActionMenuOpen]);

  // Handle page exit warning
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (Object.keys(pendingCardAssignments).length > 0) {
        e.preventDefault();
        e.returnValue = 'You have unsaved card assignments. Are you sure you want to leave?';
        return 'You have unsaved card assignments. Are you sure you want to leave?';
      }
    };

    // Add event listeners for various navigation methods
    window.addEventListener('beforeunload', handleBeforeUnload);

    // Intercept all link clicks
    const handleLinkClick = (e) => {
      if (Object.keys(pendingCardAssignments).length > 0) {
        const target = e.target.closest('a');
        if (target && target.href) {
          e.preventDefault();
          e.stopPropagation();
          setShowExitWarningModal(true);
          setPendingNavigation(() => () => {
            window.location.href = target.href;
          });
          return false;
        }
      }
    };

    // Intercept browser back/forward buttons
    const handlePopState = (e) => {
      if (Object.keys(pendingCardAssignments).length > 0) {
        e.preventDefault();
        setShowExitWarningModal(true);
        // Push current state back to prevent navigation
        window.history.pushState(null, '', window.location.href);
        setPendingNavigation(() => () => window.history.back());
        return false;
      }
    };

    document.addEventListener('click', handleLinkClick, true);
    window.addEventListener('popstate', handlePopState);

    // Push a state to detect back button
    if (Object.keys(pendingCardAssignments).length > 0) {
      window.history.pushState(null, '', window.location.href);
    }

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('click', handleLinkClick, true);
      window.removeEventListener('popstate', handlePopState);
    };
  }, [pendingCardAssignments]);

  // Handle exit warning modal actions
  const handleConfirmExit = () => {
    setPendingCardAssignments({});
    setShowExitWarningModal(false);
    if (pendingNavigation) {
      pendingNavigation();
    }
  };

  const handleCancelExit = () => {
    setShowExitWarningModal(false);
    setPendingNavigation(null);
  };

  const handleSaveAndExit = () => {
    setShowExitWarningModal(false);
    handleEditClick(); // Open the edit modal to save changes
  };

const getAvailableCards = (currentUserId = null) => {
  // First check if we have the current group data with card_type_id
  if (!currentGroupData || !currentGroupData.data || !currentGroupData.data.card_type_id) {
    return [];
  }

  const groupCardTypeId = currentGroupData.data.card_type_id;

  return memberCards
    .filter(card => {
      // Filter cards by matching the group's card type
      const matchesCardType = card.type === groupCardTypeId ||
                             (card.card_type && card.card_type.id === groupCardTypeId);

      // Filter out cards that are already assigned to any member in this group
      const isAssigned = data.some(member =>
        member.cards?.some(memberCard => memberCard.number === card.number)
      );

      // Filter out cards that have pending assignments to OTHER users (not the current user)
      const hasPendingAssignmentToOthers = Object.entries(pendingCardAssignments).some(
        ([userId, assignment]) =>
          assignment.cardNumber === card.number &&
          userId !== currentUserId?.toString()
      );

      // Return only cards that match the group's card type AND are not already assigned AND don't have pending assignments to others
      return matchesCardType && !isAssigned && !hasPendingAssignmentToOthers;
    })
    .map(card => ({
      label: `${card.number} (${card.card_type?.type_of_connection || 'N/A'})`,
      value: card.number,
      cardData: card
    }));
};

// Add this new function to check card assignment status
const checkCardAssignmentStatus = async (userId) => {
  try {
    // Skip invalid user IDs
    if (!userId || userId === null || userId === undefined) {
      console.warn("Skipping card assignment check for invalid user ID:", userId);
      return { status: 'unassigned', card_number: null };
    }

    const token = localStorage.getItem('token');
    const response = await axiosInstance.get('cards_assign_status', {
      headers: {
        Authorization: `Bearer ${token}`
      },
      params: {
        group_id: groupID,
        user_id: userId
      }
    });

    console.log('Card assignment status API response for user', userId, ':', response.data);
    return response.data;
  } catch (error) {
    console.error('Error checking card assignment status for user', userId, ':', error);
    // Return a default status instead of null to prevent UI issues
    return { status: 'unassigned', card_number: null };
  }
};

const handleUnassignCard = (memberId, cardNumber) => {
  console.log("handleUnassignCard called with:", { memberId, cardNumber });

  // Check if this is a pending assignment
  const hasPendingAssignment = pendingCardAssignments[memberId];

  if (hasPendingAssignment) {
    // Remove from pending assignments (local state only)
    setPendingCardAssignments(prev => {
      const newAssignments = { ...prev };
      delete newAssignments[memberId];
      console.log("Removed pending assignment for user:", memberId);
      return newAssignments;
    });

    toastRef.current.show({
      severity: 'info',
      summary: 'Pending Assignment Cancelled',
      detail: 'Pending card assignment has been cancelled',
      life: 3000
    });
  } else {
    // This is an already assigned card - add to pending assignments with 'unassign' action
    setPendingCardAssignments(prev => ({
      ...prev,
      [memberId]: {
        cardNumber: cardNumber,
        action: 'unassign'
      }
    }));

    console.log("Added unassign action for user:", memberId, "card:", cardNumber);

    toastRef.current.show({
      severity: 'info',
      summary: 'Card Unassignment Pending',
      detail: 'Card will be unlinked when you save group changes',
      life: 3000
    });
  }
};

// Add bulk unassign handler
const handleBulkUnassign = async () => {
  if (selectedUnassignUsers.length === 0) {
    toastRef.current.show({
      severity: 'warn',
      summary: 'Warning',
      detail: 'Please select users to unassign',
      life: 3000
    });
    return;
  }

  const newAssignments = {};
  let unassignCount = 0;

  // Check card assignment status for each selected user
  for (const user of selectedUnassignUsers) {
    try {
      const status = await checkCardAssignmentStatus(user.id);
      if (status && status.status === 'assigned' && status.card && status.card.number) {
        newAssignments[user.id] = {
          cardNumber: status.card.number,
          action: 'unassign'
        };
        unassignCount++;
      }
    } catch (error) {
      console.error(`Error checking card status for user ${user.id}:`, error);
    }
  }

  if (unassignCount === 0) {
    toastRef.current.show({
      severity: 'warn',
      summary: 'Warning',
      detail: 'Selected users do not have assigned cards',
      life: 3000
    });
    return;
  }

  setPendingCardAssignments(prev => ({ ...prev, ...newAssignments }));
  setSelectedUnassignUsers([]); // Clear selection after adding to pending

  toastRef.current.show({
    severity: 'info',
    summary: 'Bulk Unassign Pending',
    detail: `${unassignCount} users will be unassigned when you save group changes`,
    life: 3000
  });
};

// Check if any selected users have assigned cards in the current group
const hasAssignedUsers = selectedUnassignUsers.some(user => {
  // Check if user has cards assigned in the current group using the tracked state
  return usersWithAssignedCards.has(user.id);
});

const handleAssignCard = (memberId, cardNumber) => {
  // Check if user already has a pending assignment
  if (pendingCardAssignments[memberId]) {
    toastRef.current.show({
      severity: 'warn',
      summary: 'Warning',
      detail: 'This user already has a pending card assignment',
      life: 3000
    });
    return;
  }

  // Check if card is already assigned to another user in pending assignments
  const cardAlreadyAssigned = Object.entries(pendingCardAssignments).find(
    ([userId, assignment]) =>
      assignment.cardNumber === cardNumber && userId !== memberId.toString()
  );

  if (cardAlreadyAssigned) {
    const [assignedUserId] = cardAlreadyAssigned;
    const assignedUser = data.find(user => user.id.toString() === assignedUserId);
    toastRef.current.show({
      severity: 'warn',
      summary: 'Warning',
      detail: `This card is already pending assignment to ${assignedUser?.name || 'another user'}`,
      life: 3000
    });
    return;
  }

  setPendingCardAssignments(prev => ({
    ...prev,
    [memberId]: {
      cardNumber: cardNumber,
      action: 'assign',
      group_id: groupID 
    }
  }));

  toastRef.current.show({
    severity: 'info',
    summary: 'Card Assigned',
    detail: 'Card will be assigned when you save group changes',
    life: 3000
  });
};




  const fetchCurrentGroupData = async (groupId) => {
    try {
      // Use axiosInstance instead of fetch for consistency
      const response = await axiosInstance.get(`groups/${groupId}`);

      const data = response.data;
      setCurrentGroupData(data);
      return data;
    } catch (error) {
      console.error("Error fetching group data:", error);

      // Show error message to user
      toastRef.current.show({
        severity: "error",
        summary: "Error",
        detail: "Failed to fetch group data. The server returned an error.",
        life: 5000,
      });

      // Return a minimal fallback object to prevent UI errors
      return {
        data: {
          id: groupId,
          title: "Group Information Unavailable",
          description: "Could not load group details. Please try again later.",
          status: "active",
          users: []
        }
      };
    }
  };

  const fetchUsersByGroup = useCallback(
    async (groupId) => {
      try {
        const backendUrl = import.meta.env.VITE_BACKEND_URL;
        const token = localStorage.getItem("token");

        const response = await fetch(
          `${backendUrl}/groups?users=true&groupID=${groupId}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              Accept: "application/json",
            },
            cache: "no-store",
          }
        );

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const newData = await response.json();

        const currentSelectedIds =
          selectedMembers.data?.map((member) => member.id) || [];

        // تحديث البيانات في السياق الجديد
        setLazyParams((prev) => ({
          ...prev,
          ...defaultTableConfig,
          ...usersTableConfig,
          groupID: groupId,
          data: newData.data || [],
          totalRecords: newData.data?.length || 0,
        }));

        if (newData.data && currentSelectedIds.length > 0) {
          const newSelectedMembers = newData.data.filter((member) =>
            currentSelectedIds.includes(member.id)
          );
          setSelectedMembers((prev) => ({
            ...prev,
            data: newSelectedMembers,
          }));
        }
      } catch (error) {
        console.error("Error fetching users by group:", error);
      }
    },
    [selectedMembers.data, setLazyParams, setSelectedMembers]
  );
  useEffect(() => {
    if (groupID) {
      Promise.all([
        fetchUsersByGroup(groupID),
        fetchCurrentGroupData(groupID),
      ]);
    }
  }, [groupID, fetchUsersByGroup]);

  const editMember = (data) => {
    setActionType("update");
    const updatedData = { ...data };
    delete updatedData.role;
    delete updatedData.group_permission;
    delete updatedData.design;
    setSelectedMember(updatedData);
    dialogHandler("addMember");
  };

  const selectAllHandler = (event) => {
    if (!event || !event.value) return;

    setSelectedMembers((prev) => ({
      ...prev,
      data: event.value,
      groupData: prev.groupData || {},
      action: event.value.length > 0 ? "update" : "create"
    }));
  }

  // Function to update local data with pending changes
  const updateLocalDataWithPendingChanges = useCallback(() => {
    if (Object.keys(pendingCardAssignments).length === 0) return;

    // Update the lazyParams data which is used by the DataTable
    setLazyParams(prevParams => {
      if (!prevParams.data) return prevParams;

      const updatedData = prevParams.data.map(user => {
        const pendingAssignment = pendingCardAssignments[user.id];
        if (!pendingAssignment) return user;

        // Create updated user object
        const updatedUser = { ...user };

        if (pendingAssignment.action === 'assign') {
          // Add the new card assignment
          updatedUser.card_number = pendingAssignment.cardNumber;
          updatedUser.card_assignment_status = 'assigned';
          // Add to cards array if it doesn't exist
          if (!updatedUser.cards) {
            updatedUser.cards = [];
          }
          // Add the card to the cards array if not already there
          const cardExists = updatedUser.cards.some(card => card.number === pendingAssignment.cardNumber);
          if (!cardExists) {
            updatedUser.cards.push({
              number: pendingAssignment.cardNumber,
              status: 'assigned'
            });
          }
        } else if (pendingAssignment.action === 'unassign') {
          // Remove the card assignment
          updatedUser.card_number = null;
          updatedUser.card_assignment_status = null;
          // Remove from cards array
          if (updatedUser.cards) {
            updatedUser.cards = updatedUser.cards.filter(card => card.number !== pendingAssignment.cardNumber);
          }
        }

        return updatedUser;
      });

      return {
        ...prevParams,
        data: updatedData,
        totalRecords: updatedData.length
      };
    });

    console.log("Local data updated with pending changes");
  }, [pendingCardAssignments, setLazyParams]);

  const handleSaveCardChanges = async () => {
    try {
      // Update local data first
      updateLocalDataWithPendingChanges();

      // Save card changes
      const savedPendingAssignments = { ...pendingCardAssignments };
      const savedSelectedDesignId = lazyParams?.designID;

      // Update group data first
      if (currentGroupData?.data) {
        const groupDetails = currentGroupData.data;
        const payload = {
          title: groupDetails.title,
          description: groupDetails.description,
          status: groupDetails.status,
          card_type_id: groupDetails.card_type_id,
          group_type: groupDetails.group_type,
          print_status: groupDetails.print_status,
          parent_group_id: groupDetails.parent_group_id,
          user_ids: data.map(user => user.id),
          card_assignments: Object.fromEntries(
            Object.entries(savedPendingAssignments).map(([userId, assignment]) => [
              userId,
              {
                ...assignment,
                group_id: assignment.group_id || groupID // تأكد أن group_id موجود
              }
            ])
          )
        };

        console.log("Saving group changes with payload:", payload);
        const response = await axiosInstance.put(`/groups/${groupDetails.id}`, payload);
        console.log("Group update response:", response.data);

        // Update current group data with the response
        setCurrentGroupData(prev => ({
          ...prev,
          data: {
            ...prev.data,
            ...response.data.data
          }
        }));
      }

      // Update data from API
      if (groupID) {
        await fetchUsersByGroup(groupID);
        // Refresh current group data
        const updatedGroupData = await fetchCurrentGroupData(groupID);
        setCurrentGroupData(updatedGroupData);
        // Refresh member cards to update available cards list
        await fetchMemberCards();
        // Update users with assigned cards after data refresh
        await updateUsersWithAssignedCards();
      }

      // Check for new card assignments
      const newlyAssignedUserIds = Object.entries(savedPendingAssignments)
        .filter(([, assignment]) => assignment.action === 'assign')
        .map(([userId]) => parseInt(userId));
      let designIdToUse = savedSelectedDesignId;

      // استخدام التصاميم المحددة للمجموعة من groupDesigns
      let availableDesigns = groupDesigns || [];
      console.log("groupDesigns for this group:", availableDesigns);

      if (!designIdToUse && availableDesigns.length > 0) {
        designIdToUse = availableDesigns[0].id;
      }
      console.log("designIdToUse", designIdToUse);

      if (newlyAssignedUserIds.length > 0 && designIdToUse) {
        try {
          const payload = {
            design_id: designIdToUse,
            user_ids: newlyAssignedUserIds,
            group_ids: [groupID] // Send as array for backward compatibility
          };
          console.log('🚀 generate-images-for-new-users payload:', payload);
          const response = await axiosInstance.post('/designs/generate-images-for-new-users', payload);

          if (response.data.user_count > 0) {
            console.log("Setting currentDesignId to", designIdToUse);
            setCurrentDesignId(designIdToUse);
            if (response.data.batch_id) {
              localStorage.setItem(`batch_${designIdToUse}`, response.data.batch_id);
              console.log("Stored batch_id:", response.data.batch_id, "for design:", designIdToUse);
            }
            setShowImageGenerationModal(true);
          }
        } catch (error) {
          console.error("Error starting image generation:", error);
          if (toastRef?.current) {
            toastRef.current.show({
              severity: "error",
              summary: "Image Generation Error",
              detail: "Failed to start image generation for new users",
              life: 5000,
            });
          }
        }
      } else if (newlyAssignedUserIds.length > 0 && (!availableDesigns || availableDesigns.length === 0)) {
        // لا يوجد أي تصميم متاح لهذه المجموعة
        if (toastRef?.current) {
          toastRef.current.show({
            severity: "warn",
            summary: "No Designs Available",
            detail: "No designs are assigned to this group. Please assign designs to the group first.",
            life: 5000,
          });
        }
      }

      // Clear pending changes
      setPendingCardAssignments({});

      // Show success message if toast is available
      if (toastRef?.current) {
        toastRef.current.show({
          severity: "success",
          summary: "Success",
          detail: "Group has been updated successfully",
          life: 3000,
        });
      }
    } catch (error) {
      console.error("Error saving changes:", error);
      // Show error message if toast is available
      if (toastRef?.current) {
        toastRef.current.show({
          severity: "error",
          summary: "Error",
          detail: error.response?.data?.message || "Failed to save group changes",
          life: 3000,
        });
      }
    }
  };

  const handleEditClick = async () => {
    if (!groupID) return;

    try {
      let groupData = currentGroupData;
      if (!groupData) {
        groupData = await fetchCurrentGroupData(groupID);
      }

      if (!groupData) {
        toastRef.current.show({
          severity: "error",
          summary: "Error",
          detail: "Failed to fetch group data. Please try again later.",
          life: 5000,
        });
        return;
      }

      console.log("Editing group, data received:", groupData);

      const groupDetails = groupData.data;

      // Create a fallback object if the server returns incomplete data
      const fallbackGroup = {
        id: groupID,
        group_type: "",
        title: "",
        description: "",
        status: "active",
        print_status: "",
        parent_group_id: null,
        card_type_id: null,
        users: selectedMembers.data || [],
      };

      // Use the server data if available, otherwise use fallback values
      const groupToEdit = {
        id: groupDetails?.id || fallbackGroup.id,
        group_type: groupDetails?.group_type || fallbackGroup.group_type,
        title: groupDetails?.title || fallbackGroup.title,
        description: groupDetails?.description || fallbackGroup.description,
        status: groupDetails?.status || fallbackGroup.status,
        print_status: groupDetails?.print_status || fallbackGroup.print_status,
        parent_group_id: groupDetails?.parent_group_id || fallbackGroup.parent_group_id,
        card_type_id: groupDetails?.card_type_id || fallbackGroup.card_type_id,
        users: selectedMembers.data || (groupDetails?.users || fallbackGroup.users),
        pendingCardAssignments: pendingCardAssignments, // Pass pending assignments
      };

      console.log("Setting group to edit:", groupToEdit);
      setGroupBeingEdited(groupToEdit);
      setIsEditGroupModalOpen(true);
    } catch (error) {
      console.error("Error in handleEditClick:", error);
      toastRef.current.show({
        severity: "error",
        summary: "Error",
        detail: "An error occurred while preparing to edit the group. Please try again later.",
        life: 5000,
      });
    }
  };



  // Add new function to handle group update success from GroupForm
  const handleGroupUpdateSuccess = async (responseData) => {
    try {
      console.log('Group update success, response data:', responseData);

      // Clear pending card assignments since the group was successfully updated
      setPendingCardAssignments({});

      // Refresh all data to reflect the updated group and members
      if (groupID) {
        await Promise.all([
          fetchUsersByGroup(groupID),
          fetchCurrentGroupData(groupID),
          fetchMemberCards()
        ]);

        // Update users with assigned cards after data refresh
        await updateUsersWithAssignedCards();
      }

      // Show success message (GroupForm already shows its own success message, so we don't need another one)
      console.log('Group data refreshed successfully');

    } catch (error) {
      console.error('Error refreshing data after group update:', error);
      toastRef.current.show({
        severity: 'warn',
        summary: 'Warning',
        detail: 'Group updated successfully, but failed to refresh data. Please refresh the page.',
        life: 5000,
      });
    }
  };

  // Card Status Indicator Component for Mobile View
  const CardStatusIndicator = ({ memberId }) => {
    const [cardStatus, setCardStatus] = useState(null);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
      const fetchCardStatus = async () => {
        setIsLoading(true);
        try {
          const status = await checkCardAssignmentStatus(memberId);
          setCardStatus(status);
        } catch (error) {
          console.error('Error fetching card status:', error);
          setCardStatus({ status: 'unassigned' });
        }
        setIsLoading(false);
      };

      fetchCardStatus();
    }, [memberId, pendingCardAssignments]);

    if (isLoading) {
      return <i className="pi pi-spin pi-spinner text-blue-500 text-xs"></i>;
    }

    const pendingAssignment = pendingCardAssignments[memberId];
    const hasPendingAssignment = pendingAssignment && pendingAssignment.action === 'assign';
    const hasPendingUnassignment = pendingAssignment && pendingAssignment.action === 'unassign';

    if (hasPendingAssignment) {
      return (
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
          <span className="text-xs font-medium text-yellow-700 dark:text-yellow-300">Pending Assignment</span>
        </div>
      );
    }

    if (hasPendingUnassignment) {
      return (
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
          <span className="text-xs font-medium text-red-700 dark:text-red-300">Pending Unassignment</span>
        </div>
      );
    }

    if (cardStatus?.status === 'assigned') {
      return (
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span className="text-xs font-medium text-green-700 dark:text-green-300">
            Card: {cardStatus.card?.number?.slice(-4) || 'N/A'}
          </span>
        </div>
      );
    }

    return (
      <div className="flex items-center gap-1">
        <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
        <span className="text-xs font-medium text-gray-600 dark:text-gray-400">No Card</span>
      </div>
    );
  };

  // Mobile action menu component
  const MobileActionMenu = ({ member, isOpen, onClose }) => {
    const [assignmentStatus, setAssignmentStatus] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [availableCards, setAvailableCards] = useState([]);

    useEffect(() => {
      if (isOpen && member) {
        const fetchData = async () => {
          setIsLoading(true);
          try {
            const status = await checkCardAssignmentStatus(member.id);
            console.log('Mobile Action Menu - Card assignment status for member', member.id, ':', status);
            setAssignmentStatus(status);
            const cards = getAvailableCards(member.id);
            console.log('Mobile Action Menu - Available cards for member', member.id, ':', cards);
            setAvailableCards(cards);
          } catch (error) {
            console.error('Error fetching card data:', error);
          }
          setIsLoading(false);
        };
        fetchData();
      }
    }, [isOpen, member, memberCards, pendingCardAssignments]);

    if (!isOpen || !member) return null;

    const pendingAssignment = pendingCardAssignments[member.id];
    const hasPendingAssignment = pendingAssignment && pendingAssignment.action === 'assign';
    const hasPendingUnassignment = pendingAssignment && pendingAssignment.action === 'unassign';

    console.log('Mobile Action Menu - Member:', member.id, 'Assignment Status:', assignmentStatus);
    console.log('Mobile Action Menu - Pending Assignment:', pendingAssignment);
    console.log('Mobile Action Menu - Has Pending Assignment:', hasPendingAssignment);
    console.log('Mobile Action Menu - Has Pending Unassignment:', hasPendingUnassignment);

    return createPortal(
      <div
        className="fixed inset-0 bg-black bg-opacity-60 z-[9999] flex items-center justify-center"
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          backdropFilter: 'blur(2px)'
        }}
        onClick={onClose}
      >
        <div
          className="mobile-action-menu bg-white dark:bg-gray-800 rounded-lg p-4 m-4 w-full max-w-sm relative shadow-2xl max-h-[80vh] overflow-y-auto"
          style={{
            zIndex: 10000,
            backgroundColor: '#ffffff',
            boxShadow: '0 10px 25px rgba(0, 0, 0, 0.5)',
            border: '1px solid rgba(0, 0, 0, 0.1)'
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center mb-4 border-b border-gray-200 dark:border-gray-700 pb-3">
            <img
              src={member.image || profile_img}
              alt="Profile"
              className="w-10 h-10 rounded-full object-cover mr-3"
            />
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-gray-100">{member.name}</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">{member.position || 'N/A'}</p>
            </div>
          </div>

          {/* Card Assignment Section */}
          <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">Card Assignment</h4>
            {isLoading ? (
              <div className="flex justify-center py-2">
                <i className="pi pi-spin pi-spinner text-blue-500"></i>
              </div>
            ) : hasPendingAssignment ? (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-2">
                <div className="flex justify-between items-center mb-1">
                  <span className="text-xs font-semibold text-yellow-600">PENDING ASSIGNMENT</span>
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      console.log('Cancelling pending assignment for member:', member.id);
                      handleUnassignCard(member.id, pendingAssignment.cardNumber);
                      onClose();
                    }}
                    className="text-red-500 hover:text-red-700 p-1 rounded transition-colors"
                    title="Cancel Assignment"
                  >
                    <i className="pi pi-times"></i>
                  </button>
                </div>
                <div className="font-mono text-sm font-bold text-yellow-800">
                  {pendingAssignment.cardNumber}
                </div>
              </div>
            ) : hasPendingUnassignment ? (
              <div className="bg-red-50 border border-red-200 rounded-lg p-2">
                <div className="flex justify-between items-center mb-1">
                  <span className="text-xs font-semibold text-red-600">PENDING UNASSIGNMENT</span>
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      console.log('Cancelling pending unassignment for member:', member.id);
                      handleUnassignCard(member.id, pendingAssignment.cardNumber);
                      onClose();
                    }}
                    className="text-red-500 hover:text-red-700 p-1 rounded transition-colors"
                    title="Cancel Unassignment"
                  >
                    <i className="pi pi-times"></i>
                  </button>
                </div>
                <div className="font-mono text-sm font-bold text-red-800">
                  {pendingAssignment.cardNumber}
                </div>
              </div>
            ) : (assignmentStatus?.status === 'assigned' || assignmentStatus?.card?.number) ? (
              <div className="space-y-2">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-2">
                  <span className="text-xs font-semibold text-blue-600">ASSIGNED CARD</span>
                  <div className="font-mono text-sm font-bold text-blue-800">
                    {assignmentStatus.card?.number || assignmentStatus.card_number || 'N/A'}
                  </div>
                </div>
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    const cardNumber = assignmentStatus.card?.number || assignmentStatus.card_number;
                    console.log('Unassigning card for member:', member.id, 'card:', cardNumber);
                    handleUnassignCard(member.id, cardNumber);
                    onClose();
                          }}
                          style={{
                              width: '100%',
                              backgroundColor: '#ef4444', // Tailwind's red-500
                              color: 'white',
                              paddingTop: '0.5rem',
                              paddingBottom: '0.5rem',
                              paddingLeft: '0.75rem',
                              paddingRight: '0.75rem',
                              borderRadius: '0.5rem',
                              fontSize: '0.875rem', // text-sm
                              fontWeight: 500, // font-medium
                              transition: 'background-color 150ms ease-in-out'
                            }}
                >
                  Unassign Card
                </button>
              </div>
            ) : availableCards.length > 0 ? (
              <div className="space-y-2">
                <p className="text-xs text-gray-600">{availableCards.length} cards available</p>
                <div className="max-h-32 overflow-y-auto space-y-1">
                  {availableCards.map((card) => (
                    <button
                      key={card.value}
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('Assigning card to member:', member.id, 'card:', card.value);
                        handleAssignCard(member.id, card.value);
                        onClose();
                      }}
                      className="w-full text-left p-2 bg-white border border-gray-200 rounded hover:bg-blue-50 hover:border-blue-300 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          {card.cardData?.card_type?.type_of_connection === 'NFC' ? (
                            <div className="text-blue-500 mr-2">
                              <svg className="w-3 h-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 4C7.58 4 4 7.58 4 12C4 16.42 7.58 20 12 20C16.42 20 20 16.42 20 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                                <path d="M12 8C9.79 8 8 9.79 8 12C8 14.21 9.79 16 12 16C14.21 16 16 14.21 16 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                              </svg>
                            </div>
                          ) : (
                            <div className="text-purple-500 mr-2">
                              <svg className="w-3 h-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M6 8L18 16L12 22V2L18 8L6 16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              </svg>
                            </div>
                          )}
                          <div>
                            <div className="text-xs font-medium">{card.label}</div>
                          </div>
                        </div>
                        <div className="font-mono text-xs bg-gray-100 px-1 py-0.5 rounded">
                          {card.value?.slice(-4)}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center p-2 border border-dashed border-gray-300 rounded-lg bg-gray-50">
                <i className="pi pi-info-circle text-gray-400 mb-1"></i>
                <p className="text-xs text-gray-600">No available cards</p>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="space-y-2 bg-gray-50 dark:bg-gray-700 p-2 rounded-lg">
            {/* Edit */}
            <button
              className="w-full flex items-center p-3 text-left bg-white dark:bg-gray-800 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg border border-gray-200 dark:border-gray-600 text-gray-900 dark:text-gray-100"
              onClick={() => {
                editMember(member);
                onClose();
              }}
            >
              <FiEdit className="mr-3 text-green-500 dark:text-green-400" size={18} />
              <span>Edit Member</span>
            </button>

            {/* Remove */}
            {member.id.toString() !== localStorage.getItem("user_id") && (
              <button
                className="w-full flex items-center p-3 text-left bg-white dark:bg-gray-800 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg border border-gray-200 dark:border-gray-600 text-red-600 dark:text-red-400"
                onClick={() => {
                  handleDeleteMemberClick(member, groupID);
                  onClose();
                }}
              >
                <TfiTrash className="mr-3" size={18} />
                <span>Remove from Group</span>
              </button>
            )}
          </div>

          <button
            className="w-full mt-4 p-2 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 rounded-lg text-center font-medium transition-colors text-gray-900 dark:text-gray-100"
            onClick={onClose}
          >
            Cancel
          </button>
        </div>
      </div>,
      document.body
    );
  };

  const handleDeleteMemberClick = (rowData, groupId) => {
    confirmDialog({
      message: `Are you sure you want to remove ${rowData.name || 'this member'} from the group?`,
      header: 'Remove Member Confirmation',
      icon: 'pi pi-exclamation-triangle',
      acceptClassName: 'p-button-danger',
      acceptLabel: 'Yes, Remove',
      rejectLabel: 'Cancel',
      accept: () => deleteRowHandler(rowData, groupId),
    });
  };

  const deleteRowHandler = async (rowData, groupId) => {
    console.log("Deleting row:", rowData + " groupId:", groupId);
    try {
      const backendUrl = import.meta.env.VITE_BACKEND_URL;
      const token = localStorage.getItem("token");

      const response = await fetch(`${backendUrl}/users/groups/remove-member-group`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          group_id: Number(groupId),
          user_id: rowData.id
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      // Update the UI after successful deletion
      setLazyParams((prev) => ({ ...prev, ...usersTableConfig }));
      setSelectedMembers((prev) => ({
        ...prev,
        data: prev.data?.filter((member) => member.id !== rowData.id) || [],
      }));

      // Refresh member cards to update available cards list
      await fetchMemberCards();

      toastRef.current.show({
        severity: "success",
        summary: "Success",
        detail: "Member removed from group successfully",
        life: 3000,
      });
    } catch (error) {
      console.error("Error removing member from group:", error);
      toastRef.current.show({
        severity: "error",
        summary: "Error",
        detail: "Failed to remove member from group",
        life: 3000,
      });
    }
  };



  const imageBodyTemplate = (rowData) => {
    if (rowData?.template_image_html) {
      // Use the template_image_html directly from the server
      // The server should already have replaced the image with the latest one
      const templateWithUserData = rowData.template_image_html;
      
      console.log('Using template from server:', templateWithUserData);
      console.log('Current user image:', rowData.image);

      return (
        <div
          className="rounded-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow duration-300"
          style={{
            width: '200px',
            height: '200px',
            position: 'relative'
          }}
          onClick={() => {
            setSelectedTemplate(templateWithUserData);
            setTemplateModalVisible(true);
          }}
        >
          <div
            style={{
              width: '100%',
              height: '100%',
              display: 'flex',
              justifyContent: 'flex-start',
              alignItems: 'center'
            }}
          >
            <div
              style={{
                transform: 'scale(0.32)',
                transformOrigin: 'left center',
                display: 'inline-block',
                border: '3px solid black',
                borderRadius: '8px',
                padding: '8px',
                backgroundColor: 'white'
              }}
            >
              {parse(templateWithUserData)}
            </div>
          </div>
          {/* Overlay with magnify icon */}
          <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 flex items-center justify-center transition-all duration-300">
            <div className="text-white dark:text-gray-200 opacity-0 hover:opacity-100 transform scale-90 hover:scale-100 transition-all duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
              </svg>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  const profileBodyTemplate = (rowData) => {
    return rowData?.image ? (
      <img loading="lazy" src={rowData?.image} width={100} alt="profile" />
    ) : (
      ""
    );
  };

  const statusBodyTemplate = (rowData) => {
    // Check if a design is selected
    if (!lazyParams?.designID) {
      return (
        <div className="flex items-center justify-center">
          <div className="bg-gradient-to-r from-blue-50 to-sky-50 dark:from-blue-900/20 dark:to-sky-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-3 shadow-sm max-w-[350px]">
            <div className="flex items-center gap-3">
              <div className="flex-shrink-0">
                <svg
                  className="w-5 h-5 text-blue-600 dark:text-blue-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div className="text-center">
                <div className="text-xs font-semibold text-blue-700 dark:text-blue-300 uppercase tracking-wide whitespace-nowrap">
                  Select design
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }

    // If design is selected, show the normal print status
    const status = rowData?.print_status?.toLowerCase() || "unprinted";
    console.log("Status:", rowData);
    return (
      <span
        className={`text-[white] rounded-[6px] font-bold text-sm py-2 px-3 capitalize ${statusStyles?.[status]}`}
      >
        {status}
      </span>
    );
  };

  const actionBodyTemplate = (rowData) => {
    const currentUserId = localStorage.getItem("user_id");
    return (
      <>
        <div className="d-inline-block text-nowrap">
          <Tooltip
            target={`.update-button-${rowData.id}`}
            showDelay={100}
            className="fs-8"
          />
          <button
            className={`btn btn-sm btn-icon update-button-${rowData.id} me-3 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400`}
            data-pr-position="bottom"
            data-pr-tooltip="Update"
            onClick={() => editMember(rowData)}
          >
            <FiEdit />
          </button>

          {rowData.id.toString() !== currentUserId && (
            <>
              <Tooltip
                target={`.delete-button-${rowData.id}`}
                showDelay={100}
                className="fs-8"
              />
              <button
                className={`btn btn-sm btn-icon delete-button-${rowData.id} text-gray-700 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400`}
                data-pr-position="bottom"
                data-pr-tooltip="Delete"
                onClick={() => handleDeleteMemberClick(rowData, groupID)}
              >
                <TfiTrash />
              </button>
            </>
          )}
        </div>
      </>
    );
  };

  const memberCount = data?.length || 0;

  return (
    <>
      {/* CSS مخصص لإصلاح مشكلة رأس الجدول في الوضع المظلم */}
      <style>{`
        .dark .p-datatable .p-datatable-header {
          background-color: rgb(31 41 55) !important;
          border-color: rgb(55 65 81) !important;
          color: rgb(229 231 235) !important;
        }
        
        .dark .p-datatable .p-datatable-thead > tr > th {
          background-color: rgb(31 41 55) !important;
          border-color: rgb(55 65 81) !important;
          color: rgb(229 231 235) !important;
          padding: 0.75rem !important;
        }
        
        .dark .p-datatable .p-datatable-thead > tr > th.p-selection-column {
          background-color: rgb(31 41 55) !important;
          border-color: rgb(55 65 81) !important;
        }
        
        .dark .p-datatable .p-datatable-thead > tr > th.p-sortable-column:hover {
          background-color: rgb(55 65 81) !important;
          color: rgb(243 244 246) !important;
        }
        
        .dark .p-datatable .p-datatable-thead > tr > th.p-sortable-column:focus {
          background-color: rgb(55 65 81) !important;
          color: rgb(243 244 246) !important;
        }
        
        .dark .p-datatable .p-datatable-thead > tr > th.p-sortable-column.p-highlight {
          background-color: rgb(55 65 81) !important;
          color: rgb(243 244 246) !important;
        }
        
        .dark .p-datatable .p-datatable-thead > tr > th .p-column-header-content {
          color: rgb(229 231 235) !important;
        }
        
        .dark .p-datatable .p-datatable-thead > tr > th .p-sortable-column-icon {
          color: rgb(229 231 235) !important;
        }
        
        .dark .p-datatable .p-datatable-thead > tr > th .p-checkbox {
          background-color: rgb(55 65 81) !important;
          border-color: rgb(75 85 99) !important;
        }
        
        .dark .p-datatable .p-datatable-thead > tr > th .p-checkbox .p-checkbox-box {
          background-color: rgb(55 65 81) !important;
          border-color: rgb(75 85 99) !important;
        }
        
        .dark .p-datatable .p-datatable-thead > tr > th .p-checkbox .p-checkbox-box.p-highlight {
          background-color: rgb(59 130 246) !important;
          border-color: rgb(59 130 246) !important;
        }
        
        /* إصلاح المسافات بين الأعمدة */
        .dark .p-datatable .p-datatable-thead > tr > th:not(:last-child) {
          border-right: 1px solid rgb(55 65 81) !important;
        }
        
        /* إصلاح خلفية الجدول بالكامل */
        .dark .p-datatable {
          background-color: rgb(31 41 55) !important;
          border-color: rgb(55 65 81) !important;
        }
        
        .dark .p-datatable .p-datatable-tbody > tr {
          background-color: rgb(31 41 55) !important;
          border-color: rgb(55 65 81) !important;
        }
        
        .dark .p-datatable .p-datatable-tbody > tr > td {
          background-color: rgb(31 41 55) !important;
          border-color: rgb(55 65 81) !important;
          color: rgb(229 231 235) !important;
        }
        
        .dark .p-datatable .p-datatable-tbody > tr:hover > td {
          background-color: rgb(55 65 81) !important;
        }
        
        /* إصلاح الـ tr الرئيسي للجدول */
        .dark .p-datatable .p-datatable-thead > tr {
          background-color: rgb(31 41 55) !important;
          border-color: rgb(55 65 81) !important;
        }
        
        /* إصلاح إضافي للـ table wrapper */
        .dark .p-datatable .p-datatable-wrapper {
          background-color: rgb(31 41 55) !important;
        }
        
        .dark .p-datatable .p-datatable-table {
          background-color: rgb(31 41 55) !important;
        }
        
        /* إصلاح الـ table header بشكل أكثر تحديداً */
        .dark .p-datatable .p-datatable-header {
          background-color: rgb(31 41 55) !important;
          border-bottom: 1px solid rgb(55 65 81) !important;
        }
        
        /* إصلاح شامل لجميع عناصر الجدول */
        .dark .p-datatable * {
          border-color: rgb(55 65 81) !important;
        }
        
        .dark .p-datatable .p-datatable-thead * {
          background-color: rgb(31 41 55) !important;
        }
        
        /* ضبط المسافات المناسبة في الجدول */
        .dark .p-datatable .p-datatable-thead > tr > th {
          padding: 8px 12px !important;
          margin: 0 !important;
        }
        
        .dark .p-datatable .p-datatable-tbody > tr > td {
          padding: 8px 12px !important;
          margin: 0 !important;
        }
        
        .dark .p-datatable {
          border-collapse: collapse !important;
        }
        
        .dark .p-datatable .p-datatable-table {
          border-collapse: collapse !important;
        }
        
        /* إزالة الحدود بين الأعمدة للحصول على مظهر متصل */
        .dark .p-datatable .p-datatable-thead > tr > th + th {
          border-left: none !important;
        }
        
        .dark .p-datatable .p-datatable-tbody > tr > td + td {
          border-left: none !important;
        }
        
        /* محاذاة النصوص في الأعمدة */
        .dark .p-datatable .p-datatable-thead > tr > th {
          text-align: center !important;
          vertical-align: middle !important;
        }
        
        .dark .p-datatable .p-datatable-tbody > tr > td {
          text-align: center !important;
          vertical-align: middle !important;
        }
        
        /* محاذاة خاصة للأعمدة النصية */
        .dark .p-datatable .p-datatable-tbody > tr > td:first-child {
          text-align: left !important; /* الاسم في اليسار */
        }
        
        .dark .p-datatable .p-datatable-tbody > tr > td:nth-child(2) {
          text-align: left !important; /* البريد في اليسار */
        }
        
        .dark .p-datatable .p-datatable-tbody > tr > td:nth-child(3) {
          text-align: center !important; /* القسم في الوسط */
        }
        
        .dark .p-datatable .p-datatable-tbody > tr > td:nth-child(4) {
          text-align: center !important; /* تفاصيل البطاقة في الوسط */
        }
        
        .dark .p-datatable .p-datatable-tbody > tr > td:nth-child(5) {
          text-align: center !important; /* حالة الطباعة في الوسط */
        }
        
        .dark .p-datatable .p-datatable-tbody > tr > td:last-child {
          text-align: center !important; /* الإجراءات في الوسط */
        }
        
        /* ضبط عرض الأعمدة لضمان التوازي */
        .dark .p-datatable .p-datatable-table {
          table-layout: auto !important;
          width: 100% !important;
        }
        
        /* ضبط عرض الأعمدة بناءً على الـ style المحدد */
        .dark .p-datatable .p-datatable-thead > tr > th:nth-child(1) {
          width: 3em !important; /* Checkbox */
        }
        
        .dark .p-datatable .p-datatable-thead > tr > th:nth-child(2) {
          width: 275px !important; /* Template */
        }
        
        .dark .p-datatable .p-datatable-thead > tr > th:nth-child(3) {
          width: 200px !important; /* Name */
        }
        
        .dark .p-datatable .p-datatable-thead > tr > th:nth-child(4) {
          width: 130px !important; /* Type */
        }
        
        .dark .p-datatable .p-datatable-thead > tr > th:nth-child(5) {
          width: 130px !important; /* Position */
        }
        
        .dark .p-datatable .p-datatable-thead > tr > th:nth-child(6) {
          width: 130px !important; /* Department */
        }
        
        .dark .p-datatable .p-datatable-thead > tr > th:nth-child(7) {
          width: 260px !important; /* Card Details */
        }
        
        .dark .p-datatable .p-datatable-thead > tr > th:nth-child(8) {
          width: 260px !important; /* Card Assignment */
        }
        
        .dark .p-datatable .p-datatable-thead > tr > th:nth-child(9) {
          width: 220px !important; /* Status - زيادة العرض */
        }
        
        .dark .p-datatable .p-datatable-thead > tr > th:nth-child(10) {
          width: 220px !important; /* Actions */
        }
        
        /* ضبط محتوى الخلايا لضمان التوازي */
        .dark .p-datatable .p-datatable-tbody > tr > td {
          white-space: nowrap !important;
          overflow: hidden !important;
          text-overflow: ellipsis !important;
        }
        
        /* محاذاة خاصة لمحتوى Card Assignment وما بعدها */
        .dark .p-datatable .p-datatable-tbody > tr > td:nth-child(4) > div,
        .dark .p-datatable .p-datatable-tbody > tr > td:nth-child(5) > div,
        .dark .p-datatable .p-datatable-tbody > tr > td:nth-child(6) > div {
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          width: 100% !important;
          min-height: 40px !important;
        }
        
        /* ضبط الأزرار في عمود الإجراءات */
        .dark .p-datatable .p-datatable-tbody > tr > td:last-child > div {
          gap: 6px !important;
          flex-wrap: nowrap !important;
          justify-content: center !important;
          align-items: center !important;
          padding: 4px !important;
        }
        
        /* ضبط أزرار الـ Actions */
        .dark .p-datatable .p-datatable-tbody > tr > td:last-child .p-button {
          padding: 6px 8px !important;
          font-size: 11px !important;
          min-width: 32px !important;
          height: 28px !important;
          margin: 0 !important;
        }
        
        /* ضبط أيقونات الأزرار */
        .dark .p-datatable .p-datatable-tbody > tr > td:last-child .p-button .p-button-icon {
          font-size: 11px !important;
        }
        
        /* ضبط أزرار الـ Actions بدون نص */
        .dark .p-datatable .p-datatable-tbody > tr > td:last-child .p-button.p-button-icon-only {
          width: 28px !important;
          height: 28px !important;
          padding: 4px !important;
        }
        
        /* ضبط Badge في Card Assignment */
        .dark .p-datatable .p-datatable-tbody > tr > td:nth-child(4) .p-badge {
          white-space: nowrap !important;
          max-width: 100% !important;
          font-size: 11px !important;
        }
        
        /* ضبط Badge في Print Status */
        .dark .p-datatable .p-datatable-tbody > tr > td:nth-child(5) .p-badge {
          white-space: nowrap !important;
          max-width: 100% !important;
          font-size: 11px !important;
        }
        
        /* Animations for Card Header */
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-10px); }
        }
        
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        
        @keyframes glow {
          0%, 100% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.2); }
          50% { box-shadow: 0 0 20px rgba(255, 255, 255, 0.4), 0 0 30px rgba(255, 255, 255, 0.2); }
        }
        
        @keyframes textGlow {
          0%, 100% { 
            text-shadow: 0 0 5px rgba(255, 255, 255, 0.3), 0 0 10px rgba(59, 130, 246, 0.2);
            color: #ffffff;
          }
          50% { 
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.6), 0 0 15px rgba(59, 130, 246, 0.4), 0 0 20px rgba(147, 197, 253, 0.3);
            color: #e0e7ff;
          }
        }
        
        .card-header-shimmer {
          animation: shimmer 2s infinite;
        }
        
        .card-glow {
          animation: glow 3s ease-in-out infinite;
        }
        
        .text-glow {
          animation: textGlow 3s ease-in-out infinite;
        }
        
        .typing-effect {
          display: inline-block;
          overflow: hidden;
          white-space: nowrap;
          border-right: 2px solid rgba(255, 255, 255, 0.8);
          width: 0;
          animation: typing 3s steps(20, end) forwards, blink-caret 0.75s step-end 3s, hide-cursor 0s 3s forwards;
          max-width: 100%;
        }
        
        @keyframes typing {
          from { width: 0; }
          to { width: 100%; }
        }
        
        @keyframes blink-caret {
          0%, 50% { border-color: rgba(255, 255, 255, 0.8); }
          25%, 75% { border-color: transparent; }
        }
        
        @keyframes hide-cursor {
          to { border-right: none; }
        }
        
        .typing-effect-simple {
          display: inline-block;
          overflow: hidden;
          white-space: nowrap;
          width: 0;
          animation: typing 2s steps(15, end) forwards;
          max-width: 100%;
        }
      `}</style>
      
      <div className="w-full  h-full px-[20px]">
      {/* Professional Header Section */}
      <div className="w-full mb-2">
    <div className="w-full">
          {/* Header Background with Light Gradient */}
          <div className="relative overflow-hidden rounded-lg bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 dark:from-gray-800 dark:via-blue-900/20 dark:to-indigo-900/20 shadow-md border border-gray-200 dark:border-gray-700 groups-header-container">
            {/* Animated Background Pattern */}
            <div className="absolute inset-0 opacity-5">
              <div className="absolute inset-0" style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                backgroundSize: '60px 60px'
              }}></div>
            </div>
            
            {/* Floating Elements */}
            <div className="absolute top-4 right-4 w-20 h-20 bg-blue-200/30 dark:bg-blue-400/20 rounded-full blur-xl"></div>
            <div className="absolute bottom-4 left-4 w-16 h-16 bg-purple-200/30 dark:bg-purple-400/20 rounded-full blur-xl"></div>
                
            {/* Header Content */}
            <div className="relative z-10 p-2">
              {/* Title Section - Improved Mobile Layout */}
              <div className={`${isMobile ? 'flex flex-col space-y-1 mb-2' : 'flex flex-row items-center justify-between mb-2'}`}>
                <div className={`${isMobile ? 'w-full' : 'mb-0'}`}>
                  <div className={`flex items-center gap-3 ${isMobile ? 'justify-center' : ''}`}>
                    <div className="relative">
                      <div className={`${isMobile ? 'w-6 h-6' : 'w-8 h-8'} bg-gradient-to-br from-blue-500 to-indigo-600 rounded-md flex items-center justify-center shadow-md`}>
                        <FaUsers className={`text-white ${isMobile ? 'text-xs' : 'text-sm'}`} />
                      </div>
                      <div className={`absolute -top-0.5 -right-0.5 ${isMobile ? 'w-2.5 h-2.5' : 'w-3 h-3'} bg-green-500 rounded-full flex items-center justify-center border border-white shadow-sm`}>
                        <span className={`text-white ${isMobile ? 'text-xs' : 'text-xs'} font-bold`}>
                          {memberCount || 0}
                        </span>
                      </div>
                    </div>
                    <div className={`${isMobile ? 'text-center' : ''}`}>
                      <h1 className={`${isMobile ? 'text-sm' : 'text-base lg:text-lg'} font-bold text-gray-900 dark:text-gray-100 mb-0`}>
                        {currentGroupData?.data?.title ? `Group: ${currentGroupData.data.title}` : 'Group Members'}
                      </h1>
                      <p className={`text-gray-600 dark:text-gray-300 ${isMobile ? 'text-xs' : 'text-xs'}`}>
                        {isMobile ? 'Manage group members' : currentGroupData?.data?.description || 'Organize and manage your group members efficiently'}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Desktop Action Buttons */}
                  {!isMobile && (
                  <div className="flex gap-3">
                    {/* Bulk Unassign Button */}
                    {selectedUnassignUsers.length > 0 && hasAssignedUsers && (
                      <button
                        className="group relative px-6 py-3 bg-gradient-to-r from-red-500 to-red-600 text-white font-semibold rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 flex items-center gap-2 border border-red-400/30"
                        onClick={handleBulkUnassign}
                      >
                        <div className="relative">
                          <i className="pi pi-trash text-base group-hover:rotate-12 transition-transform duration-300" />
                        </div>
                        <span className="text-base">Unassign Selected ({selectedUnassignUsers.filter(user => 
                          user.cards && user.cards.length > 0
                        ).length})</span>
                        <div className="absolute inset-0 bg-gradient-to-r from-red-400/20 to-red-500/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      </button>
                    )}
                    {Object.keys(pendingCardAssignments).length > 0 && (
                      <button
                        className="group relative px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white font-semibold rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 flex items-center gap-2 border border-blue-400/30"
                        onClick={handleSaveCardChanges}
                      >
                        <div className="relative">
                          <i className="pi pi-save text-base group-hover:rotate-12 transition-transform duration-300" />
                        </div>
                        <span className="text-base">Save Changes ({Object.keys(pendingCardAssignments).length})</span>
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-blue-500/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      </button>
                    )}
                    <button
                      className="group relative px-6 py-3 bg-gradient-to-r from-emerald-500 to-teal-600 text-white font-semibold rounded-xl hover:from-emerald-600 hover:to-teal-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 flex items-center gap-2 border border-emerald-400/30"
                      onClick={handleEditClick}
                    >
                      <div className="relative">
                        <FiEdit className="text-base group-hover:rotate-12 transition-transform duration-300" />
                  </div>
                      <span className="text-base">Update Group</span>
                      <div className="absolute inset-0 bg-gradient-to-r from-emerald-400/20 to-teal-500/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </button>
                </div>
                )}
              </div>

              {/* Template Selection Section - Full Width */}
              <div className="mb-2">
                <div className="relative">
                <div className={`${isMobile ? 'mb-1' : ''}`}>
                  {isMobile && (
                      <label className="text-sm text-gray-700 dark:text-gray-300 block mb-1 font-medium">Select Template:</label>
                    )}
                    <div className="relative">
                      <div className={`absolute inset-y-0 left-0 ${isMobile ? 'pl-3' : 'pl-4'} flex items-center pointer-events-none z-10`}>
                        <div className={`${isMobile ? 'w-6 h-6' : 'w-8 h-8'} bg-gradient-to-br from-slate-700 via-slate-600 to-slate-800 rounded-xl flex items-center justify-center shadow-lg border border-slate-500/30 group-hover:scale-110 transition-all duration-300 hover:shadow-xl hover:from-slate-600 hover:via-slate-500 hover:to-slate-700`}>
                          <svg className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-white group-hover:scale-110 transition-transform duration-300 group-hover:rotate-12`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                          </svg>
                          <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </div>
                      </div>
                  <GroupFilter groupData={currentGroupData} designs={groupDesigns} />
                    </div>
                  </div>
                </div>
              </div>

              {/* Stats Section - Full Width */}
              <div className="mb-2">
                <div className={`grid grid-cols-3 ${isMobile ? 'gap-1' : 'gap-2'}`}>
                  {/* Total Members */}
                  <div className={`bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg ${isMobile ? 'p-1' : 'p-1.5'} border border-gray-200 dark:border-gray-600 shadow-sm hover:shadow-lg transition-all duration-300 group`}>
                    <div className="text-center">
                      <div className={`flex items-center justify-center ${isMobile ? 'gap-0.5 mb-0' : 'gap-1 mb-0.5'}`}>
                        <div className="relative group">
                          <BiGroup className={`text-blue-600 dark:text-blue-400 group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors duration-300 group-hover:scale-110 group-hover:rotate-6`} size={isMobile ? 14 : 20} />
                          <div className="absolute inset-0 bg-blue-100 dark:bg-blue-900/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                        </div>
                        <div className={`${isMobile ? 'text-sm' : 'text-lg'} font-bold text-gray-900 dark:text-gray-100 group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors duration-300`}>
                          {memberCount || 0}
                        </div>
                      </div>
                      <div className={`${isMobile ? 'text-xs' : 'text-xs'} text-gray-600 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300`}>
                        {isMobile ? 'Total' : 'Members'}
                      </div>
                    </div>
                  </div>

                  {/* Assigned Members */}
                  <div className={`bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg ${isMobile ? 'p-1' : 'p-1.5'} border border-gray-200 dark:border-gray-600 shadow-sm hover:shadow-lg transition-all duration-300 group`}>
                    <div className="text-center">
                      <div className={`flex items-center justify-center ${isMobile ? 'gap-0.5 mb-0' : 'gap-1 mb-0.5'}`}>
                        <div className="relative group">
                          <BsCheckCircle className={`text-green-600 dark:text-green-400 group-hover:text-green-700 dark:group-hover:text-green-300 transition-colors duration-300 group-hover:scale-110 group-hover:rotate-6`} size={isMobile ? 14 : 20} />
                          <div className="absolute inset-0 bg-green-100 dark:bg-green-900/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                        </div>
                        <div className={`${isMobile ? 'text-sm' : 'text-lg'} font-bold text-green-600 dark:text-green-400 group-hover:text-green-700 dark:group-hover:text-green-300 transition-colors duration-300`}>
                          {data?.filter(member => member.cards && member.cards.length > 0)?.length || 0}
                        </div>
                      </div>
                      <div className={`${isMobile ? 'text-xs' : 'text-xs'} text-gray-600 dark:text-gray-400 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-300`}>Assigned</div>
                    </div>
                  </div>

                  {/* Unassigned Members */}
                  <div className={`bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg ${isMobile ? 'p-1' : 'p-1.5'} border border-gray-200 dark:border-gray-600 shadow-sm hover:shadow-lg transition-all duration-300 group`}>
                    <div className="text-center">
                      <div className={`flex items-center justify-center ${isMobile ? 'gap-0.5 mb-0' : 'gap-1 mb-0.5'}`}>
                        <div className="relative group">
                          <BsXCircle className={`text-red-600 dark:text-red-400 group-hover:text-red-700 dark:group-hover:text-red-300 transition-colors duration-300 group-hover:scale-110 group-hover:rotate-6`} size={isMobile ? 14 : 20} />
                          <div className="absolute inset-0 bg-red-100 dark:bg-red-900/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                        </div>
                        <div className={`${isMobile ? 'text-sm' : 'text-lg'} font-bold text-red-600 dark:text-red-400 group-hover:text-red-700 dark:group-hover:text-red-300 transition-colors duration-300`}>
                          {data?.filter(member => !member.cards || member.cards.length === 0)?.length || 0}
                        </div>
                      </div>
                      <div className={`${isMobile ? 'text-xs' : 'text-xs'} text-gray-600 dark:text-gray-400 group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors duration-300`}>Unassigned</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Mobile Action Buttons */}
              {isMobile && (
                <div className="mt-2 flex flex-col gap-2">
                  {/* Bulk Unassign Button */}
                  {selectedUnassignUsers.length > 0 && hasAssignedUsers && (
                    <button
                      className="group relative px-6 py-3 bg-gradient-to-r from-red-500 to-red-600 text-white font-semibold rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 flex items-center justify-center gap-2 border border-red-400/30"
                      onClick={handleBulkUnassign}
                    >
                      <div className="relative">
                        <i className="pi pi-trash text-base group-hover:rotate-12 transition-transform duration-300" />
                      </div>
                      <span className="text-base">Unassign Selected ({selectedUnassignUsers.filter(user => 
                        user.cards && user.cards.length > 0
                      ).length})</span>
                      <div className="absolute inset-0 bg-gradient-to-r from-red-400/20 to-red-500/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </button>
                  )}
                  {Object.keys(pendingCardAssignments).length > 0 && (
                    <button
                      className="group relative px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white font-semibold rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 flex items-center justify-center gap-2 border border-blue-400/30"
                      onClick={handleSaveCardChanges}
                    >
                      <div className="relative">
                        <i className="pi pi-save text-base group-hover:rotate-12 transition-transform duration-300" />
                        </div>
                      <span className="text-base">Save Changes ({Object.keys(pendingCardAssignments).length})</span>
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-blue-500/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </button>
                  )}
                  <button
                    className="group relative px-6 py-3 bg-gradient-to-r from-emerald-500 to-teal-600 text-white font-semibold rounded-xl hover:from-emerald-600 hover:to-teal-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 flex items-center justify-center gap-2 border border-emerald-400/30"
                    onClick={handleEditClick}
                  >
                    <div className="relative">
                      <FiEdit className="text-base group-hover:rotate-12 transition-transform duration-300" />
                      </div>
                    <span className="text-base">Update Group</span>
                    <div className="absolute inset-0 bg-gradient-to-r from-emerald-400/20 to-teal-500/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </button>
                </div>
                  )}

              {/* Quick Actions */}
              <div className="mt-2 flex flex-wrap gap-2 items-center">
                  {/* View Toggle Buttons */}
                  <div className="flex bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200 dark:border-gray-600 rounded-lg p-1 shadow-sm">
                    <button
                      onClick={() => setViewMode('table')}
                      className={`px-3 py-2 rounded-md text-sm font-medium transition-all duration-300 flex items-center gap-2 ${
                        viewMode === 'table'
                          ? 'bg-blue-500 text-white shadow-md'
                          : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
                      }`}
                    >
                      <HiViewList size={16} />
                      Table
                    </button>
                    <button
                      onClick={() => setViewMode('cards')}
                      className={`px-3 py-2 rounded-md text-sm font-medium transition-all duration-300 flex items-center gap-2 ${
                        viewMode === 'cards'
                          ? 'bg-blue-500 text-white shadow-md'
                          : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
                      }`}
                    >
                      <HiViewGrid size={16} />
                      Cards
                    </button>
                  </div>

                  <button
                  onClick={() => {
                    setSelectedUnassignUsers([]);
                  }}
                  className="px-4 py-2 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-800 hover:border-gray-300 dark:hover:border-gray-500 transition-all duration-300 text-sm font-medium shadow-sm flex items-center gap-2 group"
                >
                  <div className="relative">
                    <MdClear className="text-gray-600 dark:text-gray-400 group-hover:text-gray-800 dark:group-hover:text-gray-200 transition-colors duration-300 group-hover:scale-110 group-hover:rotate-12" size={16} />
                    <div className="absolute inset-0 bg-gray-100 dark:bg-gray-700 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                    </div>
                  Clear Selection
                  </button>
                <div className="px-4 py-2 bg-blue-100/80 dark:bg-blue-900/20 backdrop-blur-sm border border-blue-200 dark:border-blue-700 rounded-lg text-blue-700 dark:text-blue-300 text-sm font-medium shadow-sm flex items-center gap-2 group">
                  <div className="relative">
                    <MdTrendingUp className="text-blue-600 dark:text-blue-400 group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors duration-300 group-hover:scale-110 group-hover:-rotate-12" size={16} />
                    <div className="absolute inset-0 bg-blue-200 dark:bg-blue-800 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                </div>
                  Showing {data?.length || 0} members
              </div>
            </div>
          </div>
          </div>
        </div>
      </div>

      {/* Data Table with Members */}
      <div className="bg-white dark:bg-gray-900">
        {isMobile ? (
          // Mobile Card Layout
          <div className="p-4">
            {/* Mobile Selection Controls */}
            <div className="mb-4 flex items-center justify-between bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={data && data.length > 0 && selectedUnassignUsers.length === data.length}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedUnassignUsers(data || []);
                    } else {
                      setSelectedUnassignUsers([]);
                    }
                  }}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Select All ({selectedUnassignUsers.length}/{data?.length || 0})
                </span>
              </div>
              {selectedUnassignUsers.length > 0 && (
                <button
                  onClick={() => setSelectedUnassignUsers([])}
                  className="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 font-medium"
                >
                  Clear
                </button>
              )}
            </div>
            
            {data && data.length > 0 ? (
              <div className="space-y-4">
                {data.map((member, index) => (
                  <div key={member.id} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm p-4">
                    {/* Member Header */}
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        {/* Checkbox for selection */}
                        <input
                          type="checkbox"
                          checked={selectedUnassignUsers.some(user => user.id === member.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedUnassignUsers(prev => [...prev, member]);
                            } else {
                              setSelectedUnassignUsers(prev => prev.filter(user => user.id !== member.id));
                            }
                          }}
                          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                        />
                        <img
                          src={member.image || profile_img}
                          alt="Profile"
                          className="w-12 h-12 rounded-full object-cover"
                        />
                        <div>
                          <h3 className="font-semibold text-gray-900 dark:text-gray-100">{member.name}</h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">{member.position || 'N/A'}</p>
                        </div>
                      </div>
                      <div className="relative">
                        <button
                          data-mobile-menu-trigger
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            console.log('Mobile action menu clicked for member:', member.id);
                            setMobileActionMenuOpen(mobileActionMenuOpen === member.id ? null : member.id);
                          }}
                          className="p-2 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                        >
                          <HiDotsVertical className="w-5 h-5" />
                        </button>
                      </div>
                    </div>

                    {/* Member Details */}
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-500 dark:text-gray-400">Type:</span>
                        <span className="text-gray-900 dark:text-gray-100">{member.type || 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500 dark:text-gray-400">Department:</span>
                        <span className="text-gray-900 dark:text-gray-100">{member.department || 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500 dark:text-gray-400">Card Status:</span>
                        <CardStatusIndicator memberId={member.id} />
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500 dark:text-gray-400">Print Status:</span>
                        {!lazyParams?.designID ? (
                          <div className="flex items-center gap-1">
                            <svg className="w-4 h-4 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                            <span className="text-xs font-medium text-amber-700 dark:text-amber-300">
                              Design Required
                            </span>
                          </div>
                        ) : (
                          <span className={`px-2 py-1 rounded text-xs font-medium text-white ${
                            statusStyles[member.print_status?.toLowerCase() || 'unprinted']
                          }`}>
                            {member.print_status || 'Unprinted'}
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Template Image */}
                    {member.template_image_html && (
                      <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
                        <div className="flex justify-center">
                          <div
                            className="rounded-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow duration-300 border border-gray-300 dark:border-gray-600"
                            style={{
                              width: '150px',
                              height: '120px',
                              position: 'relative'
                            }}
                            onClick={() => {
                              let templateWithUserData = member.template_image_html;
                              if (templateWithUserData.includes('https://www.gravatar.com/avatar/?d=mp')) {
                                templateWithUserData = templateWithUserData.replace(
                                  'https://www.gravatar.com/avatar/?d=mp',
                                  member.image || 'https://www.gravatar.com/avatar/?d=mp'
                                );
                              }
                              setSelectedTemplate(templateWithUserData);
                              setTemplateModalVisible(true);
                            }}
                          >
                            <div
                              style={{
                                width: '100%',
                                height: '100%',
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center'
                              }}
                            >
                              <div
                                style={{
                                  transform: 'scale(0.2)',
                                  transformOrigin: 'center center',
                                  display: 'inline-block',
                                  border: '2px solid black',
                                  borderRadius: '6px',
                                  padding: '6px',
                                  backgroundColor: 'white'
                                }}
                              >
                                {parse(member.template_image_html.replace(
                                  'https://www.gravatar.com/avatar/?d=mp',
                                  member.image || 'https://www.gravatar.com/avatar/?d=mp'
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                No members found in this group.
              </div>
            )}

            {/* Mobile Action Menu */}
            {mobileActionMenuOpen && (
              <MobileActionMenu
                member={data.find(m => m.id === mobileActionMenuOpen)}
                isOpen={!!mobileActionMenuOpen}
                onClose={() => setMobileActionMenuOpen(null)}
              />
            )}

            {/* Mobile Pagination */}
            {data && data.length > 0 && totalRecords > lazyParams?.rows && (
              <div className="mt-6 flex justify-center">
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => dataHandler({ first: Math.max(0, (lazyParams?.first || 0) - (lazyParams?.rows || 5)) })}
                    disabled={!lazyParams?.first || lazyParams.first === 0}
                    className="px-3 py-2 text-sm bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100"
                  >
                    Previous
                  </button>
                  <span className="px-3 py-2 text-sm text-gray-600 dark:text-gray-400">
                    {Math.floor((lazyParams?.first || 0) / (lazyParams?.rows || 5)) + 1} of {Math.ceil(totalRecords / (lazyParams?.rows || 5))}
                  </span>
                  <button
                    onClick={() => dataHandler({ first: (lazyParams?.first || 0) + (lazyParams?.rows || 5) })}
                    disabled={(lazyParams?.first || 0) + (lazyParams?.rows || 5) >= totalRecords}
                    className="px-3 py-2 text-sm bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100"
                  >
                    Next
                  </button>
                </div>
              </div>
            )}
          </div>
        ) : viewMode === 'cards' ? (
          // Cards View Layout
          <div className="space-y-4">
            {/* Cards Grid - 3 per row */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-4">
            {data?.map((member) => (
              <CardMemberComponent 
                key={member.id} 
                member={member}
                isSelected={selectedUnassignUsers.some(selected => selected.id === member.id)}
                onSelect={() => {
                  const isSelected = selectedUnassignUsers.some(selected => selected.id === member.id);
                  if (isSelected) {
                    setSelectedUnassignUsers(prev => prev.filter(selected => selected.id !== member.id));
                  } else {
                    setSelectedUnassignUsers(prev => [...prev, member]);
                  }
                }}
                onEdit={() => {
                  setSelectedMember(member);
                  dialogHandler("addMember");
                }}
                setSelectedMember={setSelectedMember}
                dialogHandler={dialogHandler}
                handleDeleteMemberClick={handleDeleteMemberClick}
                confirmDialog={confirmDialog}
                setSelectedTemplate={setSelectedTemplate}
                setTemplateModalVisible={setTemplateModalVisible}
                groupId={groupID}
                onDelete={() => {
                  handleDeleteMemberClick(member, groupID);
                }}
                onTemplateView={() => {
                  let templateWithUserData = member.template_image_html;
                  if (templateWithUserData.includes('https://www.gravatar.com/avatar/?d=mp')) {
                    templateWithUserData = templateWithUserData.replace(
                      'https://www.gravatar.com/avatar/?d=mp',
                      member.image || 'https://www.gravatar.com/avatar/?d=mp'
                    );
                  }
                  setSelectedTemplate(templateWithUserData);
                  setTemplateModalVisible(true);
                }}
                lazyParams={lazyParams}
                statusStyles={statusStyles}
                profile_img={profile_img}
                handleAssignCard={handleAssignCard}
                handleUnassignCard={handleUnassignCard}
                checkCardAssignmentStatus={checkCardAssignmentStatus}
                getAvailableCards={getAvailableCards}
                pendingCardAssignments={pendingCardAssignments}
                memberCards={memberCards}
              />
            ))}
            </div>
            
            {/* Pagination for Cards */}
            <div className="flex justify-center px-4">
              <div className="flex items-center gap-2 bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm border border-gray-200 dark:border-gray-700">
                <button
                  onClick={() => dataHandler({ first: 0, rows: lazyParams?.rows || 9 })}
                  disabled={!lazyParams?.first || lazyParams.first === 0}
                  className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  First
                </button>
                <button
                  onClick={() => dataHandler({ first: Math.max(0, (lazyParams?.first || 0) - (lazyParams?.rows || 9)), rows: lazyParams?.rows || 9 })}
                  disabled={!lazyParams?.first || lazyParams.first === 0}
                  className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  Previous
                </button>
                
                <span className="px-3 py-1 text-sm text-gray-600 dark:text-gray-400">
                  Page {Math.floor((lazyParams?.first || 0) / (lazyParams?.rows || 9)) + 1} of {Math.ceil(totalRecords / (lazyParams?.rows || 9))}
                </span>
                
                <button
                  onClick={() => dataHandler({ first: (lazyParams?.first || 0) + (lazyParams?.rows || 9), rows: lazyParams?.rows || 9 })}
                  disabled={(lazyParams?.first || 0) + (lazyParams?.rows || 9) >= totalRecords}
                  className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  Next
                </button>
                <button
                  onClick={() => dataHandler({ first: Math.max(0, totalRecords - (lazyParams?.rows || 9)), rows: lazyParams?.rows || 9 })}
                  disabled={(lazyParams?.first || 0) + (lazyParams?.rows || 9) >= totalRecords}
                  className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  Last
                </button>
                
                <select
                  value={lazyParams?.rows || 9}
                  onChange={(e) => dataHandler({ first: 0, rows: parseInt(e.target.value) })}
                  className="px-2 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-300"
                >
                  <option value={9}>9 per page</option>
                  <option value={18}>18 per page</option>
                  <option value={27}>27 per page</option>
                  <option value={36}>36 per page</option>
                </select>
              </div>
            </div>
          </div>
        ) : (
          // Desktop Table Layout
          <DataTable
            selection={selectedUnassignUsers}
            onSelectionChange={e => setSelectedUnassignUsers(e.value)}
            lazy
            responsiveLayout="stack"
            breakpoint="960px"
            dataKey="id"
            paginator
            className="border-t-0 dark:bg-gray-800"
            rowClassName="row dark:bg-slate-800 dark:hover:bg-slate-700/50"
            value={data}
            first={lazyParams?.first}
            rows={lazyParams?.rows}
            rowsPerPageOptions={[5, 25, 50, 100]}
            totalRecords={totalRecords}
            onPage={dataHandler}
            onSort={dataHandler}
            sortField={lazyParams?.sortField}
            sortOrder={lazyParams?.sortOrder}
            onFilter={dataHandler}
            filters={lazyParams?.filters}
            loading={loading}
            scrollable
            scrollHeight="100%"
            paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
            currentPageReportTemplate="Showing {first} to {last} of {totalRecords} products"
            pt={{
              header: { className: 'dark:bg-gray-800 dark:border-gray-700' },
              headerCell: { className: 'dark:bg-gray-800 dark:text-gray-200 dark:border-gray-700' },
              columnHeader: { className: 'dark:bg-gray-800 dark:text-gray-200' },
              headerCheckbox: { className: 'dark:bg-gray-800' },
              table: { className: 'dark:bg-gray-800' },
              tbody: { className: 'dark:bg-gray-800' },
              row: { className: 'dark:bg-gray-800 dark:hover:bg-gray-700' },
              cell: { className: 'dark:bg-gray-800 dark:text-gray-200 dark:border-gray-700' },
              thead: { className: 'dark:bg-gray-800' },
              headerRow: { className: 'dark:bg-gray-800 dark:border-gray-700' }
            }}
            header={
              <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    checked={data && data.length > 0 && selectedUnassignUsers.length === data.length}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedUnassignUsers(data || []);
                      } else {
                        setSelectedUnassignUsers([]);
                      }
                    }}
                    className="w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
                  />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Select All ({selectedUnassignUsers.length}/{data?.length || 0} selected)
                  </span>
                </div>
                {selectedUnassignUsers.length > 0 && (
                  <button
                    onClick={() => setSelectedUnassignUsers([])}
                    className="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 font-medium"
                  >
                    Clear Selection
                  </button>
                )}
              </div>
            }
          >
                <Column selectionMode="multiple" headerStyle={{ width: '3em' }} />
                {/* <Column
                  body={profileBodyTemplate}
                  header="Profile Image"
                  className="text-center"
                /> */}
                <Column
                  body={imageBodyTemplate}
                  header="Template"
                  className="text-center"
                  style={{ width: '275px', minWidth: '250px' }}
                />
                <Column
                  field="name"
                  header="Name"
                  className="text-left truncate-cell"
                  style={{ minWidth: '180px', maxWidth: '220px', width: '200px' }}
                  // filter
                  sortable
                  body={(rowData) => (
                    <div className="flex items-center gap-2">
                      <img
                        src={rowData.image || profile_img}
                        alt="Profile"
                        className="w-8 h-8 rounded-full object-cover"
                      />
                      <span>{rowData.name}</span>
                    </div>
                  )}
                />
                <Column
                  field="type"
                  header="Type"
                  className="text-center truncate-cell"
                  style={{ minWidth: '120px', maxWidth: '150px', width: '130px' }}
                  filter
                  sortable
                  showFilterMenu={false}
                />
                <Column
                  field="position"
                  header="Position"
                  className="text-center truncate-cell "
                  style={{ minWidth: '120px', maxWidth: '150px', width: '130px' }}
                  filter
                  sortable
                  showFilterMenu={false}
                />
                  <Column
                  field="department"
                  header="Department"
                  className="text-center truncate-cell"
                  style={{ minWidth: '120px', maxWidth: '150px', width: '130px' }}
                  filter
                  sortable
                  showFilterMenu={false}
                />

<Column
  header="Card Details"
  body={(rowData) => {
    const [cardDetails, setCardDetails] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
      const fetchCardDetails = async () => {
        setLoading(true);
        const status = await checkCardAssignmentStatus(rowData.id);
        if (status && status.status === 'assigned') {
          setCardDetails(status.card);
        } else {
          setCardDetails(null);
        }
        setLoading(false);
      };

      fetchCardDetails();
    }, [rowData.id, pendingCardAssignments]);

    if (loading) {
      return <div className="flex justify-center"><i className="pi pi-spin pi-spinner"></i></div>;
    }

    return cardDetails ? (
      <div className="bg-gradient-to-r from-blue-50 to-gray-50 dark:from-blue-900/20 dark:to-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-3 shadow-sm w-[240px]">
        <div className="flex justify-between items-center mb-2 ml-10">
          <span className="text-xs font-semibold text-gray-500 dark:text-gray-400">CARD NUMBER</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="font-mono text-sm font-bold tracking-wider text-gray-900 dark:text-gray-100">
            {cardDetails.number || 'N/A'}
          </span>
          <i className="pi pi-credit-card text-gray-400 dark:text-gray-500 ml-2"></i>
        </div>
      </div>
    ) : (
      <span className="text-sm text-gray-500 dark:text-gray-400 italic ml-10">No card assigned</span>
    );
  }}
  className="text-center ml-10"
  style={{ minWidth: '260px' }}
/>

<Column
  header="Card Assignment"
  body={(rowData) => {
    const availableCards = getAvailableCards(rowData.id);
    const [isUpdating, setIsUpdating] = useState(false);
    const [assignmentStatus, setAssignmentStatus] = useState(null);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
      const fetchAssignmentStatus = async () => {
        setIsLoading(true);
        const status = await checkCardAssignmentStatus(rowData.id);
        setAssignmentStatus(status);
        setIsLoading(false);
      };

      fetchAssignmentStatus();
    }, [rowData.id, memberCards, pendingCardAssignments]);

    const handleCardAssign = (memberId, card) => {
      handleAssignCard(memberId, card);
    };

    const handleCardUnassign = (memberId, cardNumber) => {
      handleUnassignCard(memberId, cardNumber);
    };

    // Check if user has pending assignment
    const pendingAssignment = pendingCardAssignments[rowData.id];
    const hasPendingAssignment = pendingAssignment && pendingAssignment.action === 'assign';
    const hasPendingUnassignment = pendingAssignment && pendingAssignment.action === 'unassign';

    if (isLoading) {
      return <div className="flex justify-center"><i className="pi pi-spin pi-spinner"></i></div>;
    }

    return (
      <div className="flex justify-center items-center gap-2">
        {hasPendingAssignment ? (
          // Show pending assignment
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-3 w-[200px]">
            <div className="flex justify-between items-center mb-2">
              <span className="text-xs font-semibold text-yellow-600 dark:text-yellow-400">PENDING ASSIGNMENT</span>
              <button
                onClick={() => handleCardUnassign(rowData.id, pendingAssignment.cardNumber)}
                className="text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300"
                title="Cancel Assignment"
              >
                <i className="pi pi-times"></i>
              </button>
            </div>
            <div className="font-mono text-sm font-bold tracking-wider text-yellow-800 dark:text-yellow-300">
              {pendingAssignment.cardNumber}
            </div>
          </div>
        ) : hasPendingUnassignment ? (
          // Show pending unassignment
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-3 w-[200px]">
            <div className="flex justify-between items-center mb-2">
              <span className="text-xs font-semibold text-red-600 dark:text-red-400">PENDING UNASSIGNMENT</span>
              <button
                onClick={() => handleCardUnassign(rowData.id, pendingAssignment.cardNumber)}
                className="text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300"
                title="Cancel Unassignment"
              >
                <i className="pi pi-times"></i>
              </button>
            </div>
            <div className="font-mono text-sm font-bold tracking-wider text-red-800 dark:text-red-300">
              {pendingAssignment.cardNumber}
            </div>
          </div>
        ) : assignmentStatus?.status !== 'assigned' ? (
          availableCards.length > 0 ? (
            <div className="flex items-center gap-2">
              <div className="relative w-[200px]">
                <Dropdown
                  value={null}
                  options={availableCards}
                  onChange={(e) => e.value && handleCardAssign(rowData.id, e.value)}
                  optionLabel="label"
                  className="w-full"
                  panelClassName="border border-gray-200 dark:border-gray-600 shadow-xl rounded-lg mt-1"
                  dropdownIcon="pi pi-chevron-down"
                  placeholder={`Assign Card (${availableCards.length} available)`}
                  pt={{
                    root: { className: 'border border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400 transition-colors' },
                    trigger: { className: 'bg-white dark:bg-gray-800' },
                    panel: { className: 'shadow-lg bg-white dark:bg-gray-800' },
                    item: ({ context }) => ({
                      className: context.selected ? 'bg-blue-50 dark:bg-blue-900/20' : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                    }),
                  }}
                  itemTemplate={(option) => (
                    <div className="flex items-center justify-between p-2">
                      <div className="flex items-center">
                        {option.cardData?.card_type?.type_of_connection === 'NFC' ? (
                          <div className="text-blue-500 dark:text-blue-400 mr-3">
                            <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M12 4C7.58 4 4 7.58 4 12C4 16.42 7.58 20 12 20C16.42 20 20 16.42 20 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                              <path d="M12 8C9.79 8 8 9.79 8 12C8 14.21 9.79 16 12 16C14.21 16 16 14.21 16 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                              <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                              <path d="M20 4L20 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                              <path d="M20 4L16 4" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                            </svg>
                          </div>
                        ) : (
                          <div className="text-purple-500 dark:text-purple-400 mr-3">
                            <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M6 8L18 16L12 22V2L18 8L6 16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                          </div>
                        )}
                        <div>
                          <div className="font-medium text-gray-900 dark:text-gray-100">{option.label}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {option.cardData?.card_type?.type_of_connection || 'Unknown'} Card
                          </div>
                        </div>
                      </div>
                      {option.number && (
                        <div className="font-mono text-xs bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100 px-2 py-1 rounded">
                          {option.number.slice(-4)}
                        </div>
                      )}
                    </div>
                  )}
                />
                {isUpdating && (
                  <div className="absolute inset-0 bg-white bg-opacity-50 flex items-center justify-center">
                    <i className="pi pi-spinner pi-spin text-blue-500"></i>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center p-3 border border-dashed border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-800 w-[200px]">
              <i className="pi pi-info-circle text-gray-400 dark:text-gray-500 mb-1"></i>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {Object.keys(pendingCardAssignments).length > 0
                  ? "No cards available (some may be pending assignment)"
                  : "No available cards"
                }
              </p>
            </div>
          )
        ) : (
          <Button
            severity="danger"
            label="Unassign Card"
            tooltip="Unassign Card"
            tooltipOptions={{ position: 'top' }}
            onClick={() => handleCardUnassign(rowData.id, assignmentStatus.card.number)}
            disabled={isUpdating}
            className="w-[200px]"
            style={{
              backgroundColor: '#ef4444',
              borderColor: '#ef4444',
              color: 'white',
              width: '200px',
              borderRadius: '8px'
            }}
            pt={{
              root: {
                className: '!w-[200px] !rounded-lg dark:!bg-red-600 dark:!border-red-600'
              },
              label: {
                className: '!text-white'
              }
            }}
          />
        )}
      </div>
    );
  }}
  className="text-center"
  style={{ minWidth: '260px' }}
/>



                <Column
                  field="status"
                  body={statusBodyTemplate}
                  header="Status"
                  className="text-center"
                  showFilterMenu={false}
                  sortable
                  style={{ minWidth: '200px', maxWidth: '250px', width: '220px' }}
                />
                <Column
                  body={actionBodyTemplate}
                  header="Actions"
                  exportable={false}
                  style={{ minWidth: '200px', maxWidth: '250px', width: '220px' }}
                />
          </DataTable>
        )}
      </div>

      {openDialog?.addMember && (
        <AddMemberDialog
          data={selectedMember}
          actionType={actionType}
          onSuccess={() => {
            // Show success message
            toastRef.current?.show({
              severity: 'success',
              summary: 'Success',
              detail: actionType === "update" ? 'Member updated successfully!' : 'Member created successfully!',
              life: 3000
            });

            // Refresh the group data
            if (groupID) {
              fetchUsersByGroup(groupID);
            }
          }}
        />
      )}
      {openDialog?.updateGroup && <AssignGroupDialog />}

      <Toast ref={toastRef} position="bottom-center" />

      {isEditGroupModalOpen && groupBeingEdited && (
        <GroupForm
          isModalOpen={isEditGroupModalOpen}
          setIsModalOpen={setIsEditGroupModalOpen}
          onSuccess={handleGroupUpdateSuccess}
          groupToEdit={groupBeingEdited}
          toast={toastRef}
        />
      )}

      {/* Template Modal */}
      <Dialog
        visible={templateModalVisible}
        onHide={() => {
          setTemplateModalVisible(false);
          setTemplateZoomLevel(1.0); // Reset zoom when closing
        }}
        header={
          <div className="flex justify-between items-center w-full">
            <span className="mr-1 text-gray-900 dark:text-gray-100">Template Preview</span>
            <div className="flex items-center gap-2 mr-8">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setTemplateZoomLevel(prev => Math.max(prev - 0.2, 0.5));
                }}
                className="p-2 rounded-md bg-white dark:bg-gray-800 text-black dark:text-white border border-gray-300 dark:border-gray-600 hover:shadow-md transition-all hover:translate-y-[-2px] w-9 h-9 flex items-center justify-center"
                title="Zoom Out"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M4 8a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7A.5.5 0 0 1 4 8z"/>
                </svg>
              </button>
              <span className="text-sm font-medium w-14 text-center text-gray-900 dark:text-gray-100">{Math.round(templateZoomLevel * 100)}%</span>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setTemplateZoomLevel(prev => Math.min(prev + 0.2, 3.0));
                }}
                className="p-2 rounded-md bg-white dark:bg-gray-800 text-black dark:text-white border border-gray-300 dark:border-gray-600 hover:shadow-md transition-all hover:translate-y-[-2px] w-9 h-9 flex items-center justify-center"
                title="Zoom In"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                </svg>
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setTemplateZoomLevel(1.0);
                }}
                className="p-2 rounded-md bg-white dark:bg-gray-800 text-black dark:text-white border border-gray-300 dark:border-gray-600 hover:shadow-md transition-all hover:translate-y-[-2px] w-9 h-9 flex items-center justify-center"
                title="Reset Zoom"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M8 3a5 5 0 1 1-4.546 2.914.5.5 0 0 0-.908-.417A6 6 0 1 0 8 2v1z"/>
                  <path d="M8 4.466V.534a.25.25 0 0 0-.41-.192L5.23 2.308a.25.25 0 0 0 0 .384l2.36 1.966A.25.25 0 0 0 8 4.466z"/>
                </svg>
              </button>
            </div>
          </div>
        }
        style={{ width: '90vw', maxWidth: '1200px' }}
        modal
        className="template-preview-dialog"
        contentClassName="p-0 overflow-hidden"
      >
        <div className="template-preview-container p-4 bg-gray-100 dark:bg-gray-800 rounded-b-lg flex justify-center items-center overflow-auto" style={{ maxHeight: '80vh' }}>
          <div
            className="template-preview-content bg-white dark:bg-gray-700 rounded-lg shadow-xl p-6 border border-gray-200 dark:border-gray-600"
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              transform: `scale(${templateZoomLevel})`,
              transformOrigin: 'center center',
              transition: 'transform 0.2s ease-in-out',
              background: '#ffffff' // Force white background for transparent templates
            }}
          >
            {selectedTemplate && (
              <div 
                style={{ 
                  background: '#ffffff', // Ensure white background for transparent templates
                  borderRadius: '8px',
                  padding: '10px'
                }}
              >
                {parse(selectedTemplate)}
              </div>
            )}
          </div>
        </div>
      </Dialog>

      {/* Exit Warning Modal */}
      <Dialog
        visible={showExitWarningModal}
        onHide={handleCancelExit}
        modal
        closable={false}
        className="exit-warning-modal"
        style={{ width: '650px', maxWidth: '90vw' }}
        contentClassName="p-0 overflow-hidden"
      >
        <div className="relative bg-white dark:bg-gray-800 p-10 rounded-lg">
          {/* Animated Background Elements */}
          <div className="absolute inset-0 overflow-hidden rounded-lg">
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-red-200 rounded-full opacity-20 animate-pulse"></div>
            <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-orange-200 rounded-full opacity-15 animate-pulse" style={{ animationDelay: '1s' }}></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-16 bg-yellow-200 rounded-full opacity-10 animate-ping"></div>
          </div>

          {/* Content */}
          <div className="relative z-10">
            {/* Warning Icon */}
            <div className="flex justify-center mb-6">
              <div className="relative">
                <div className="w-20 h-20 bg-gradient-to-br from-red-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg animate-bounce">
                  <i className="pi pi-exclamation-triangle text-white text-3xl"></i>
                </div>
                <div className="absolute inset-0 w-20 h-20 bg-gradient-to-br from-red-500 to-orange-500 rounded-full animate-ping opacity-25"></div>
              </div>
            </div>

            {/* Title */}
            <h2 className="text-2xl font-bold text-center text-gray-800 dark:text-gray-100 mb-4">
              ⚠️ Unsaved Card Assignments
            </h2>

            {/* Message */}
            <div className="text-center mb-6">
              <p className="text-gray-700 dark:text-gray-300 text-lg mb-3">
                You have <span className="font-bold text-red-600 dark:text-red-400">{Object.keys(pendingCardAssignments).length}</span> pending card assignments that haven't been saved yet.
              </p>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                If you leave now, all pending assignments will be lost and cards will not be linked to users.
              </p>
            </div>

            {/* Pending Assignments List */}
            <div className="bg-white dark:bg-gray-700 rounded-lg p-4 mb-6 border-l-4 border-yellow-400 shadow-inner">
              <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-2 flex items-center">
                <i className="pi pi-clock text-yellow-500 mr-2"></i>
                Pending Assignments:
              </h4>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {Object.entries(pendingCardAssignments).map(([userId, assignment]) => {
                  const user = data.find(u => u.id.toString() === userId);
                  return (
                    <div key={userId} className="flex items-center justify-between text-sm bg-white dark:bg-gray-600 p-3 rounded border border-gray-200 dark:border-gray-500 shadow-sm">
                      <span className="font-medium text-gray-700 dark:text-gray-200">{user?.name || 'Unknown User'}</span>
                      <span className="font-mono text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900/30 px-3 py-1 rounded-md font-bold">
                        {assignment.cardNumber}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col gap-6">
              {/* Save and Continue Button */}
              <button
                onClick={handleSaveAndExit}
                className="group relative overflow-hidden px-8 py-4 rounded-xl font-bold text-white transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 active:scale-95 flex items-center justify-center text-lg"
                style={{
                  background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                  boxShadow: '0 10px 25px rgba(16, 185, 129, 0.4)'
                }}
                onMouseEnter={(e) => {
                  e.target.style.boxShadow = '0 20px 40px rgba(16, 185, 129, 0.6)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.boxShadow = '0 10px 25px rgba(16, 185, 129, 0.4)';
                }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                <i className="pi pi-save mr-3 text-lg group-hover:animate-bounce"></i>
                <span className="relative z-10">Save Changes & Continue</span>
              </button>

              {/* Discard and Leave Button */}
              <button
                onClick={handleConfirmExit}
                className="group relative overflow-hidden px-8 py-4 rounded-xl font-bold text-white transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 active:scale-95 flex items-center justify-center text-lg"
                style={{
                  background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                  boxShadow: '0 10px 25px rgba(239, 68, 68, 0.4)'
                }}
                onMouseEnter={(e) => {
                  e.target.style.boxShadow = '0 20px 40px rgba(239, 68, 68, 0.6)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.boxShadow = '0 10px 25px rgba(239, 68, 68, 0.4)';
                }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                <i className="pi pi-trash mr-3 text-lg group-hover:animate-pulse"></i>
                <span className="relative z-10">Discard Changes & Leave</span>
              </button>

              {/* Cancel Button */}
              <button
                onClick={handleCancelExit}
                className="px-8 py-4 rounded-xl font-bold text-gray-700 dark:text-gray-200 bg-gray-100 dark:bg-gray-600 border-2 border-gray-300 dark:border-gray-500 hover:bg-gray-200 dark:hover:bg-gray-500 hover:border-gray-400 dark:hover:border-gray-400 transition-all duration-300 flex items-center justify-center text-lg"
              >
                <i className="pi pi-times mr-3"></i>
                Stay on Page
              </button>
            </div>
          </div>
        </div>
      </Dialog>

      {/* Image Generation Modal - Same as DesignSpace but with batch support */}
      <ImageGenerationModal
        visible={showImageGenerationModal}
        designId={currentDesignId}
        batchId={currentDesignId ? localStorage.getItem(`batch_${currentDesignId}`) : null}
        useBatchApi={true} // Use batch API for new user assignments
        groupIds={[groupID]} // Pass groupIds as array
        onHide={() => {
          setShowImageGenerationModal(false);
          // Refresh data after modal closes to ensure everything is up to date
          if (groupID) {
            setTimeout(() => {
              fetchUsersByGroup(groupID);
            }, 500); // Small delay to ensure backend has processed everything
          }
        }}
      />

      {/* Confirmation Dialog for member deletion */}
      <ConfirmDialog />

      </div>
    </>
  );
}

export default GroupMembersPage;

