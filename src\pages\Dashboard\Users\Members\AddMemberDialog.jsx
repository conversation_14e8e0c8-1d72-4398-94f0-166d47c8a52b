import { useEffect, useRef, useState } from 'react'
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import PropTypes from 'prop-types';

import { getFormErrorMessage } from '@utils/helper'
import { useGlobalContext } from '@contexts/GlobalContext';
import {
  useCreateUserMutation,
  useUpdateUserMutation,
  useUploadMutation
} from '@quires';

import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { Password } from 'primereact/password';
import { Dialog } from 'primereact/dialog';
import { Button } from 'primereact/button';

import CustomFields from './CustomFields';

const userTypeOptions = [
  { label: 'Admin', value: 'admin' },
  { label: 'Manager', value: 'manager' },
];

// Countries list for phone validation
const countries = [
  // Middle East & Arab Countries
  { name: 'Jordan', code: '+962', flag: '🇯🇴' },
  { name: 'Saudi Arabia', code: '+966', flag: '🇸🇦' },
  { name: 'UAE', code: '+971', flag: '🇦🇪' },
  { name: 'Kuwait', code: '+965', flag: '🇰🇼' },
  { name: 'Qatar', code: '+974', flag: '🇶🇦' },
  { name: 'Bahrain', code: '+973', flag: '🇧🇭' },
  { name: 'Oman', code: '+968', flag: '🇴🇲' },
  { name: 'Iraq', code: '+964', flag: '🇮🇶' },
  { name: 'Syria', code: '+963', flag: '🇸🇾' },
  { name: 'Lebanon', code: '+961', flag: '🇱🇧' },
  { name: 'Turkey', code: '+90', flag: '🇹🇷' },
  { name: 'Egypt', code: '+20', flag: '🇪🇬' },
  { name: 'Palestine', code: '+970', flag: '🇵🇸' },
  { name: 'Yemen', code: '+967', flag: '🇾🇪' },
  { name: 'Morocco', code: '+212', flag: '🇲🇦' },
  { name: 'Tunisia', code: '+216', flag: '🇹🇳' },
  { name: 'Algeria', code: '+213', flag: '🇩🇿' },
  
  // International
  { name: 'United States', code: '+1', flag: '🇺🇸' },
  { name: 'United Kingdom', code: '+44', flag: '🇬🇧' },
  { name: 'France', code: '+33', flag: '🇫🇷' },
  { name: 'Germany', code: '+49', flag: '🇩🇪' },
  { name: 'Canada', code: '+1', flag: '🇨🇦' },
  { name: 'Australia', code: '+61', flag: '🇦🇺' },
  { name: 'India', code: '+91', flag: '🇮🇳' },
  { name: 'Pakistan', code: '+92', flag: '🇵🇰' },
  { name: 'Bangladesh', code: '+880', flag: '🇧🇩' },
  { name: 'Malaysia', code: '+60', flag: '🇲🇾' },
  { name: 'Singapore', code: '+65', flag: '🇸🇬' },
  { name: 'Philippines', code: '+63', flag: '🇵🇭' },
  { name: 'Indonesia', code: '+62', flag: '🇮🇩' },
  { name: 'Thailand', code: '+66', flag: '🇹🇭' }
];

function AddMemberDialog({ actionType, data, onSuccess }) {
  const { openDialog, disableBtn, setDisableBtn, setOpenDialog } = useGlobalContext();
  const { control, formState: { errors }, handleSubmit, reset, setValue, watch, setError } = useForm({
    defaultValues: {
      countryCode: { name: 'Jordan', code: '+962', flag: '🇯🇴' }
    }
  });
  
  // Watch the selected country code
  const selectedCountry = watch('countryCode');
  
  // Add custom styles for consistent field sizing
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .p-dropdown {
        height: 48px !important;
        border-radius: 12px !important;
        border: 2px solid #cbd5e1 !important;
        background-color: white !important;
        transition: all 0.2s !important;
      }
      
      .p-dropdown:focus-within {
        border-color: #64748b !important;
        box-shadow: 0 0 0 2px rgba(100, 116, 139, 0.2) !important;
      }
      
      .p-dropdown.p-invalid {
        border-color: #ef4444 !important;
        background-color: #fef2f2 !important;
      }
      
      .p-dropdown .p-dropdown-trigger {
        width: 48px !important;
      }
      
      .p-dropdown .p-dropdown-label {
        padding: 12px 16px !important;
        font-size: 0.875rem !important;
      }
      
      .p-dropdown-panel-sm .p-dropdown-items .p-dropdown-item {
        padding: 8px 16px !important;
        font-size: 0.875rem !important;
      }
      
      .p-password {
        height: 48px !important;
        border-radius: 12px !important;
        border: 2px solid #cbd5e1 !important;
        background-color: white !important;
        transition: all 0.2s !important;
      }
      
      .p-password:focus-within {
        border-color: #64748b !important;
        box-shadow: 0 0 0 2px rgba(100, 116, 139, 0.2) !important;
      }
      
      .p-password.p-invalid {
        border-color: #ef4444 !important;
        background-color: #fef2f2 !important;
      }
      
      .p-password .p-password-input {
        height: 44px !important;
        padding: 12px 16px !important;
        font-size: 0.875rem !important;
        border: none !important;
        background: transparent !important;
      }
      
      .p-password .p-password-toggle {
        width: 48px !important;
        height: 44px !important;
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Format phone number as user types based on country
  const formatPhoneNumber = (value, countryCode) => {
    if (!value) return '';
    
    // Remove all non-numeric characters
    const cleaned = value.replace(/\D/g, '');
    
    // Apply formatting based on country
    switch (countryCode) {
      case '+1': // US/Canada format: (*************
        if (cleaned.length <= 3) return cleaned;
        if (cleaned.length <= 6) return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3)}`;
        return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}`;
      
      case '+44': // UK format: 0123 456 7890
        if (cleaned.length <= 4) return cleaned;
        if (cleaned.length <= 7) return `${cleaned.slice(0, 4)} ${cleaned.slice(4)}`;
        return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7, 11)}`;
      
      case '+962': // Jordan format: 07 9999 9999
      case '+963': // Syria
      case '+961': // Lebanon
        if (cleaned.length <= 2) return cleaned;
        if (cleaned.length <= 6) return `${cleaned.slice(0, 2)} ${cleaned.slice(2)}`;
        return `${cleaned.slice(0, 2)} ${cleaned.slice(2, 6)} ${cleaned.slice(6, 10)}`;
      
      case '+966': // Saudi Arabia: 5X XXX XXXX
      case '+971': // UAE: 5X XXX XXXX  
      case '+965': // Kuwait: 9XXX XXXX
      case '+974': // Qatar: 9XXX XXXX
      case '+973': // Bahrain: 9XXX XXXX
      case '+968': // Oman: 9XXX XXXX
        if (cleaned.length <= 2) return cleaned;
        if (cleaned.length <= 5) return `${cleaned.slice(0, 2)} ${cleaned.slice(2)}`;
        return `${cleaned.slice(0, 2)} ${cleaned.slice(2, 5)} ${cleaned.slice(5, 9)}`;
      
      case '+964': // Iraq: 07XX XXX XXXX
        if (cleaned.length <= 3) return cleaned;
        if (cleaned.length <= 6) return `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`;
        return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6, 10)}`;
      
      case '+90': // Turkey: (5XX) XXX XX XX
        if (cleaned.length <= 3) return cleaned;
        if (cleaned.length <= 6) return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3)}`;
        if (cleaned.length <= 8) return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;
        return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)} ${cleaned.slice(6, 8)} ${cleaned.slice(8, 10)}`;
      
      default: // Generic formatting: XXX XXX XXXX
        if (cleaned.length <= 3) return cleaned;
        if (cleaned.length <= 6) return `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`;
        return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;
    }
  };

  // Validate phone number based on country
  const validatePhoneNumber = (phone, countryCode) => {
    if (!phone) return false;
    
    const cleaned = phone.replace(/\D/g, '');
    
    switch (countryCode) {
      case '+1': // US/Canada: 10 digits
        return cleaned.length === 10;
      case '+44': // UK: 10-11 digits
        return cleaned.length >= 10 && cleaned.length <= 11;
      case '+962': // Jordan: 10 digits (e.g., 0798307711)
        return cleaned.length === 10;
      case '+963': // Syria: 9 digits
      case '+961': // Lebanon: 8 digits
        return cleaned.length >= 8 && cleaned.length <= 9;
      case '+966': // Saudi Arabia: 9 digits
      case '+971': // UAE: 9 digits
      case '+965': // Kuwait: 8 digits
      case '+974': // Qatar: 8 digits
      case '+973': // Bahrain: 8 digits
      case '+968': // Oman: 8 digits
        return cleaned.length >= 8 && cleaned.length <= 9;
      case '+964': // Iraq: 10 digits
        return cleaned.length === 10;
      case '+90': // Turkey: 10 digits
        return cleaned.length === 10;
      default:
        return cleaned.length >= 7 && cleaned.length <= 15;
    }
  };

  const countryTemplate = (option) => {
    return (
      <div className="flex items-center">
        <span className="mr-2">{option.flag}</span>
        <span>{option.name} ({option.code})</span>
      </div>
    );
  };

  const selectedCountryTemplate = (option) => {
    if (option) {
      return (
        <div className="flex items-center">
          <span className="mr-2">{option.flag}</span>
          <span>{option.code}</span>
        </div>
      );
    }
    return <span>Select Country</span>;
  };

  const uploadImage = useUploadMutation()
  const updateUser = useUpdateUserMutation();
  const addUser = useCreateUserMutation();

  const [companyUserType, setCompanyUserType] = useState("employee");
  const [image, setImage] = useState({});
  const [uploadedImageUrl, setUploadedImageUrl] = useState(null); // متغير جديد لحفظ رابط الصورة المرفوعة
  const [imagePreviewUrl, setImagePreviewUrl] = useState(null);
  const [originalImageUrl, setOriginalImageUrl] = useState(null);
  const [hasNewImage, setHasNewImage] = useState(false);
  const [imageUploading, setImageUploading] = useState(false); // حالة تحميل الصورة
  const hiddenFileInput = useRef(null);

  const [currentUserType] = useState(() => localStorage.getItem('user_type'));
  const isManager = currentUserType === 'manager';
  // const formUserType = watch('user_type');

  // Mobile detection
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // تنظيف URL المعاينة عند إغلاق المكون
  useEffect(() => {
    return () => {
      if (imagePreviewUrl && imagePreviewUrl !== originalImageUrl) {
        URL.revokeObjectURL(imagePreviewUrl);
      }
    };
  }, [imagePreviewUrl, originalImageUrl]);

  const onClearImage = () => {
    setImage({});
    setHasNewImage(false);
    if (imagePreviewUrl && imagePreviewUrl !== originalImageUrl) {
      URL.revokeObjectURL(imagePreviewUrl);
    }
    setImagePreviewUrl(originalImageUrl || null);
    if (hiddenFileInput.current) {
      hiddenFileInput.current.value = "";
    }
  };

  const handleFileInputChange = async (event) => {
    const selectedFile = event.target.files[0];
    if (selectedFile) {
      setImage(selectedFile);
      setHasNewImage(true);
      if (imagePreviewUrl && imagePreviewUrl !== originalImageUrl) {
        URL.revokeObjectURL(imagePreviewUrl);
      }
      const previewUrl = URL.createObjectURL(selectedFile);
      setImagePreviewUrl(previewUrl);
      if (hiddenFileInput.current) {
        hiddenFileInput.current.value = "";
      }
      // رفع الصورة مباشرة عند اختيارها
      const formData = new FormData();
      formData.append("file", selectedFile);
      formData.append("file_type", "image");
      formData.append("user_id", localStorage.getItem("user_id"));
      setImageUploading(true); // ابدأ التحميل
      try {
        const res = await uploadImage.mutateAsync(formData);
        if (res?.file_url) {
          setUploadedImageUrl(res.file_url);
        } else {
          setUploadedImageUrl(null);
        }
      } catch (err) {
        setUploadedImageUrl(null);
      }
      setImageUploading(false); // انتهى التحميل
    }
  };

  const handleChooseFile = (e) => {
    e.preventDefault();
    e.stopPropagation();
    hiddenFileInput.current.click();
  };

  const createHandler = async (payload) => {
    setDisableBtn(true);

    // Save the current user type and role before making API calls
    const originalUserType = localStorage.getItem('user_type');
    const originalUserRole = localStorage.getItem('user_role');

    try {
        // استخدم رابط الصورة الجديد إذا كان موجودًا
        if (uploadedImageUrl) {
          payload.image = uploadedImageUrl;
        }

        // Combine country code and phone number for submission
        if (payload.phone && payload.countryCode) {
          payload.phone = `${payload.countryCode.code}${payload.phone.replace(/\D/g, '')}`; // Clean phone and add country code
        }

        if (actionType !== "update" && isManager) {
            payload = {
                ...payload,
                email: ``,
                password: '',
                company_name: ''
                // Don't reset user_type - let manager set it
            };
        }

        if (currentUserType !== "admin") {
            if (actionType === "update" && data) {
                payload.email = data.email || "";
                payload.password = data.password || "";
                payload.user_type = data.user_type || "";
            } else {
                delete payload.email;
                delete payload.password;
                // Don't delete user_type for manager - let them set it
            }
        }

        if (actionType !== "update") {
            delete payload.user_id;  
        }

        // For manager, handle user_type and type mapping
        if (currentUserType === "manager") {
            console.log('Manager: Processing user_type and type mapping');
            console.log('Original payload.user_type:', payload.user_type);
            console.log('Original payload.type:', payload.type);
            
            if (payload.user_type && (payload.user_type === 'employee' || payload.user_type === 'visitor')) {
                // Set type field to the selected value
                payload.type = payload.user_type;
                // Keep user_type as 'user' for the users table
                payload.user_type = 'user';
                console.log('Manager: Mapped user_type to type field:', {
                    final_user_type: payload.user_type,
                    final_type: payload.type
                });
            } else if (payload.type && (payload.type === 'employee' || payload.type === 'visitor')) {
                // If type is already set, keep user_type as 'user'
                payload.user_type = 'user';
                console.log('Manager: Type already set, keeping user_type as user:', {
                    final_user_type: payload.user_type,
                    final_type: payload.type
                });
            }
        }

        // Clean up payload based on user type
        if (currentUserType === "admin") {
            // Remove department and type fields for admin
            delete payload.department;
            delete payload.type;
            console.log('Admin: Removed department and type fields from payload');
        }
        
        // Log the final payload for debugging
        console.log('Final payload being sent:', payload);
        console.log('Current user type:', currentUserType);
        console.log('Action type:', actionType);
        console.log('user_type in payload:', payload.user_type);
        console.log('type in payload:', payload.type);
        console.log('department in payload:', payload.department);
        console.log('All payload keys:', Object.keys(payload));

        if (actionType === "update") {
            const userId = payload?.id;
            delete payload?.id;
            delete payload?.print_status;
            
            const updateData = {
                ...data,
                ...payload, 
                image: payload.image || data.image, 
            };
            
            // Clean up update data based on user type
            if (currentUserType === "admin") {
                delete updateData.department;
                delete updateData.type;
                console.log('Admin: Removed department and type fields from update data');
            }
            
            console.log('Update data being sent:', updateData);
            console.log('Type field in update data:', updateData.type);
            console.log('User type field in update data:', updateData.user_type);
            console.log('Department field in update data:', updateData.department);
            
            updateUser.mutate(
                { id: userId, data: updateData },
                {
                    onSuccess: () => {
                        // Ensure user_type and user_role haven't changed
                        if (localStorage.getItem('user_type') !== originalUserType) {
                            localStorage.setItem('user_type', originalUserType);
                        }
                        if (localStorage.getItem('user_role') !== originalUserRole) {
                            localStorage.setItem('user_role', originalUserRole);
                        }
                        
                        reset();
                        setDisableBtn(false);
                        
                        setImage({});
                        setUploadedImageUrl(null);
                        if (imagePreviewUrl && imagePreviewUrl !== originalImageUrl) {
                          URL.revokeObjectURL(imagePreviewUrl);
                        }
                        setImagePreviewUrl(null);
                        setOriginalImageUrl(null);
                        setHasNewImage(false);
                        
                        // Call onSuccess callback if provided
                        if (onSuccess) onSuccess();
                        
                        // Close dialog
                        setOpenDialog(prev => ({ ...prev, addMember: false }));
                    },
                    onError: (error) => {
                        console.error("Error updating user:", error);
                        setDisableBtn(false);
                        
                        // Handle server validation errors
                        if (error.response?.data?.details) {
                            const serverErrors = error.response.data.details;
                            Object.keys(serverErrors).forEach(field => {
                                const message = serverErrors[field][0];
                                setError(field, {
                                    type: 'manual',
                                    message: message,
                                });
                            });
                        }
                        
                        // Also restore user type and role on error
                        if (localStorage.getItem('user_type') !== originalUserType) {
                            localStorage.setItem('user_type', originalUserType);
                        }
                        if (localStorage.getItem('user_role') !== originalUserRole) {
                            localStorage.setItem('user_role', originalUserRole);
                        }
                    }
                }
            );
        } else {
            addUser.mutate(
                payload,
                {
                    onSuccess: () => {
                        // Ensure user_type and user_role haven't changed
                        if (localStorage.getItem('user_type') !== originalUserType) {
                            localStorage.setItem('user_type', originalUserType);
                        }
                        if (localStorage.getItem('user_role') !== originalUserRole) {
                            localStorage.setItem('user_role', originalUserRole);
                        }
                        
                        reset();
                        setDisableBtn(false);
                        
                        setImage({});
                        setUploadedImageUrl(null);
                        if (imagePreviewUrl && imagePreviewUrl !== originalImageUrl) {
                          URL.revokeObjectURL(imagePreviewUrl);
                        }
                        setImagePreviewUrl(null);
                        setOriginalImageUrl(null);
                        setHasNewImage(false);
                        
                        // Call onSuccess callback if provided
                        if (onSuccess) onSuccess();

                        // Close dialog
                        setOpenDialog(prev => ({ ...prev, addMember: false }));
                    },
                    onError: (error) => {
                        console.error("Error creating user:", error);
                        setDisableBtn(false);
                        
                        // Handle server validation errors
                        if (error.response?.data?.details) {
                            const serverErrors = error.response.data.details;
                            Object.keys(serverErrors).forEach(field => {
                                const message = serverErrors[field][0];
                                setError(field, {
                                    type: 'manual',
                                    message: message,
                                });
                            });
                        }
                        
                        // Also restore user type and role on error
                        if (localStorage.getItem('user_type') !== originalUserType) {
                            localStorage.setItem('user_type', originalUserType);
                        }
                        if (localStorage.getItem('user_role') !== originalUserRole) {
                            localStorage.setItem('user_role', originalUserRole);
                        }
                    }
                }
            );
        }
        
    } catch (error) {
        console.error("Error saving data:", error);
        setDisableBtn(false);
        
        // Also restore user type and role on error
        if (localStorage.getItem('user_type') !== originalUserType) {
            localStorage.setItem('user_type', originalUserType);
        }
        if (localStorage.getItem('user_role') !== originalUserRole) {
            localStorage.setItem('user_role', originalUserRole);
        }
    }
};

  useEffect(() => {
    if (companyUserType === "employee") {
      setValue("department", data?.department || "");
    }
  }, [companyUserType, data, setValue]);

  useEffect(() => {
    if (actionType === "update") {
      setCompanyUserType(data?.type);
      
      // For manager, if type is employee or visitor, map it to user_type for the dropdown
      const formData = { ...data };
      if (currentUserType === "manager" && data?.type && (data.type === 'employee' || data.type === 'visitor')) {
        formData.user_type = data.type;
        // Also set the type field directly
        formData.type = data.type;
      }

      // Handle phone number parsing for update
      if (data?.phone) {
        // Try to extract country code from phone number
        const phoneNumber = data.phone;
        let countryCode = { name: 'Jordan', code: '+962', flag: '🇯🇴' }; // Default
        let phoneWithoutCode = phoneNumber;

        // Check if phone starts with known country codes
        for (const country of countries) {
          if (phoneNumber.startsWith(country.code)) {
            countryCode = country;
            phoneWithoutCode = phoneNumber.replace(country.code, '');
            break;
          }
        }

        formData.countryCode = countryCode;
        formData.phone = phoneWithoutCode;
      }
      
      reset(formData);
      
      if (data?.image) {
        setOriginalImageUrl(data.image);
        setImagePreviewUrl(data.image);
        setHasNewImage(false);
        setImage({});
        setUploadedImageUrl(null); 
      } else {
        setOriginalImageUrl(null);
        setImagePreviewUrl(null);
        setHasNewImage(false);
        setImage({});
        setUploadedImageUrl(null);
      }
    } else {
      reset();
      setOriginalImageUrl(null);
      setImagePreviewUrl(null);
      setHasNewImage(false);
      setImage({});
      setUploadedImageUrl(null);
    }
  }, [actionType, data, reset, currentUserType]);

  return (
    <Dialog visible={openDialog.addMember}
        style={{
          width: isMobile ? '95vw' : '55%',
          maxHeight: '90vh'
        }}
        breakpoints={{
          '960px': '95vw',
          '641px': '95vw'
        }}
        header={`${actionType === "update" ? "Update User" : "Create User"}`}
        modal className="p-fluid"
        onHide={() => setOpenDialog(prev => ({ ...prev, addMember: false }))}
        contentStyle={{
          maxHeight: isMobile ? 'calc(90vh - 60px)' : 'auto',
          overflow: 'auto'
        }}
      >
<form onSubmit={handleSubmit(createHandler)} className="w-full flex flex-col justify-center">
  <div className={`col-full flex flex-wrap justify-start py-4 border-[gray] ${isMobile ? 'flex-col' : ''}`}>
    
    {/* Name */}
    <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
      <div className="field bg-gradient-to-br from-slate-50 to-gray-50 dark:from-slate-800 dark:to-gray-800 border border-slate-200 dark:border-slate-600 rounded-2xl p-6 shadow-sm dark:shadow-lg">
        <div className="flex items-center justify-between mb-3">
          <label className="text-sm font-semibold text-slate-700 dark:text-slate-200 flex items-center gap-2">
            <i className="pi pi-user text-slate-600 dark:text-slate-300"></i>
            Name <span style={{ color: 'red' }}>*</span>
          </label>
        </div>
        <Controller name="name" control={control}
          rules={{ 
            required: actionType === "update" ? false : 'Name is required.',
            minLength: {
              value: 3,
              message: 'Please enter a name with at least 3 characters.'
            },
            maxLength: {
              value: 32,
              message: 'Name cannot exceed 32 characters.'
            }
          }}
          render={({ field, fieldState }) => (
            <InputText
              id={field.name}
              {...field}
              ref={field.ref}
              className={`w-full p-inputtext-sm p-4 rounded-xl border-2 focus:border-slate-500 focus:ring-2 focus:ring-slate-200 transition-all duration-200 ${fieldState.invalid ? 'border-red-500 bg-red-50' : 'border-slate-300 bg-white'}`}
              placeholder="Enter name"
              maxLength={32}
            />
          )} />
        {getFormErrorMessage('name', errors)}
      </div>
    </div>





        {/* Phone */}
        <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
      <div className="field bg-gradient-to-br from-slate-50 to-gray-50 dark:from-slate-800 dark:to-gray-800 border border-slate-200 dark:border-slate-600 rounded-2xl p-6 shadow-sm dark:shadow-lg">
        <div className="flex items-center justify-between mb-3">
          <label className="text-sm font-semibold text-slate-700 dark:text-slate-200 flex items-center gap-2">
            <i className="pi pi-phone text-slate-600 dark:text-slate-300"></i>
            Phone <span style={{ color: 'red' }}>*</span>
          </label>
        </div>
        <Controller name="phone" control={control}
          rules={{ 
            required: actionType === "update" ? false : 'Phone is required.',
            validate: (value) => {
              const countryCode = selectedCountry?.code || '+962';
              if (!validatePhoneNumber(value, countryCode)) {
                return 'Invalid phone number format for selected country';
              }
              return true;
            }
          }}
          render={({ field, fieldState }) => (
            <div className="flex items-center gap-2">
              <Controller
                name="countryCode"
                control={control}
                rules={{ required: 'Please select a country' }}
                render={({ field: countryField, fieldState: countryFieldState }) => (
                  <Dropdown
                    id={countryField.name}
                    value={countryField.value}
                    onChange={(e) => {
                      countryField.onChange(e.value);
                      setValue('phone', '', { shouldValidate: true }); // Clear phone number when country changes
                    }}
                    options={countries}
                    optionLabel="name"
                    placeholder="Select"
                    itemTemplate={countryTemplate}
                    valueTemplate={selectedCountryTemplate}
                    className={`${countryFieldState.invalid ? 'p-invalid' : ''}`}
                    style={{ minWidth: '140px', maxWidth: '160px' }}
                    filter
                    filterBy="name,code"
                    showClear={false}
                  />
                )}
              />
              <InputText
                id={field.name}
                value={field.value || ''}
                onChange={(e) => {
                  const countryCode = selectedCountry?.code || '+962';
                  const formatted = formatPhoneNumber(e.target.value, countryCode);
                  field.onChange(formatted);
                }}
                onBlur={field.onBlur}
                className={`flex-1 p-inputtext-sm p-4 rounded-xl border-2 focus:border-slate-500 focus:ring-2 focus:ring-slate-200 transition-all duration-200 ${fieldState.invalid ? 'border-red-500 bg-red-50' : 'border-slate-300 bg-white'}`}
                placeholder="Enter phone number"
              />
            </div>
          )} />
        {getFormErrorMessage('countryCode', errors)}
        {getFormErrorMessage('phone', errors)}
        
        {/* Helper Text */}
        <small className="text-slate-500 dark:text-slate-400 mt-1 block">
          Enter phone number without country code
        </small>
      </div>
    </div>



    {/* Company Name */}
    {currentUserType === "admin" &&
      <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
        <div className="field bg-gradient-to-br from-slate-50 to-gray-50 dark:from-slate-800 dark:to-gray-800 border border-slate-200 dark:border-slate-600 rounded-2xl p-6 shadow-sm dark:shadow-lg">
          <div className="flex items-center justify-between mb-3">
            <label className="text-sm font-semibold text-slate-700 dark:text-slate-200 flex items-center gap-2">
              <i className="pi pi-building text-slate-600 dark:text-slate-300"></i>
              Company Name <span style={{ color: 'red' }}>*</span>
            </label>
          </div>
          <Controller name="company_name" control={control}
            rules={{ 
              required: actionType === "update" ? false : "Company name is required!",
              minLength: {
                value: 3,
                message: 'Please enter a company name with at least 3 characters.'
              },
              maxLength: {
                value: 32,
                message: 'Company name cannot exceed 32 characters.'
              }
            }}
            render={({ field, fieldState }) => (
              <InputText
                id={field.name}
                {...field}
                ref={field.ref}
                className={`w-full p-inputtext-sm p-4 rounded-xl border-2 focus:border-slate-500 focus:ring-2 focus:ring-slate-200 transition-all duration-200 ${fieldState.invalid ? 'border-red-500 bg-red-50' : 'border-slate-300 bg-white'}`}
                placeholder="Enter company name"
                maxLength={32}
              />
            )}
          />
          {getFormErrorMessage('company_name', errors)}
        </div>
      </div>
    }

            {/* Position */}
    <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
      <div className="field bg-gradient-to-br from-slate-50 to-gray-50 dark:from-slate-800 dark:to-gray-800 border border-slate-200 dark:border-slate-600 rounded-2xl p-6 shadow-sm dark:shadow-lg">
        <div className="flex items-center justify-between mb-3">
          <label className="text-sm font-semibold text-slate-700 dark:text-slate-200 flex items-center gap-2">
            <i className="pi pi-briefcase text-slate-600 dark:text-slate-300"></i>
            Position <span style={{ color: 'red' }}>*</span>
          </label>
        </div>
        <Controller name="position" control={control}
          rules={{ 
            required: actionType === "update" ? false : 'Position is required.',
            minLength: {
              value: 2,
              message: 'Please enter a position with at least 2 characters.'
            },
            maxLength: {
              value: 32,
              message: 'Position cannot exceed 32 characters.'
            }
          }}
          render={({ field, fieldState }) => (
            <InputText
              id={field.name}
              {...field}
              ref={field.ref}
              className={`w-full p-inputtext-sm p-4 rounded-xl border-2 focus:border-slate-500 focus:ring-2 focus:ring-slate-200 transition-all duration-200 ${fieldState.invalid ? 'border-red-500 bg-red-50' : 'border-slate-300 bg-white'}`}
              placeholder="Enter position"
              maxLength={32}
            />
          )} />
        {getFormErrorMessage('position', errors)}
      </div>
    </div>

 

     {/* Email */}
     {currentUserType === "admin" && (
      <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
        <div className="field bg-gradient-to-br from-slate-50 to-gray-50 dark:from-slate-800 dark:to-gray-800 border border-slate-200 dark:border-slate-600 rounded-2xl p-6 shadow-sm dark:shadow-lg">
          <div className="flex items-center justify-between mb-3">
            <label className="text-sm font-semibold text-slate-700 dark:text-slate-200 flex items-center gap-2">
              <i className="pi pi-envelope text-slate-600 dark:text-slate-300"></i>
              Email <span style={{ color: 'red' }}>*</span>
            </label>
          </div>
          <Controller name="email" control={control}
            rules={{
              required: currentUserType === "admin" && actionType === "create" ? "Email is required!" : false,
              pattern: {
                value: /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
                message: "Invalid email!",
              },
              maxLength: {
                value: 40,
                message: 'Email cannot exceed 40 characters.'
              }
            }}
            render={({ field, fieldState }) => (
              <InputText
                id={field.name}
                {...field}
                ref={field.ref}
                className={`w-full p-inputtext-sm p-4 rounded-xl border-2 focus:border-slate-500 focus:ring-2 focus:ring-slate-200 transition-all duration-200 ${fieldState.invalid ? 'border-red-500 bg-red-50' : 'border-slate-300 bg-white'}`}
                placeholder="Enter email address"
                maxLength={40}
              />
            )}
          />
          {getFormErrorMessage('email', errors)}
        </div>
      </div>
    )}

    {/* Password */}
    {currentUserType === "admin" && (
      <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
        <div className="field bg-gradient-to-br from-slate-50 to-gray-50 dark:from-slate-800 dark:to-gray-800 border border-slate-200 dark:border-slate-600 rounded-2xl p-6 shadow-sm dark:shadow-lg">
          <div className="flex items-center justify-between mb-3">
            <label className="text-sm font-semibold text-slate-700 dark:text-slate-200 flex items-center gap-2">
              <i className="pi pi-lock text-slate-600 dark:text-slate-300"></i>
              Password {actionType === "create" && <span style={{ color: 'red' }}>*</span>}
            </label>
          </div>
          <Controller name="password" control={control}
            rules={{ 
              required: currentUserType === "admin" && actionType === "create" ? "Password is required" : false,
              minLength: {
                value: 6,
                message: 'Password must be at least 6 characters long.'
              }
            }}
            render={({ field, fieldState }) => (
              <Password
                id={field.name}
                {...field}
                ref={field.ref}
                className={`w-full ${fieldState.invalid ? 'p-invalid' : ''}`}
                toggleMask
                placeholder="Enter password"
                feedback={false}
              />
            )}
          />
          {getFormErrorMessage('password', errors)}
        </div>
      </div>
    )}

    {/* Department - Only for Manager */}
    {currentUserType === "manager" && (
      <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
        <div className="field bg-gradient-to-br from-slate-50 to-gray-50 dark:from-slate-800 dark:to-gray-800 border border-slate-200 dark:border-slate-600 rounded-2xl p-6 shadow-sm dark:shadow-lg">
          <div className="flex items-center justify-between mb-3">
            <label className="text-sm font-semibold text-slate-700 dark:text-slate-200 flex items-center gap-2">
              <i className="pi pi-building text-slate-600 dark:text-slate-300"></i>
              Department <span style={{ color: 'red' }}>*</span>
            </label>
          </div>
          <Controller name="department" control={control}
            rules={{
              required: actionType === "create" ? 'Department is required.' : false,
              maxLength: {
                value: 32,
                message: 'Department cannot exceed 32 characters.'
              }
            }}
            render={({ field, fieldState }) => (
              <InputText
                id={field.name}
                {...field}
                ref={field.ref}
                className={`w-full p-inputtext-sm p-4 rounded-xl border-2 focus:border-slate-500 focus:ring-2 focus:ring-slate-200 transition-all duration-200 ${fieldState.invalid ? 'border-red-500 bg-red-50' : 'border-slate-300 bg-white'}`}
                placeholder="Enter department"
                maxLength={32}
              />
            )} />
          {getFormErrorMessage('department', errors)}
        </div>
      </div>
    )}

    {/* User Type */}
    <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
      <div className="field bg-gradient-to-br from-slate-50 to-gray-50 dark:from-slate-800 dark:to-gray-800 border border-slate-200 dark:border-slate-600 rounded-2xl p-6 shadow-sm dark:shadow-lg">
        <div className="flex items-center justify-between mb-3">
          <label className="text-sm font-semibold text-slate-700 dark:text-slate-200 flex items-center gap-2">
            <i className="pi pi-users text-slate-600 dark:text-slate-300"></i>
            User Type <span style={{ color: 'red' }}>*</span>
          </label>
        </div>
        <Controller name="user_type" control={control}
          rules={{ required: actionType === "create" ? 'User Type is required.' : false }}
          render={({ field, fieldState }) => (
            <Dropdown
              id={field.name} {...field}
              value={field.value}
              options={currentUserType === "admin" ? userTypeOptions : [
                { label: 'Employee', value: 'employee' },
                { label: 'Visitor', value: 'visitor' }
              ]}
              onChange={(e) => { field.onChange(e.value); }}
              optionLabel="label"
              optionValue="value"
              ref={field.ref}
              placeholder="Select user type..."
              className={`w-full ${fieldState.invalid ? 'p-invalid' : ''}`}
              panelClassName="p-dropdown-panel-sm"
            />
          )}
        />
        {getFormErrorMessage('user_type', errors)}
      </div>
    </div>

    {/* Hidden Type field for manager */}
    {currentUserType === "manager" && (
      <Controller name="type" control={control} render={({ field }) => (
        <input type="hidden" {...field} />
      )} />
    )}

    {/* Custom Fields */}
    <CustomFields
      control={control}
      errors={errors}
      setValue={setValue}
      companyUserType={companyUserType}
      setCompanyUserType={setCompanyUserType}
    />

    <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}></div>
        {/* Image Input */}
    <div className={`${isMobile ? 'w-full' : 'w-11/12'} mb-3 px-2 ml-10`}>
      <label htmlFor="image" className="form-label text-sm mb-2 block font-medium text-gray-700 dark:text-slate-200">
        Profile Image
      </label>
      <Controller
        name="image"
        control={control}
        rules={{ required: false }}
        render={() => (
          <div className="image-upload-container bg-white dark:bg-slate-800 rounded-xl shadow-sm dark:shadow-lg">
            <input
              type="file"
              ref={hiddenFileInput}
              onChange={handleFileInputChange}
              accept="image/*"
              style={{ display: 'none' }}
            />
            <div 
              className="flex flex-col items-center justify-center p-8 transition-all duration-300 ease-in-out border-2 border-dashed border-blue-200 dark:border-blue-400 hover:border-blue-400 dark:hover:border-blue-300 rounded-xl bg-gray-50 dark:bg-slate-700 hover:bg-blue-50 dark:hover:bg-slate-600 cursor-pointer"
              onClick={handleChooseFile}
            >

              {imageUploading && (
                <div className="flex flex-col items-center justify-center mb-4">
                  <i className="pi pi-spin pi-spinner text-3xl text-blue-500"></i>
                  <span className="mt-2 text-blue-500">Uploading image...</span>
                </div>
              )}

              {!imagePreviewUrl ? (
                <>
                  <div className="mb-4 p-4 rounded-full bg-blue-100 dark:bg-blue-900">
                    <i className="pi pi-image text-4xl text-blue-500 dark:text-blue-400"></i>
                  </div>
                  <span className="text-gray-700 dark:text-slate-200 font-medium mb-2">
                    Drag and drop your image here
                  </span>
                  <span className="text-sm text-gray-500 dark:text-slate-400 text-center">
                    or click to browse from your computer
                  </span>
                  <div className="mt-4 text-xs text-gray-400 dark:text-slate-500 flex items-center gap-2">
                    <i className="pi pi-info-circle"></i>
                    <span>Supported formats: JPG, PNG, GIF (Max: 2MB)</span>
                  </div>
                </>
              ) : (
                <div className="flex flex-col items-center p-4 w-full">
                  <div className="relative group mb-4">
                    <img
                      src={imagePreviewUrl}
                      alt="Preview"
                      className="w-40 h-40 object-cover rounded-lg shadow-md transition-transform duration-300 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg flex items-center justify-center">
                      <div className="flex gap-3">
                        <Button
                          type="button"
                          icon="pi pi-times"
                          className="p-button-rounded p-button-danger p-button-sm !w-12 !h-12"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            onClearImage();
                          }}
                          tooltip="Remove image"
                        />
                        <Button
                          type="button"
                          icon="pi pi-upload"
                          className="p-button-rounded p-button-info p-button-sm !w-12 !h-12"
                          onClick={handleChooseFile}
                          tooltip="Change image"
                        />
                      </div>
                    </div>
                  </div>
                  <div className="text-center">
                    <h4 className="text-sm font-medium text-gray-700 dark:text-slate-200 mb-1">Current Image</h4>
                    <p className="text-xs text-gray-500 dark:text-slate-400">Hover to edit or remove</p>
                  </div>
                </div>
              )}
            </div>
            {hasNewImage && image.name && (
              <div className="mt-3 flex items-center gap-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-100 dark:border-green-800">
                <i className="pi pi-check-circle text-green-500 dark:text-green-400"></i>
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-green-700 dark:text-green-300">Image Selected</span>
                  <span className="text-xs text-green-600 dark:text-green-400">{image.name}</span>
                </div>
              </div>
            )}
          </div>
        )}
      />
    </div>

    {/* Buttons */}
    <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
      <Button label="Cancel"
        className="w-full"
        onClick={() => setOpenDialog(prev => ({ ...prev, addMember: false }))}
      />
    </div>
    <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
      <Button label={actionType === "update" ? "Update User" : "Create User"}
        className="w-full"
        loading={disableBtn || imageUploading}
        disabled={disableBtn || imageUploading}
        onClick={handleSubmit(createHandler)}
      />
    </div>
  </div>
</form>
</Dialog>
  );
}

AddMemberDialog.propTypes = {
  actionType: PropTypes.oneOf(['create', 'update']).isRequired,
  data: PropTypes.shape({
    id: PropTypes.string,
    name: PropTypes.string,
    email: PropTypes.string,
    password: PropTypes.string,
    phone: PropTypes.string,
    position: PropTypes.string,
    department: PropTypes.string,
    type: PropTypes.string,
    image: PropTypes.string,
    user_type: PropTypes.string,
    company_name: PropTypes.string,
    print_status: PropTypes.bool,
    countryCode: PropTypes.object,
    custom_field_1: PropTypes.string,
    custom_field_2: PropTypes.string,
    custom_field_3: PropTypes.string,
    custom_field_4: PropTypes.string,
    custom_field_5: PropTypes.string,
    custom_field_6: PropTypes.string,
    custom_field_7: PropTypes.string,
    custom_field_8: PropTypes.string,
    custom_field_9: PropTypes.string,
    custom_field_10: PropTypes.string,
  }),
  onSuccess: PropTypes.func,
};

export default AddMemberDialog;